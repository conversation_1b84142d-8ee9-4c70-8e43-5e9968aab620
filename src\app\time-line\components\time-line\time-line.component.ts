import {
  Component,
  OnInit,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AppConfig } from 'src/app/app.config';
import { Contact } from 'src/app/contact/models/contact';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { McvTimeLineEvent } from 'src/app/mcv-time-line/models/mcv-time-line-event';
import { McvTimeLineEventGroup } from 'src/app/mcv-time-line/models/mcv-time-line-event-group';
import { MeetingApiService } from 'src/app/meeting/services/meeting-api.service';
import { LeaveApiService } from 'src/app/leave-application/services/leave-api.service';
import { TimeEntryApiService } from 'src/app/services/time-entry-api.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { WFTaskApiService } from 'src/app/wf-task/services/wf-task-api.service';
import { AuthService } from 'src/app/auth/services/auth.service';
import { TimeLineGroupDialogComponent } from '../time-line-group-dialog/time-line-group-dialog.component';
import { Package } from 'src/app/package/models/package.model';
import { ApiFilter } from 'src/app/models/api-filters';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { NgIf, NgFor, CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { HeaderComponent } from '../../../mcv-header/components/header/header.component';
import { MatMenuModule } from '@angular/material/menu';
import { McvTimeLineViewComponent } from 'src/app/mcv-time-line/components/mcv-time-line-view/mcv-time-line-view.component';
import { ContactGroupService } from 'src/app/contact/services/contact-group.service';
import { debounceTime, distinctUntilChanged, firstValueFrom, merge } from 'rxjs';
import { FilterToggleDirective } from 'src/app/shared/directives/filter-toggle.directive';
import { McvFilterSidenavComponent } from "../../../mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component";
import { TimeLineService } from '../../services/time-line.service';
import { McvTimeLineService } from 'src/app/mcv-time-line/services/mcv-time-line.service';

@Component({
  selector: 'app-time-line',
  templateUrl: './time-line.component.html',
  styleUrls: ['./time-line.component.scss'],
  standalone: true,
  imports: [
    HeaderComponent,
    CommonModule,
    MatMenuModule,
    McvTimeLineViewComponent,
    FooterComponent,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    NgIf,
    MatSelectModule,
    NgFor,
    MatOptionModule,
    MatButtonModule,
    MatTooltipModule,
    FilterToggleDirective,
    McvFilterSidenavComponent
],
})
export class TimeLineComponent implements OnInit {
  selectGroup: any;
  public readonly TIMELINE_START_TIME = this.config.TIMELINE_START_TIME;
  public readonly TIMELINE_END_TIME = this.config.TIMELINE_END_TIME;
  receivedContacts: any;
  groupList: any[] = [];
  filteredContactList: any[] = [];
  partnerOptions: any[] = [];
  associateOptions: any[] = [];
  partnerFC =  new FormControl();
  associateFC =  new FormControl();
  get f(): any {
    return this.form.controls;
  }
  get currentUser() { return this.authService.currentUserStore?.contact }
  get contactOptionsFA(): FormArray {
    return this.f['contactOptionsArray'] as FormArray;
  }

  searchValue: string = '';

  packageFilter = [
    { key: 'statusFlag', value: 0 },
    { key: 'typeFlag', value: 0 },
  ];
  resources: McvTimeLineEventGroup[] = [];
  contactOptions: Contact[] = [];

  searchFC = new FormControl();
  isResizeActive: boolean = false;
  selectedEvent!: McvTimeLineEvent;
  cachedEvent!: McvTimeLineEvent;
  headerTitle!: string;
  form!: FormGroup;
  // newArrayToFilter: McvTimeLineEventGroup[] = [];
  group = new FormControl();
  filteredArray: McvTimeLineEventGroup[] = [];
  activePackages: Package[] = [];
  showBox = false;
  selectedItems: any;
  constructor(
    protected config: AppConfig,
    protected utility: UtilityService,
    protected wfTaskService: WFTaskApiService,
    protected timeEntryService: TimeEntryApiService,
    private timelineService: McvTimeLineService,
    protected meetingService: MeetingApiService,
    protected leaveService: LeaveApiService,
    protected contactService: ContactApiService,
    public dialog: MatDialog,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private contactGroupService : ContactGroupService
  ) {}

  handleContacts(contacts: any[]) {
    this.receivedContacts = contacts;
    console.log('Received contacts:', this.receivedContacts);
  }
  getPartnerList(partners: any[]) {
    this.partnerOptions = partners;
    console.log('Received partnerOptions:', this.partnerOptions);
  }
  getAssociateList(partners: any[]) {
    this.associateOptions = partners;
    console.log('Received partnerOptions:', this.partnerOptions);
  }

  ngOnInit(): void {
    if (!this.form) {
      // this.buildForm();
      this.form = this.formBuilder.group({
        searchFC: new FormControl(),
        inputLabel: new FormControl<any>(null, [Validators.required]),
        receivedContacts: [[]],
        group: new FormControl(),
      });
    }
    this.form
      .get('receivedContacts')
      ?.valueChanges.subscribe((selectedItems) => {
        this.selectedItems = selectedItems;
        console.log('this.selectedItems', this.selectedItems);
      });
    this.searchFC.valueChanges.subscribe((value) => {
      console.log(value);
      if (value) {
        this.searchValue = value; // Update the searchValue with the new value
      } else {
        this.searchValue = ''; // Reset if value is empty
      }
    });
    merge(
      this.partnerFC.valueChanges.pipe(debounceTime(400), distinctUntilChanged()),
      this.associateFC.valueChanges.pipe(debounceTime(400), distinctUntilChanged())
    ).subscribe(() => {
      let combinedValues = {
        partners: this.partnerFC.value && this.partnerFC.value.length ? this.partnerFC.value : null,
        associates: this.associateFC.value && this.associateFC.value.length ? this.associateFC.value : null
      };
      this.timelineService.setProjectAssociates(combinedValues)
  
    });
    this.getGroupList()
    this.headerTitle = 'TimeLine';

  }
  async setDefaultGroup() {
    const defaultgroup = this.groupList.find(group => group.isDefault === true)
    console.log('defaultgroup',defaultgroup);
    this.group.setValue(defaultgroup);
    // await defaultgroup.members.forEach((element:any) => {
    //   this.filteredContactList.push(element.contact) 
    // })
    setTimeout(() => {
      this.onGroupSelect(defaultgroup)
    }, 2000);

    console.log('this.filteredContactList',this.filteredContactList);
  }

  toggleBox() {
    this.showBox = !this.showBox;
  }


 async onGroupSelect(event: any) {
    console.log('event',event);
    this.filteredContactList = [];
    const selectedGroup = event;
    if(selectedGroup && selectedGroup?.members.length!=0){
      await selectedGroup.members.forEach((element:any) => {
        this.filteredContactList.push(element.contact) 
      });
    }
   
    // const selectedGroup = this.groupList.find(
    //   (group) => group.groupName === selectedGroupName
    // );

    // if (selectedGroup) {
    //   // Clear the filteredContactList before pushing new contacts
    //   this.filteredContactList = [];

    //   // Push contacts from the selected group to the filteredContactList
    //   this.filteredContactList.push(...selectedGroup.contacts);
    // }

    console.log('Filtered contacts:', this.filteredContactList);
  }
  // Method to clear the selection
  clearSelection(): void {
    if(this.receivedContacts && this.receivedContacts.length!=0){
      this.filteredContactList = this.receivedContacts;
    }
    this.filteredContactList = this.receivedContacts;
    console.log('this.filteredContactList>>',this.filteredContactList)
  }

  openGroupMenu(group: any, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    this.patchGroupForm(group);
  }

  patchGroupForm(group: any): void {
    this.form.patchValue({
      inputLabel: group.groupName, // assuming 'groupName' corresponds to 'inputLabel'
      receivedContacts: group.contacts, // 'contacts' corresponds to 'receivedContacts'
      // assuming 'group' corresponds to the 'group' field
    });

    // Store the selected contacts to display
    this.selectedItems = group.contacts;
  }

  openGroupDialog() // componentOrTemplateRef: ComponentType<unknown> | TemplateRef<unknown>,

  {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true;

    dialogConfig.width = '800px';

    dialogConfig.data = {
      // isCreateMode: true,
      dialogTitle: 'Group',
      receivedContacts: this.receivedContacts,
   
      // day: day,
      // activePackages: this.activePackages
    };
    const dialogRef = this.dialog.open(
      TimeLineGroupDialogComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe(() => {
      console.log('Dialog was closed');
      //  this.refreshTask()

      // this.doSomethingAfterDialogClose(result);

      this.getGroupList()
    });

    return dialogRef;
  }
 async getGroupList() {
  const filter : ApiFilter[] = [
    { key: 'ContactID', value: this.currentUser? this.currentUser.id.toString() : ''},
    
  ];
  this.groupList = await firstValueFrom( this.contactGroupService.get(filter))
  console.log('this.groupList',this.groupList);
  this.setDefaultGroup()
  }
}
