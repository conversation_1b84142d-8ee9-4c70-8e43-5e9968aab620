<div class="package-analysis-wrapper">
    <div class="data-filter-row" >
        <div class="filter-header" *ngIf="selectedProject.length > 0 || selectedPartner.length > 0 || selectedAssociate.length > 0 || selectedPackage.length > 0" >
           <h6 class="font-focused ">Filters:</h6> 
      </div>
          <h6 *ngIf="selectedProject.length > 0">
              <b>Project:</b> {{selectedProject.join(', ')}}
              <span class="clear-icon" (click)="clearSelection('project')">✖</span>
          </h6>
          <h6 *ngIf="selectedPartner.length > 0">
              <b>Partner:</b> {{selectedPartner.join(', ')}}
              <span class="clear-icon" (click)="clearSelection('partner')">✖</span>
          </h6>
          <h6 *ngIf="selectedAssociate.length > 0">
              <b>Associate:</b> {{selectedAssociate.join(', ')}}
              <span class="clear-icon" (click)="clearSelection('associate')">✖</span>
          </h6>
        
          <h6 *ngIf="selectedPackage.length > 0">
              <b>Package:</b> {{selectedPackage.join(', ')}}
              <span class="clear-icon" (click)="clearSelection('package')">✖</span>
          </h6>
          <h6  (click)="resetFilter()" *ngIf="selectedProject.length > 0 || selectedPartner.length > 0 || selectedAssociate.length > 0 || selectedPackage.length > 0" >
            <b> Clear All</b>
           
          </h6>
      </div>
    <div class="package-analysis-table-wrapper">
        <table>
            <thead>
                <tr>
                    <th mat-button [matMenuTriggerFor]="assignedToMenu" [ngClass]="{'filter': selectedProject.length > 0}">
                     
                        <div class="analysis-table-header">
                            <h6>Project</h6>
                            <mat-icon >filter_alt</mat-icon>
                            <mat-menu #assignedToMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input
                                        matInput
                                        placeholder="Search"
                                        [(ngModel)]="projectSearchKey"
                                        (input)="filterDistinctProject()"
                                      />
                                      <mat-icon class="clear-icon"
                                      matSuffix
                                      (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                  </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('project')">
                                    {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
                                </button>
                              <mat-option class="menu-mat-option" *ngFor="let project of filteredProject" (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                                <mat-checkbox [checked]="selectedProject.includes(project)">{{ project }}</mat-checkbox>
                              </mat-option>
                             
                            </mat-menu>
                          </div>
                          
                    </th>
                    <th mat-button [matMenuTriggerFor]="partnerMenu" [ngClass]="{'filter': selectedPartner.length > 0}">
                        
                        <div class="analysis-table-header">
                            <h6>Partner</h6>
                            <mat-icon >filter_alt</mat-icon>
                            <mat-menu #partnerMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input
                                        matInput
                                        placeholder="Search"
                                        [(ngModel)]="partnerSearchKey"
                                        (input)="filterDistinctProject()"
                                      />
                                      <mat-icon class="clear-icon"
                                      matSuffix
                                      (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                  </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('partner')">
                                    {{ isAllSelected('partner') ? 'Deselect All' : 'Select All' }}
                                </button>
                              <mat-option class="menu-mat-option" *ngFor="let partner of filteredPartner" (click)="$event.stopPropagation(); toggleSelection(partner, 'partner')">
                                <mat-checkbox [checked]="selectedPartner.includes(partner)">{{ partner }}</mat-checkbox>
                              </mat-option>
                             
                            </mat-menu>
                          </div>
                    </th>
                    <th mat-button [matMenuTriggerFor]="associateMenu" [ngClass]="{'filter': selectedAssociate.length > 0}">
                        
                       
                        <div class="analysis-table-header">
                            <h6>Associate</h6>
                            <mat-icon >filter_alt</mat-icon>
                            <mat-menu #associateMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input
                                        matInput
                                        placeholder="Search"
                                        [(ngModel)]="associateSearchKey"
                                        (input)="filterDistinctAssociate()"
                                      />
                                      <mat-icon class="clear-icon"
                                      matSuffix
                                      (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                  </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('associate')">
                                    {{ isAllSelected('associate') ? 'Deselect All' : 'Select All' }}
                                </button>
                              <mat-option class="menu-mat-option" *ngFor="let associate of filteredAssociate" (click)="$event.stopPropagation(); toggleSelection(associate, 'associate')">
                                <mat-checkbox [checked]="selectedAssociate.includes(associate)">{{ associate }}</mat-checkbox>
                              </mat-option>
                             
                            </mat-menu>
                          </div>
                    </th>
                    <th mat-button [matMenuTriggerFor]="packageMenu" [ngClass]="{'filter': selectedPackage.length > 0}">
                        
                      
                        <div class="analysis-table-header">
                            <h6>Package</h6>
                            <mat-icon >filter_alt</mat-icon>
                            <mat-menu #packageMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input
                                        matInput
                                        placeholder="Search"
                                        [(ngModel)]="packageSearchKey"
                                        (input)="filterDistinctPackage()"
                                      />
                                      <mat-icon class="clear-icon"
                                      matSuffix
                                      (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                  </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('package')">
                                    {{ isAllSelected('package') ? 'Deselect All' : 'Select All' }}
                                </button>
                              <mat-option class="menu-mat-option" *ngFor="let package of filteredPackage" (click)="$event.stopPropagation(); toggleSelection(package, 'package')">
                                <mat-checkbox [checked]="selectedPackage.includes(package)">{{ package }}</mat-checkbox>
                              </mat-option>
                             
                            </mat-menu>
                          </div>
                    
                    </th>
                   
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'startDate'}">
                        <div class="analysis-table-header" (click)="sortData('startDate')" >
                            <h6>Start</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'startDate' || !sortState['startDate']">import_export</mat-icon>
                          </div>
                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'finalDate'}">
                        <div class="analysis-table-header" (click)="sortData('finalDate')" >
                            <h6>Due</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'finalDate' && sortState['finalDate'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'finalDate' && sortState['finalDate'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'finalDate' || !sortState['finalDate']">import_export</mat-icon>
                          </div>

                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'submissionDate'}">
                        <div class="analysis-table-header" (click)="sortData('submissionDate')" >
                            <h6>Submission</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'submissionDate' && sortState['submissionDate'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'submissionDate' && sortState['submissionDate'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'submissionDate' || !sortState['submissionDate']">import_export</mat-icon>
                          </div>
                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'delay'}">
                        <div class="analysis-table-header" (click)="sortData('delay')" >
                            <h6>Delay</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'delay' && sortState['delay'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'delay' && sortState['delay'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'delay' || !sortState['delay']">import_export</mat-icon>
                          </div>
                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrAssigned'}">
                        <div class="analysis-table-header" (click)="sortData('vHrAssigned')" >
                            <h6>vHR</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrAssigned' && sortState['vHrAssigned'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrAssigned' && sortState['vHrAssigned'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'vHrAssigned' || !sortState['vHrAssigned']">import_export</mat-icon>
                          </div>
                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrConsumed'}">
                        <div class="analysis-table-header" (click)="sortData('vHrConsumed')" >
                            <h6>vHR Spent</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrConsumed' && sortState['vHrConsumed'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrConsumed' && sortState['vHrConsumed'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'vHrConsumed' || !sortState['vHrConsumed']">import_export</mat-icon>
                          </div>

                    </th>
                    <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrBalance'}">
                        <div class="analysis-table-header" (click)="sortData('vHrBalance')" >
                            <h6>vHR Earned</h6>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrBalance' && sortState['vHrBalance'] === 'newFirst'">south</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] === 'vHrBalance' && sortState['vHrBalance'] === 'oldFirst'">north</mat-icon>
                            <mat-icon *ngIf="sortState['activeColumn'] !== 'vHrBalance' || !sortState['vHrBalance']">import_export</mat-icon>
                          </div>

                    </th>
                
                </tr>
                <tr class="total">     
                  
                    <td [colSpan]="7">Total</td>

                    <td><span [ngClass]="{'text-green':total.delay<0,'text-red':total.delay>0}"
                            *ngIf="total.delay>0">+</span><span
                            [ngClass]="{'text-green':total.delay<0,'text-red':total.delay>0}">{{total.delay |
                            mcvManHour}}</span></td>
                    <td>{{total.vHrAssigned | number:'2.0-2'}}</td>
                    <td>{{total.vHrConsumed | number:'2.0-2'}}</td>
                    <td><span [ngClass]="{'text-green':total.vHrBalance>0,'text-red':total.vHrBalance<0}"
                            *ngIf="total.vHrBalance>0">+</span>
                        <span
                            [ngClass]="{'text-green':total.vHrBalance>0,'text-red':total.vHrBalance<0}">{{total.vHrBalance
                            |
                            number:'2.0-2'}}</span>
                    </td>
              
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let item of dataList; let i = index">
                    <tr>
                        <td class="td-align-left">{{item.project}}</td>
                        <td class="td-align-left">{{item.partner}}</td>
                        <td class="td-align-left">{{item.associate}}</td>
                        <td class="td-align-left text-pre-wrap">{{item.package}}</td>
                    
                        <td class="td-align-right">{{item.startDate | date:'dd MMM y HH:mm'}}</td>
                        <td class="td-align-right">{{item.finalDate | date:'dd MMM y HH:mm'}}</td>
                        <td class="td-align-right">{{item.submissionDate | date:'dd MMM y HH:mm'}}</td>
                        <td class="td-align-right">
                            <span [ngClass]="{'text-green':item.delay<0,'text-red':item.delay>0}"
                                *ngIf="item.delay>0">+</span>
                            <span [ngClass]="{'text-green':item.delay<0,'text-red':item.delay>0}">{{item.delay |
                                mcvManHour}}</span>
                        </td>
                        <td class="td-align-right">
                            <span>{{item.vHrAssigned | number:'2.0-2'}}</span>
                        </td>
                        <td class="td-align-right">
                            <span>{{item.vHrConsumed | number:'2.0-2'}}</span></td>
                        <td class="td-align-right"><span [ngClass]="{'text-green':item.vHrBalance>0,'text-red':item.vHrBalance<0}"
                                *ngIf="item.vHrBalance>0">+</span>
                            <span
                                [ngClass]="{'text-green':item.vHrBalance>0,'text-red':item.vHrBalance<0}">{{item.vHrBalance
                                |
                                number:'2.0-2'}}</span>
                        </td>
                     
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</div>
<app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
          </button>

        <!-- <div *ngIf="showAll">
            <mat-form-field appearance="outline">
                <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                    <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div> -->

        <button mat-raised-button class="btn" [color]="showAll ? 'accent' : ''"
            (click)="onToggleShowAll()" *ngIf="isPermissionSpecialShowAll" matTooltip="Show All" aria-label="Show All">
            Show All
        </button>
        <button mat-raised-button  (click)="onExportExcel()" class="btn"  matTooltip="Export Excel" aria-label="export excel"
        *ngIf="isPermissionSpecialShowAll" >
        Export Excel
        </button>
       

   
    </div>

    <div class="nav-footer-mobile-actions">
        <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
          </button>
        <button mat-raised-button class="btn" [ngClass]="{ 'btn-warning': showAll, 'btn-light': !showAll }"
            (click)="onToggleShowAll()" *ngIf="isPermissionSpecialShowAll" matTooltip="Show All" aria-label="Show All">
            Show All
        </button>

       
        <button mat-raised-button  (click)="onExportExcel()" class="btn"  matTooltip="Export Excel" aria-label="export excel"
        *ngIf="isPermissionSpecialShowAll" >
        Export Excel
        </button>
      
    </div>

    <div class="nav-filters">
        <div class="inline-list">
       
            <mat-form-field appearance="outline">
                <mat-label>Select Range</mat-label>
                <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                    <input matStartDate placeholder="Start date" formControlName="start" readonly>
                    <input matEndDate placeholder="End date" formControlName="end" readonly>
                </mat-date-range-input>
                <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
                <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
            </mat-form-field>
       
            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
       
            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
       
        </div>
    </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav >
    <div>
        <mat-form-field appearance="outline">
            <mat-label>Select Range</mat-label>
            <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                <input matStartDate placeholder="Start date" formControlName="start" readonly>
                <input matEndDate placeholder="End date" formControlName="end" readonly>
            </mat-date-range-input>
            <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
            <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
        </mat-form-field>
    </div>

    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="companyFC" placeholder="Company">
                <mat-option *ngFor="let item of companyOptions" [value]="item">
                    {{item.title }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="statusFC" multiple placeholder="Status">
                <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    
         
        </app-mcv-filter-sidenav>