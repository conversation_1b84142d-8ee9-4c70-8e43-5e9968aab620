import { BaseAttribute } from './../../../mcv-core/models/base-entity.model';
import { DesignScriptDataCardAttachment, DesignScriptDataCardAttribute, MultiValueAttribute } from './../../models/design-script-data-card.model';
import { LibraryEntityAttachment, LibraryEntityAttribute } from 'src/app/library/model/library-entity.model';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { LibraryEntity } from 'src/app/library/model/library-entity.model';
import { LibraryEntityApiService } from 'src/app/library/service/library-entity-api.service';
import { McvBaseFormComponent } from 'src/app/mcv-core/components/mcv-base-form/mcv-base-form.component';
import { McvLightBoxComponent } from 'src/app/mcv-file/components/mcv-light-box/mcv-light-box.component';
import { McvLightBoxService } from 'src/app/mcv-file/services/mcv-light-box.service';


import { DesignScriptApiService } from '../../services/design-script-api.service';
import { DesignScriptDataCard } from '../../models/design-script-data-card.model';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { DesignScriptLibraryEntityFormAttachmentComponent } from '../design-script-library-entity-form-attachment/design-script-library-entity-form-attachment.component';
import { NgFor, NgIf, NgClass } from '@angular/common';

@Component({
    selector: 'app-design-script-library-entity-view-form',
    templateUrl: './design-script-library-entity-view-form.component.html',
    styleUrls: ['./design-script-library-entity-view-form.component.scss'],
    standalone: true,
    imports: [NgFor, DesignScriptLibraryEntityFormAttachmentComponent, NgIf, MatIconModule, MatTooltipModule, NgClass, MatFormFieldModule, MatSelectModule, ReactiveFormsModule, MatOptionModule]
})
export class DesignScriptLibraryEntityViewFormComponent extends McvBaseFormComponent implements OnInit
{
  attributeFC: FormControl[] = [];
  libraryEntity = new LibraryEntity();
  entityID!: number;
  isReadOnly: boolean = false;
  isCreateMode: boolean = false;
  selectedAttachments: LibraryEntityAttachment[] = [];
  filename!: string;
  singlePreview: boolean = true;
  contentType!: string;
  mediaCaption!: string;
  dropDownOptions: MultiValueAttribute[] = [];
  dropDownSelectedValue: any[] = [];
  attributeValue: any[] = [];
  descriptionFullScreen: boolean = false;
  filterAttachments: LibraryEntityAttachment[] = [];
  hideAtt: boolean[] = [];
  filteredAttributes: DesignScriptDataCardAttribute[] = [];
  vendorDetail: boolean[] = [];

  @Input('config') set configValue(value: LibraryEntity)
  {
    this.libraryEntity = Object.assign({}, value ? value : new LibraryEntity());
    this.libraryEntity.attributes = [];
    value.attributes.forEach(att =>
    {
      this.libraryEntity.attributes.push(Object.assign({}, att))
    });
    this.filterAttachments = this.libraryEntity.attachments;
    this.refresh();
    this.attributeValues();
    this.getSeparatedValue();
    this.filteredAttributes = [];
    this.libraryEntity.attributes.forEach(att =>
    {
      //Take first option after split
      this.filteredAttributes.push(new DesignScriptDataCardAttribute({ attributeKey: att.attributeKey, attributeValue: att.attributeValue.split(";")[0] }));
    });

    //Creating a new formcontrol using map and binding the first value to it.
    this.libraryEntity.attributes.map((y, i) => { this.attributeFC[i] = new FormControl(y.attributeValue.split(';')[0]) });
  }

  @Input('isCreateMode') set mode(value: boolean)
  {
    this.isCreateMode = value;
  }

  @Output() create = new EventEmitter();
  @Output() override cancel = new EventEmitter();
  @Output() update = new EventEmitter();
  @Output() delete = new EventEmitter();
  @Output() editOption = new EventEmitter<boolean>();
  @Output() preview = new EventEmitter<any>();

  get categoryOptions() { return this.designScriptService.categoryOptions; }

  constructor(
    private dialogRef: MatDialogRef<DesignScriptLibraryEntityViewFormComponent>,
    public entityService: LibraryEntityApiService,
    private mcvLightBoxService: McvLightBoxService,
    private designScriptService: DesignScriptApiService
  )
  {
    super();
  }

  ngOnInit()
  {
    // this.buildForm();
    this.getSelectedValue();
  }

  refresh()
  {
    if (!this.libraryEntity)
    {
      this.libraryEntity = new LibraryEntity();
    }
  }

  getMasterCategory(category: string)
  {
    return this.categoryOptions.find(x => x.label == category);
  }

  fullScreenDesc()
  {
    this.descriptionFullScreen = !this.descriptionFullScreen;
  }

  onPreviewClick(e: any)
  {
    if (this.singlePreview)
    {
      const _data = {
        title: e.entityTitle ? e.entityTitle : e.filename,
        urls: null,
        activeUrl: e.url,
        mediaType: 'image',
        contentType: e.contentType,
        mediaCaption: this.mediaCaption
      };
      const ref = this.mcvLightBoxService.open(McvLightBoxComponent, {
        data: _data,
      });
      ref.afterClosed.subscribe(result =>
      {
      });
    } else
    {
      this.preview.emit({ filename: e.filename, url: e.url });
    }
  }

  onPushToEntityList()
  {
    // const existingItem = this.designScriptService.currentDesignScriptEntity.dataCards.find(x => x.libraryEntityID == this.libraryEntity.uid);
    // if(existingItem){
    //   console.log(existingItem);
    // }
    if (this.designScriptService.selectedDataCard && this.designScriptService.selectedDataCard.length > 0)
    {
      // console.log(this.selectedAttachments);
      var dataCardAttachments: DesignScriptDataCardAttachment[] = [];
      dataCardAttachments = [];
      this.designScriptService.selectedDataCard.forEach(x =>
      {
        const dataCardAttachment = new DesignScriptDataCardAttachment();
        dataCardAttachment.url = x.url;
        dataCardAttachment.filename = x.filename;
        dataCardAttachments.push(dataCardAttachment);
      });
      // console.log(dataCardAttachments);

      const dataCard = new DesignScriptDataCard();
      dataCard.uid = this.libraryEntity.uid;
      dataCard.libraryEntityID = this.libraryEntity.uid;
      dataCard.description = this.libraryEntity.description;
      dataCard.category = this.libraryEntity.category;
      dataCard.attributes = this.filteredAttributes;
      dataCard.title = this.libraryEntity.title;
      dataCard.subtitle = this.libraryEntity.subtitle;
      dataCard.libraryCode = this.libraryEntity.code;
      dataCard.isVersion = false;
      dataCard.attachments = dataCardAttachments;
      // console.log('new dataCard', dataCard);
      // if (!this.designScriptService.selectedLibraryItems.find(s => s.uid == dataCard.uid)) {
      this.designScriptService.selectedLibraryItems.push(dataCard);
      // }
      this.designScriptService.selectedDataCard = [];
    }
    this.dialogRef.close();
  }

  getSelectedItem(selectedItem: any)
  {
    if (selectedItem)
    {
      this.designScriptService.selectedDataCard.push((<LibraryEntityAttachment>selectedItem.libraryAttachment))
      // this.selectedAttachments.push((<LibraryEntityAttachment>selectedItem.libraryAttachment));
    }
  }

  getUnselectedItem(unSelectedItem: any)
  {
    this.designScriptService.selectedDataCard = this.designScriptService.selectedDataCard.filter(x => x.id != unSelectedItem.libraryAttachment.id);
    // this.selectedAttachments = this.selectedAttachments.filter(x => x.id != unSelectedItem.libraryAttachment.id);
  }

  attributeValues()
  {
    this.libraryEntity.attributes.forEach(x =>
    {
      this.attributeValue.push(x.attributeValue.split(';'));
    });
  }

  getSeparatedValue()
  {
    this.dropDownOptions = this.libraryEntity.attributes.filter(x => x.attributeValue.includes(';'))
      .map(y => { return { attributeKey: y.attributeKey, attributeValues: y.attributeValue.split(';') }; });
  }

  getDropDownOptions(attributeKey: string)
  {
    return this.dropDownOptions.find(x => x.attributeKey == attributeKey)?.attributeValues;
  }

  getSelectedValue()
  {
    this.libraryEntity.attributes.forEach((x, i) =>
    {
      this.attributeFC[i].valueChanges.pipe(
        debounceTime(400),
        distinctUntilChanged(),
      ).subscribe(value =>
      {
        //console.log(value);
        this.filteredAttributes[i].attributeValue = value;
      });
    });
  }

  hideAttributes(attributes: LibraryEntityAttribute, index: number)
  {
    this.hideAtt[index] = !this.hideAtt[index];
    let att = this.filteredAttributes.find(x => x.attributeKey == attributes.attributeKey);
    if (att)
    {
      att.isHidden = true
    }
  }

  unhideAttributes(attributes: LibraryEntityAttribute, index: number)
  {
    this.hideAtt[index] = !this.hideAtt[index];
    let att = this.filteredAttributes.find(x => x.attributeKey == attributes.attributeKey);
    if (att)
      att.isHidden = false;
  }

  onShowVendorDetails(i: number)
  {
    this.vendorDetail[i] = !this.vendorDetail[i]
  }

  mailTo(e: any)
  {
    (window as any).open(`mailto:${e}`, "_blank");
  }

  callTo(e: any)
  {
    (window as any).open(`tel:${e}`, "_blank");
  }

  onWebsiteClick(e: any)
  {
    (window as any).open(`https://${e}`, "_blank");
  }
}

