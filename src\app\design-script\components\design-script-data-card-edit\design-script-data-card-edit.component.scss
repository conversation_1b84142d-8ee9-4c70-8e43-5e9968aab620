@import "variables";
@import "theme";

.disable-div {
  pointer-events: none;
  opacity: 0.7;
}
.header-controls{
  display: flex;
  align-items: center;
  justify-content: end;
  margin: 0.5rem;
  padding: 0.5rem;
  border-radius: $border-radius;
  background-color: #ececec;

 .toggle-on{
    background-color: #fefefe;
    color: #1c1b1b;
  }
  .toggle-off{
  background-color: $primary;
  color: #fff;
  }
}
.design-script-attachment-action-buttons {
  z-index: 99;
  padding: 0.3rem;
  display: flex;
  align-content: center;
  justify-content: flex-end;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  background-color: $gray-300;
  border-radius: $border-radius;
}

// .ds-category-icon {
//     margin-left: 2rem;
// }

.design-script-category-icon {
  position: absolute;
  left: 1rem;
  top: 1rem;
}

.design-script-attachment-form-wrapper {
  display: flex;
  flex-direction: row;

  .design-script-attachment-file-slider-wrapper {
    flex-grow: 0;
    flex-shrink: 0;
    height: 75vh;
    width: 51vh;
    cursor: pointer;

    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.3rem;

      .image-wrapper {
        width: 9rem;
        height: 15rem;
        border-radius: 4px;
        box-sizing: border-box;
        background-color: $gray-400;
        padding: 0.2rem;
        cursor: pointer;
        position: relative;
        flex-grow: 1;

        &.more-then-two-images {
          flex-grow: 0;
        }

        img {
          width: 100%;
          height: 85%;
          object-fit: cover;
          border-radius: 4px;
        }

        .image-overlay {
          text-align: right;
        }

        &.isHidden {
          opacity: 0.6;
        }
      }
    }
  }

  .design-script-attachment-details {
    flex-grow: 1;
    padding: 0.5rem;
    width: 100%;

    .tag-button-wrapper {
      display: flex;
      align-items: flex-start;

      .detail-tag-wrapper {
        flex-grow: 1;

        mat-icon {
          font-size: 1.2rem;
        }
      }

      .design-script-attachment-action-buttons {
        flex-grow: 1;
      }
    }

    .detail-attribute-wrapper {
      padding-top: 0.3rem;

      .attribute-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: space-between;
        // gap: 0.5rem;
        flex-shrink: 1;
        padding-bottom: 0.5rem;
        gap: 0.3rem;

        .attribute-action-container {
          display: flex;
          align-items: center;
          border: 1px solid #dee2e6;
          border-radius: 4px;
          flex-grow: 1;

          .attribute-container {
            width: 14rem;
            padding: 0.2rem;
            flex-grow: 1;
            // word-break: break-word;
            display: flex;
            justify-content: space-between;

            .attribute-key {
              font-size: 0.8rem;
              color: $gray-700;
              // width: 4rem;
            }

            .attribute-value {
              display: flex;
              gap: 0.3rem;
              flex-wrap: wrap;
              padding-left: 1rem;

              .attribute-value-chips {
                font-weight: 500;
                background-color: $gray-300;
                padding: 0.2rem 0.4rem;
                border-radius: 16px;
              }
            }
          }
        }

        .disable-wrapper {
          opacity: 0.6;
        }
      }

      .design-script-attachment-form-table {
        position: relative;
        border: 1px solid $gray-300;
        border-collapse: collapse;

        td {
          white-space: nowrap;
          border: 1px solid $gray-300;
          padding: 3px;
          text-align: center;
        }

        td mat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.1rem;
          cursor: pointer;
        }

        th {
          white-space: pre-wrap;
          border: 1px solid $gray-300;
          padding: 3px;
          text-align: center;

          top: -1px;
          background-color: $gray-100;
          // min-width: 150px;

          // &.width-auto{
          //   // min-width: 50px;
          // }

          &:first-of-type {
            left: 0;
            z-index: 3;
          }
        }

        tr.sticky-row {
          th {
            position: sticky;
            position: -webkit-sticky;

            z-index: 2;
          }

          th:first-of-type {
            z-index: 3;
          }
        }

        .vertical-header {
          text-align: center;
          white-space: nowrap;
          transform-origin: center;
          transform: rotate(-90deg);
        }

        .vertical-header p {
          margin: 0 -100%;
          display: inline-block;
        }

        .vertical-header p:before {
          content: "";
          width: 0;
          padding-top: 110%;
          /* takes width as reference, + 10% for faking some extra padding */
          display: inline-block;
          vertical-align: middle;
        }

        tbody tr td:first-of-type {
          background-color: $white;
          position: sticky;
          position: -webkit-sticky;

          left: -1px;
          text-align: center;
        }

        .design-script-attachment-form-cell-td {
          // vertical-align: top;
        }

        .design-script-attachment-form-cell-content {
          display: flex;
          align-items: center;

          .design-script-attachment-form-cell-input,
          .design-script-attachment-form-cell-value {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            text-align: left;
            max-width: 100%;
            margin: 0;
            border: none;
            padding: 3px;
          }

          .design-script-attachment-form-cell-input,
          .design-script-attachment-form-cell-value {
            flex-grow: 1;
            flex-shrink: 1;
            display: flex;
            justify-content: center;

            input,
            textarea{
              border: none;
            }

            textarea{
              overflow: hidden;
            }
          }

          .design-script-attachment-form-cell-input-prefix,
          .design-script-attachment-form-cell-input-suffix {
            flex-grow: 0;
            flex-shrink: 0;
          }
        }

        &.active {
          // border-bottom: 1px solid mat-color($mcv-primary,400);
          background-color: $gray-200;
        }
      }
    }

    .detail-description-wrapper {
      margin-top: 0.8rem;
      border: 1px solid lightgray;
      border-radius: $border-radius;
      margin-bottom: 0.8rem;
      padding: 0.3rem;

      mat-icon {
        font-size: 1.2rem;
      }
    }
  }

  .design-script-edit-details {
    // border: 1px solid $gray-300;
    // border-radius: $border-radius;
    // padding: 0.5rem;
  }
}

@media only screen and (max-width: 768px) {
  .ds-category-icon {
    margin-left: 0;
  }

  .design-script-attachment-form-wrapper {
    flex-direction: column;
    align-items: center;
  }

  .design-script-attachment-file-slider-wrapper {
    height: fit-content !important;
    width: calc(60vh * 0.6) !important;
    margin: 0.3rem;
  }
}
