import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { AppPermissions } from 'src/app/app.permissions';
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { AuthService } from 'src/app/auth/services/auth.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { WFTaskApiService } from 'src/app/wf-task/services/wf-task-api.service';
import { VHr } from '../../models/vhr-analysis.model';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { VhrAnalysisListItemComponent } from '../vhr-analysis-list-item/vhr-analysis-list-item.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
    selector: 'mcv-vhr-analysis',
    templateUrl: './vhr-analysis.component.html',
    styleUrls: ['./vhr-analysis.component.scss'],
    standalone: true,
    imports: [ MatIconModule,CommonModule,McvFilterSidenavComponent, MatOptionModule,MatCheckboxModule, MatMenuModule,FormsModule, VhrAnalysisListItemComponent, FooterComponent, MatFormFieldModule, MatDatepickerModule, ReactiveFormsModule, MatInputModule, MatButtonModule, MatTooltipModule, FilterToggleDirective,]
})
export class VhrAnalysisComponent implements OnInit {
  pagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 50,
    filters: [
      { key: 'rangeStart', value: this.utility.getMonthStart().toISOString() },
      { key: 'rangeEnd', value: this.utility.getMonthEnd().toISOString() },
      // { key: 'contactID', value: '20404' },
    ],
    searchKey: null,
    sort: '',
    route: '',
    showAll: false,
    showAssigned: false,
    groupBy: [], keyPropertyName: ''
  });

  @Input('config') set configValue(value: PagedListConfig) {
    if (value) {
      this.pagedListConfig = value;
    }
  }

  dataList: VHr[] = [];
  total!: VHr;
  isLoading: boolean = false;
  totalRecordsCount = 0;
  totalPages = 0;
  // pageSize = 50;
  currentPage = 0;
  isMobileView: boolean = false;
  dateFilters!: FormGroup;
  searchFilter!: FormControl;
  isExpanded: boolean[] = [];

  personKeySearch : string = '';
  filteredPerson: string[] = [];
  selectedPerson: string[] = [];
  distinctPerson: string[] = [];
  originalDataList: any[] = [];
  companyKeySearch : string = '';
  filteredCompany: string[] = [];
  selectedCompany: string[] = [];
  distinctCompany: string[] = [];
  renumerationKeySearch : string = '';
  filteredRenumerationType: string[] = [];
  selectedRenumerationType: string[] = [];
  distinctRenumerationType: string[] = [];
  isSorted: boolean = false;
  sortState: { 
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    currentManValue: '',
    vHrDifferencePercentage: '',
    expectedVHr: '',
    expectedRemuneration: '',
    mHr: '',
    vHr: '',
    remuneration: '',
  };
  @Output() listLoad = new EventEmitter<any>();

  constructor(
    private authService: AuthService,
    private service: WFTaskApiService,
    private utility: UtilityService,
    private formBuilder: FormBuilder,
    private permissions: AppPermissions
  ) { }

  ngOnInit() {

    this.buildForm()
    this.isMobileView = this.utility.isMobileView;
    this.refresh();
  }

  buildForm() {
    this.dateFilters = this.formBuilder.group({
      start: new FormControl<any>(null, { validators: [Validators.required] }),
      end: new FormControl<any>(null, { validators: [Validators.required] }),
    });
    this.dateFilters.controls['start'].setValue(this.utility.getMonthStart(), { emitEvent: false });

    this.dateFilters.controls['end'].setValue(this.utility.getMonthEnd(), { emitEvent: false });


    this.dateFilters.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value && value.start && value.end) {
          if (value.start.getTime() < value.end.getTime()) {
            this.pagedListConfig.filters = this.pagedListConfig.filters.filter(x => x.key !== 'rangeStart' && x.key != 'rangeEnd');
            this.pagedListConfig.filters.push({ key: 'rangeStart', value: value.start.toISOString() });
            this.pagedListConfig.filters.push({ key: 'rangeEnd', value: value.end.toISOString() });

            this.search();
          }
        }
      });
    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value) {
          this.pagedListConfig.searchKey = value;
          this.search();
        }
      })
  }

  getDataList(currentPage: any, pageSize: any) {

    this.isLoading = true;
    this.service.getVHrAnalysis(this.pagedListConfig.filters, this.pagedListConfig.searchKey,
      this.pagedListConfig.sort, this.pagedListConfig.showAll)
      .subscribe(
        data => {
          // this.totalRecordsCount = data.total;
          // this.totalPages = data.pages;
          // this.listLoad.emit({ totalRecordsCount: this.totalRecordsCount });
          this.dataList = data;// this.utility.updatePagedList<WFTaskVHrAnalysisDto>(data.list, this.dataList, 'contactID');
          this.isLoading = false;

          this.calculateTotal();
          this.extractDistinctValues();
          this.originalDataList = [...this.dataList];
        },
        error => {
          this.isLoading = false;
        }
      );
  }

  calculateTotal() {
    this.total = new VHr();
    this.dataList.forEach(x => {

      this.total.expectedVHr += x.expectedVHr;
      this.total.expectedRemuneration += x.expectedRemuneration;

      this.total.mHr += x.mHr;
      this.total.vHr += x.vHr;
      this.total.remuneration += x.remuneration;

    });
    this.total.vHrDifferencePercentage = -(100 - (this.total.vHr / this.total.expectedVHr * 100.0));
  }

  loadMoreRecords() {
    if (this.currentPage * this.pagedListConfig.pageSize < this.totalRecordsCount) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
    }
  }

  search() {
    this.currentPage = 0;
    this.dataList = [];
    this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
  }
  refresh() {
    this.pagedListConfig.searchKey = null;
    this.searchFilter.setValue(null);
    this.dataList = [];
    this.getDataList(0, (this.currentPage + 1) * this.pagedListConfig.pageSize);

  }

  onPeriodSelection(event: any) {

  }

  onExportExcel() {
    this.service.exportVHrAnalysisExcel(this.pagedListConfig.filters, this.pagedListConfig.searchKey,
      this.pagedListConfig.sort);
  }

  startMonthHandler(normalizedMonth: Date, formControlName: string, datepicker: any) {
    this.dateFilters.controls[formControlName].setValue(this.utility.getMonthStart(normalizedMonth));
    datepicker.close();
  }

  endMonthHandler(normalizedMonth: Date, formControlName: string, datepicker: any) {
    this.dateFilters.controls[formControlName].setValue(this.utility.getMonthEnd(normalizedMonth));
    datepicker.close();
  }

  getDifferenceColor(item: VHr) {
    if (item) {
      const _today = new Date();
      const _endOfMonth = this.utility.getMonthEnd(_today);
      const _diffD = (_endOfMonth.getDay() - _today.getDay()) / 30 * 100;

      const _diffR = (item.expectedRemuneration - item.remuneration) / item.expectedRemuneration * 100;

      if (_diffR - _diffD >= 20) {
        return 'red'
      } else if ((_diffR - _diffD) > 0 && (_diffR - _diffD) < 20) {
        return 'yellow'
      } else if ((_diffR - _diffD) <= 0) {
        return 'green'
      }
    }
    return 'black';
  }

  toggleExpand(i: number) {
    this.isExpanded[i] = !this.isExpanded[i];
  }

  extractDistinctValues() {
    this.distinctPerson = [...new Set(this.dataList.map(item => item.person))];
    this.distinctCompany = [...new Set(this.dataList.map(item => item.company))];
    this.distinctRenumerationType = [...new Set(this.dataList.map(item => item.renumerationType))];

    this.filteredPerson = [...this.distinctPerson]; 
    this.filteredCompany = [...this.distinctCompany]; 
    this.filteredRenumerationType = [...this.distinctRenumerationType];
  }

  filterDistinctPerson() {
    const searchLower = this.personKeySearch.toLowerCase();
    this.filteredPerson = this.distinctPerson.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }
  filterDistinctCompany() {
    const searchLower = this.companyKeySearch.toLowerCase();
    this.filteredCompany = this.distinctCompany.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }
  filterDistinctRenumerationType() {
    const searchLower = this.renumerationKeySearch.toLowerCase();
    this.filteredRenumerationType = this.distinctRenumerationType.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: Event) {
    this.personKeySearch = '';
    this.companyKeySearch = '';
    this.renumerationKeySearch = '';
    this.filterDistinctRenumerationType(); // Reset options
    this.filterDistinctCompany(); // Reset options
    this.filterDistinctPerson(); // Reset options

  }

  isAllSelected(type: string) {
    if (type === 'person') {
      return this.selectedPerson.length === this.filteredPerson.length;
    }
    if (type === 'company') {
      return this.selectedCompany.length === this.filteredCompany.length;
    }
    if (type === 'renumerationType') {
      return this.selectedRenumerationType.length === this.filteredRenumerationType.length;
    }
    return false;
  }

  toggleSelectAll(type: string) {
    if (type === 'person') {
      this.selectedPerson = this.isAllSelected(type) ? [] : [...this.filteredPerson];
    }
    if (type === 'company') {
      this.selectedCompany = this.isAllSelected(type) ? [] : [...this.filteredCompany];
    }
    if (type === 'renumerationType') {
      this.selectedRenumerationType = this.isAllSelected(type) ? [] : [...this.filteredRenumerationType];
    }
    this.applyFilters();
  }

  toggleSelection(value: string, type: string) {
    if (type === 'person') {
      this.selectedPerson.includes(value)
        ? this.selectedPerson.splice(this.selectedPerson.indexOf(value), 1)
        : this.selectedPerson.push(value);
    }
    if (type === 'company') {
      this.selectedCompany.includes(value)
        ? this.selectedCompany.splice(this.selectedCompany.indexOf(value), 1)
        : this.selectedCompany.push(value);
    }
    if (type === 'renumerationType') {
      this.selectedRenumerationType.includes(value)
        ? this.selectedRenumerationType.splice(this.selectedRenumerationType.indexOf(value), 1)
        : this.selectedRenumerationType.push(value);
    }
    this.applyFilters();
  }

  applyFilters() {
   
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if(this.pagedListConfig.searchKey){

      const searchLower = this.pagedListConfig.searchKey.toLowerCase();
  
     filteredList = filteredList.filter(item =>
      
        (item.person?.toLowerCase().includes(searchLower) || '') ||
        (item.company?.toLowerCase().includes(searchLower) || '') ||
        (item.renumerationType?.toLowerCase().includes(searchLower) || '')
      );
    }
  
    if (this.selectedPerson.length > 0) {
      filteredList = filteredList.filter(item => this.selectedPerson.includes(item.person));
    }
    if (this.selectedCompany.length > 0) {
      filteredList = filteredList.filter(item => this.selectedCompany.includes(item.company));
    }
    if (this.selectedRenumerationType.length > 0) {
      filteredList = filteredList.filter(item => this.selectedRenumerationType.includes(item.renumerationType));
    }
   

    
    this.dataList = filteredList;
    this.calculateTotal();
  }

 // Reset all filters
 resetFilter() {
  this.selectedPerson = [];
  this.selectedCompany = [];
  this.selectedRenumerationType = [];
  this.dataList = [...this.originalDataList]; // Restore full list
  this.calculateTotal();
}



clearSelection(type: string) {
  if (type === 'person') {
      this.selectedPerson = [];
  }  
    else if (type === 'company') {
      this.selectedCompany = [];
  } 
  else if (type === 'renumerationType') {
      this.selectedRenumerationType = [];
  }
  this.applyFilters(); // Apply filters after clearing the selection
}


sortData(column: keyof VHr | '') {
  if (column === '') {
    // Reset to default (original data) but apply the filters again
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
        (this.selectedPerson.length === 0 || this.selectedPerson.includes(item.person)) && 
        (this.selectedCompany.length === 0 || this.selectedCompany.includes(item.company)) &&
        (this.selectedRenumerationType.length === 0 || this.selectedRenumerationType.includes(item.renumerationType))
      );
    });

    // Reset the sort state to default
    this.sortState = { 
      activeColumn: '',
      currentManValue: '',
      vHrDifferencePercentage: '',
      expectedVHr: '',
      expectedRemuneration: '',
      mHr: '',
      vHr: '',
      remuneration: '',
    };
    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if no sorting is selected
  }

  // If the clicked column is already the active column, cycle through the three states
  const currentSort = this.sortState[column];

  if (currentSort === 'newFirst') {
    // If it's 'newFirst', change to 'oldFirst'
    this.sortState[column] = 'oldFirst';
  } else if (currentSort === 'oldFirst') {
    // If it's 'oldFirst', reset to default (no sorting)
    this.sortState[column] = '';
    this.sortState.activeColumn = '';
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
        (this.selectedPerson.length === 0 || this.selectedPerson.includes(item.person))  
      );
    });

    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if reset is selected
  } else {
    // If no sorting is applied, set it to 'newFirst' (ascending order)
    this.sortState[column] = 'newFirst';
  }

  // Set the active column
  this.sortState['activeColumn'] = column;

  // Reset other columns' sort state to '' (no sort)
  for (let key in this.sortState) {
    if (key !== column && key !== 'activeColumn') {
      this.sortState[key] = ''; // Reset other columns' sort state to no sort
    }
  }

  // Sorting logic: Compare dates for the active column
  const sortedData = [...this.dataList].sort((a, b) => {
    const dateA = new Date(a[column] as string);
    const dateB = new Date(b[column] as string);

    if (this.sortState[column] === 'newFirst') {
      return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
    } else if (this.sortState[column] === 'oldFirst') {
      return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
    }
    return 0; // If no sorting, return unchanged order
  });

  // Update the dataList with the sorted data
  this.dataList = sortedData;
  this.isSorted = true; // Set isSorted to true when sorting is applied
}



}
