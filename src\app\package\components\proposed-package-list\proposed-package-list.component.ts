import { Component, OnInit } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuthService } from 'src/app/auth/services/auth.service';
import { PackageApiService } from '../../services/package-api.service';
import { TypeMasterService } from 'src/app/services/type-master.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';

import { debounceTime, distinctUntilChanged, first } from 'rxjs/operators';

import { Contact } from 'src/app/contact/models/contact';
import { AppConfig } from 'src/app/app.config';
import { TypeMaster } from 'src/app/models/type-master-dto';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { PackageAnalysisDto } from '../../models/package-analysis-dto';
import { ApiFilter } from 'src/app/models/api-filters';
import { firstValueFrom } from 'rxjs';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
  selector: 'app-proposed-package-list',
  templateUrl: './proposed-package-list.component.html',
  styleUrls: ['./proposed-package-list.component.scss'],
  standalone: true,
  imports: [
    MatIconModule,
    MatMenuModule,
    MatCheckboxModule,
    MatOptionModule,
    FormsModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatTooltipModule,
    FooterComponent,
    McvFilterSidenavComponent,
  ],
})
export class ProposedPackageListComponent implements OnInit {
  searchKey!: string;
  isSorted: boolean = false;
  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();
  typeFC = new FormControl();

  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];
  typeOptions: TypeMaster[] = [];
  projectSearchKey: string = '';
  partnerSearchKey: string = '';
  packageSearchKey: string = '';
  statusSearchKey: string = '';
  distinctProject: string[] = [];
  distinctPartner: string[] = [];
  distinctPackage: string[] = [];
  distinctStatus: string[] = [];
  filteredProject: string[] = [];
  filteredPartner: string[] = [];
  filteredPackage: string[] = [];
  filteredStatus: string[] = [];
  selectedProject: string[] = [];
  selectedPartner: string[] = [];
  selectedPackage: string[] = [];
  selectedStatus: string[] = [];

  partnerFilter = [
    { key: 'usersOnly', value: 'true' },
    { key: 'projectPartnersOnly', value: 'true' },
  ];

  filters: ApiFilter[] = [
    {
      key: 'projectstatusFlag',
      value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString(),
    },
    // { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_ONHOLD },
    {
      key: 'projectstatusFlag',
      value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString(),
    },
    {
      key: 'projectstatusFlag',
      value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString(),
    },
    { key: 'companyID', value: '1' },
    {
      key: 'typeFlag',
      value: this.packageService.PACKAGE_TYPEFLAG_PROPOSED.toString(),
    },
    {
      key: 'typeFlag',
      value: this.packageService.PACKAGE_TYPEFLAG_ACTIVE.toString(),
    },
    {
      key: 'statusFlag',
      value: this.packageService.PACKAGE_STATUSFLAG_ACTIVE.toString(),
    },
  ];

  dataList: PackageAnalysisDto[] = [];
  total: PackageAnalysisDto = new PackageAnalysisDto();
  totalProposedVhr: number = 0;
  totalActiveVHr: number = 0;
  originalDataList: any[] = [];
  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    startDate: '',
    finalDate: '',

    proposedProbablity: '',
    stageServiceAmount: '',
    expectedVhr: '',
    vHrAssigned: '',
    vHrAssignedCost: '',
  };
  totalActivePackages: number = 0;
  totalProposedPackages: number = 0;
  get PACKAGE_TYPE_FLAG_ACTIVE() {
    return this.packageService.PACKAGE_TYPEFLAG_ACTIVE;
  }
  get PACKAGE_TYPE_FLAG_PROPOSED() {
    return this.packageService.PACKAGE_TYPEFLAG_PROPOSED;
  }
  get PACKAGE_COMPLETED_COLOR() {
    return this.config.PACKAGE_COMPLETED_COLOR;
  }
  get PACKAGE_PROPOSED_COLOR() {
    return this.config.PACKAGE_PROPOSED_COLOR;
  }
  get PACKAGE_ACTIVE_COLOR() {
    return this.config.PACKAGE_ACTIVE_COLOR;
  }

  get PAYMENT_RECEIVED_COLOR() {
    return this.config.PAYMENT_RECEIVED_COLOR;
  }
  get PAYMENT_DUE_COLOR() {
    return this.config.PAYMENT_DUE_COLOR;
  }

  get VHR_CONSUMED_COLOR() {
    return this.config.VHR_CONSUMED_COLOR;
  }
  get VHR_X_COLOR() {
    return this.config.VHR_X_COLOR;
  }
  constructor(
    private authService: AuthService,
    private packageService: PackageApiService,
    private contactService: ContactApiService,
    private config: AppConfig,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
    private typeMasterService: TypeMasterService
  ) {}
  ngOnInit() {
    this.refresh();
    this.getPartnerOptions();
    this.getTypeOptions();
    this.getStatusOptions();
    this.getCompanyOptions();

    this.typeFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter((i) => i.key !== 'typeFlag');
          value.forEach((element: any) => {
            this.addFilter('typeFlag', element);
          });
          this.refresh();
        }
      });

    this.statusFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter(
            (i) => i.key !== 'projectstatusFlag'
          );
          value.forEach((element: any) => {
            this.addFilter('projectstatusFlag', element);
          });
          this.refresh();
        }
      });

    this.searchFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    this.partnerFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter(
            (i) => i.key !== 'projectPartnerContactID'
          );
          value.forEach((contact: Contact) => {
            if (contact && contact.id) {
              this.addFilter('projectPartnerContactID', contact.id);
            }
          });
          this.refresh();
        }
      });

    this.companyFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter((i) => i.key !== 'companyID');
          if (value && value.id) {
            this.addFilter('companyID', value.id);
          }
          this.refresh();
        }
      });
  }

  private async refresh() {
    this.dataList = await firstValueFrom(
      this.packageService.getAnalysis(
        'full',
        this.filters,
        this.searchKey,
        'finaldate'
      )
    );

    this.dataList = this.dataList
      .sort((a, b) => b.proposedProbablity - a.proposedProbablity)
      .map((x) => {
        x.expectedVhr =
          x.vHrAssigned != 0 && x.vHrAssignedCost / x.vHrAssigned != 0
            ? x.stageServiceAmount / 5.0 / (x.vHrAssignedCost / x.vHrAssigned)
            : 0;

        return x;
      });
    this.calculateTotal(this.dataList);
    this.extractDistinctValues();
    this.originalDataList = [...this.dataList];
  }
  calculateTotal(dataList: PackageAnalysisDto[]) {
    this.total = new PackageAnalysisDto();
    this.totalActiveVHr = 0;
    this.totalProposedVhr = 0;
    this.totalActivePackages = 0;
    this.totalProposedPackages = 0;
    this.dataList.forEach((x) => {
      this.total.vHrAssigned += x.vHrAssigned;
      this.total.vHrAssignedCost += x.vHrAssignedCost;
      this.total.expectedVhr += x.expectedVhr;
      this.total.proposedVHrAssigned += x.proposedVHrAssigned;
      this.total.proposedVHrAssignedCost += x.proposedVHrAssignedCost;
      this.total.stageServiceAmount += x.stageServiceAmount;
    });
    this.totalActivePackages = this.dataList.filter(
      (x) => x.typeFlag == this.PACKAGE_TYPE_FLAG_ACTIVE
    ).length;
    this.totalProposedPackages = this.dataList.filter(
      (x) => x.typeFlag == this.PACKAGE_TYPE_FLAG_PROPOSED
    ).length;
    // this.totalActiveVHr = this.dataList.filter(x => x.typeFlag == this.PACKAGE_TYPE_FLAG_ACTIVE).map(x => x.vHrAssigned).reduce((a, b) => a + b);
    // this.totalProposedVhr = this.dataList.filter(x => x.typeFlag == this.PACKAGE_TYPE_FLAG_PROPOSED).map(x => x.vHrAssigned).reduce((a, b) => a + b);
    this.totalActiveVHr = this.dataList
      .filter((x) => x.typeFlag == this.PACKAGE_TYPE_FLAG_ACTIVE)
      .map((x) => x.vHrAssigned)
      .reduce((a, b) => a + b, 0); // 👈 Add initial value

    this.totalProposedVhr = this.dataList
      .filter((x) => x.typeFlag == this.PACKAGE_TYPE_FLAG_PROPOSED)
      .map((x) => x.vHrAssigned)
      .reduce((a, b) => a + b, 0);
  }

  private addFilter(key: string, value: any) {
    const _filter = this.filters.find((obj) => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  private async getCompanyOptions() {
    this.companyOptions = await firstValueFrom(
      this.companyAccountService.get()
    );

    this.companyFC.setValue(
      this.companyOptions.find((x) => x.id == 1),
      { emitEvent: false }
    );
  }

  private async getPartnerOptions() {
    this.partnerOptions = await firstValueFrom(
      this.contactService.get(this.partnerFilter)
    );
    if (
      !this.authService.isRoleMaster &&
      this.partnerOptions.find(
        (x) =>
          x.id ==
          (this.authService.currentUserStore
            ? this.authService.currentUserStore.contact.id
            : 0)
      )
    ) {
      this.partnerFC.setValue(
        this.partnerOptions.filter(
          (x) =>
            x.id ==
            (this.authService.currentUserStore
              ? this.authService.currentUserStore.contact.id
              : 0)
        )
      );
    }
  }

  private async getStatusOptions() {
    this.statusOptions = await firstValueFrom(
      this.statusMasterService.get([
        { key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT },
      ])
    );
    this.statusFC.setValue(
      this.filters.map((x) => x.value),
      { emitEvent: false }
    );
  }
  private async getTypeOptions() {
    this.typeOptions = await firstValueFrom(
      this.typeMasterService.get([
        { key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT },
      ])
    );
    this.typeFC.setValue(
      this.filters.map((x) => x.value),
      { emitEvent: false }
    );
  }

  onRefresh() {
    this.refresh();
  }

  onExportExcel() {
    this.packageService.exportAnalysisExcel(
      'full',
      this.filters,
      this.searchKey,
      this.sortFC.value
    );
  }

  filterDistinctProject() {
    const searchLower = this.projectSearchKey.toLowerCase();
    this.filteredProject = this.distinctProject.filter((element) =>
      element.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctPartner() {
    const searchLower = this.partnerSearchKey.toLowerCase();
    this.filteredPartner = this.distinctPartner.filter((element) =>
      element.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctPackage() {
    const searchLower = this.packageSearchKey.toLowerCase();
    this.filteredPackage = this.distinctPackage.filter((element) =>
      element.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctStatus() {
    const searchLower = this.statusSearchKey.toLowerCase();
    this.filteredStatus = this.distinctStatus.filter((element) =>
      element.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: Event) {
    this.projectSearchKey = '';
    this.partnerSearchKey = '';
    this.packageSearchKey = '';
    this.statusSearchKey = '';
    // this.associateSearchKey = '';

    this.filterDistinctProject(); // Reset options
    this.filterDistinctPartner(); // Reset options
    this.filterDistinctPackage(); // Reset options
    this.filterDistinctStatus(); // Reset options
    // this.filterDistinctAssociate(); // Reset options
  }

  toggleSelectAll(
    filterType: 'project' | 'partner' | 'package' | 'status'
  ): void {
    const distinctValues = this.getDistinctValues(filterType);

    switch (filterType) {
      case 'project':
        this.selectedProject = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;

      case 'partner':
        this.selectedPartner = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'package':
        this.selectedPackage = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'status':
        this.selectedStatus = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
    }

    this.applyFilters(); // Ensure filters update when selecting/deselecting all
  }
  getDistinctValues(
    filterType: 'project' | 'partner' | 'package' | 'status'
  ): string[] {
    switch (filterType) {
      case 'project':
        return this.distinctProject;
      case 'partner':
        return this.distinctPartner;
      case 'package':
        return this.distinctPackage;
      case 'status':
        return this.distinctStatus;
      default:
        return [];
    }
  }

  isAllSelected(
    filterType: 'project' | 'partner' | 'package' | 'status'
  ): boolean {
    const distinctValues = this.getDistinctValues(filterType);

    switch (filterType) {
      case 'project':
        return (
          this.selectedProject.length === distinctValues.length &&
          this.selectedProject.length > 0
        );
      case 'partner':
        return (
          this.selectedPartner.length === distinctValues.length &&
          this.selectedPartner.length > 0
        );
      case 'package':
        return (
          this.selectedPackage.length === distinctValues.length &&
          this.selectedPackage.length > 0
        );
      case 'status':
        return (
          this.selectedStatus.length === distinctValues.length &&
          this.selectedStatus.length > 0
        );
      default:
        return false;
    }
  }

  toggleSelection(value: string, type: string) {
    if (type === 'project') {
      this.selectedProject.includes(value)
        ? this.selectedProject.splice(this.selectedProject.indexOf(value), 1)
        : this.selectedProject.push(value);
    } else if (type === 'partner') {
      this.selectedPartner.includes(value)
        ? this.selectedPartner.splice(this.selectedPartner.indexOf(value), 1)
        : this.selectedPartner.push(value);
    } else if (type === 'package') {
      this.selectedPackage.includes(value)
        ? this.selectedPackage.splice(this.selectedPackage.indexOf(value), 1)
        : this.selectedPackage.push(value);
    } else if (type === 'status') {
      this.selectedStatus.includes(value)
        ? this.selectedStatus.splice(this.selectedStatus.indexOf(value), 1)
        : this.selectedStatus.push(value);
    }
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if (this.searchKey) {
      const searchLower = this.searchKey.toLowerCase();

      filteredList = filteredList.filter(
        (item) =>
          item.project?.toLowerCase().includes(searchLower) ||
          '' ||
          item.partner?.toLowerCase().includes(searchLower) ||
          '' ||
          item.package?.toLowerCase().includes(searchLower) ||
          '' ||
          item.projectStatus?.toLowerCase().includes(searchLower) ||
          ''
        // (item.entityTitle?.toLowerCase().includes(searchLower) || '')
      );
    }
    // Apply the filter for Assigned To if selected
    if (this.selectedProject.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedProject.includes(item.project)
      );
    }
    if (this.selectedPartner.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedPartner.includes(item.partner)
      );
    }

    if (this.selectedPackage.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedPackage.includes(item.package)
      );
    }
    if (this.selectedStatus.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedStatus.includes(item.projectStatus)
      );
    }

    this.dataList = filteredList;
    this.calculateTotal(this.dataList);
  }

  extractDistinctValues() {
    this.distinctProject = [
      ...new Set(this.dataList.map((item) => item.project)),
    ];
    this.distinctPartner = [
      ...new Set(this.dataList.map((item) => item.partner)),
    ];
    this.distinctPackage = [
      ...new Set(this.dataList.map((item) => item.package)),
    ];
    this.distinctStatus = [
      ...new Set(this.dataList.map((item) => item.projectStatus)),
    ];

    this.filteredProject = [...this.distinctProject];
    this.filteredPartner = [...this.distinctPartner];
    this.filteredPackage = [...this.distinctPackage];
    this.filteredStatus = [...this.distinctStatus];
  }

  clearSelection(type: string) {
    if (type === 'project') {
      this.selectedProject = [];
    } else if (type === 'partner') {
      this.selectedPartner = [];
    } else if (type === 'package') {
      this.selectedPackage = [];
    } else if (type === 'status') {
      this.selectedStatus = [];
    }
    this.applyFilters(); // Apply filters after clearing the selection
  }

  resetFilter() {
    this.selectedPartner = [];
    this.selectedProject = [];
    this.selectedPackage = [];
    this.selectedStatus = [];
    this.dataList = [...this.originalDataList]; // Restore full list
    this.calculateTotal(this.dataList);
  }

  sortData(column: keyof PackageAnalysisDto | '') {
    if (column === '') {
      // Reset to default (original data) but apply the filters again
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProject.length === 0 ||
            this.selectedProject.includes(item.project)) &&
          (this.selectedPartner.length === 0 ||
            this.selectedPartner.includes(item.partner)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status)) &&
          (this.selectedPackage.length === 0 ||
            this.selectedPackage.includes(item.package))
        );
      });

      // Reset the sort state to default
      this.sortState = {
        activeColumn: '',
        startDate: '',
        finalDate: '',

        proposedProbablity: '',
        stageServiceAmount: '',
        expectedVhr: '',
        vHrAssigned: '',
        vHrAssignedCost: '',
      };
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if no sorting is selected
    }

    // If the clicked column is already the active column, cycle through the three states
    const currentSort = this.sortState[column];

    if (currentSort === 'newFirst') {
      // If it's 'newFirst', change to 'oldFirst'
      this.sortState[column] = 'oldFirst';
    } else if (currentSort === 'oldFirst') {
      // If it's 'oldFirst', reset to default (no sorting)
      this.sortState[column] = '';
      this.sortState.activeColumn = '';
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProject.length === 0 ||
            this.selectedProject.includes(item.project)) &&
          (this.selectedPartner.length === 0 ||
            this.selectedPartner.includes(item.partner)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status)) &&
          (this.selectedPackage.length === 0 ||
            this.selectedPackage.includes(item.package))
        );
      });

      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if reset is selected
    } else {
      // If no sorting is applied, set it to 'newFirst' (ascending order)
      this.sortState[column] = 'newFirst';
    }

    // Set the active column
    this.sortState['activeColumn'] = column;

    // Reset other columns' sort state to '' (no sort)
    for (let key in this.sortState) {
      if (key !== column && key !== 'activeColumn') {
        this.sortState[key] = ''; // Reset other columns' sort state to no sort
      }
    }

    // Sorting logic: Compare dates for the active column
    const sortedData = [...this.dataList].sort((a, b) => {
      const dateA = new Date(a[column] as string);
      const dateB = new Date(b[column] as string);

      if (this.sortState[column] === 'newFirst') {
        return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
      } else if (this.sortState[column] === 'oldFirst') {
        return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
      }
      return 0; // If no sorting, return unchanged order
    });

    // Update the dataList with the sorted data
    this.dataList = sortedData;
    this.isSorted = true; // Set isSorted to true when sorting is applied
  }
}
