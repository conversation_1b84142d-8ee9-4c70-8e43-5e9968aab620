<mcv-header [title]="headerTitle"></mcv-header>
<div class="cockpit-view-wrapper">
    <mat-tab-group [selectedIndex]="selectedTabIndex" (selectedIndexChange)="onSelectedTabIndexChange($event)">
        <mat-tab>
            <ng-template mat-tab-label>
                <mat-icon>dashboard</mat-icon>
                <small>My Workboard</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit></app-cockpit>
            </ng-template>
        </mat-tab>

        <mat-tab>
            <ng-template mat-tab-label>
                <mat-icon>123</mat-icon>
                <small>Number Speaks</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-metrics></app-cockpit-metrics>
            </ng-template>
        </mat-tab>

        <mat-tab>
            <ng-template mat-tab-label>
                <mat-icon>view_timeline</mat-icon>
                <small>My Timeline</small>
            </ng-template>
            <ng-template matTabContent>
                <app-mcv-time-line-view [componentFlag]="'mytimeline'"  
               ></app-mcv-time-line-view>
            </ng-template>
        </mat-tab>

        <mat-tab>
            <ng-template mat-tab-label>
                <mat-icon>task_alt</mat-icon>
                <small>My TaskAnalysis</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-task-analysis></app-cockpit-task-analysis>
            </ng-template>
        </mat-tab>

        <mat-tab>
            <ng-template mat-tab-label>
                <mat-icon>card_giftcard</mat-icon>
                <small>My PackageAnalysis</small>
            </ng-template>
            <ng-template matTabContent>

                <app-sent-package-list></app-sent-package-list>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="IsPermissionTeamCalendar">
            <ng-template mat-tab-label>
                <mat-icon>event</mat-icon>
                <small>Team Calendar</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-team-calendar [showAll]="true"></app-cockpit-team-calendar>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionVHrAnalysis">
            <ng-template mat-tab-label>
                <mat-icon>analytics</mat-icon>
                <small>VhrAnalysis</small>
            </ng-template>
            <ng-template matTabContent>
                <mcv-vhr-analysis></mcv-vhr-analysis>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionLastBiteAnalysis">
            <ng-template mat-tab-label>
                <mat-icon>lunch_dining</mat-icon>
                <small>Last Bite</small>
            </ng-template>
            <ng-template matTabContent>
                <mcv-last-bite-analysis></mcv-last-bite-analysis>
            </ng-template>
        </mat-tab>
        
        <!-- <mat-tab *ngIf="isPermissionCashflow">
            <ng-template mat-tab-label>
                <mat-icon>currency_rupee</mat-icon>
                <small>Cashflow</small>
            </ng-template>
            <ng-template matTabContent>
                <app-project-cashflow-chart></app-project-cashflow-chart>
            </ng-template>
        </mat-tab> -->

        <mat-tab *ngIf="isPermissionCRM">
            <ng-template mat-tab-label>
                <mat-icon>connect_without_contact</mat-icon>
                <small>CRM</small>
            </ng-template>
            <ng-template matTabContent>
                <app-project-crm-chart></app-project-crm-chart>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionProjectProgress">
            <ng-template mat-tab-label>
                <mat-icon>sort</mat-icon>
                <small>Project Progress</small>
            </ng-template>
            <ng-template matTabContent>
                <project-progress-chart></project-progress-chart>
            </ng-template>
        </mat-tab>


        <mat-tab *ngIf="isPermissionPackagetProgress">
            <ng-template mat-tab-label>
                <mat-icon>card_giftcard</mat-icon>
                <small>Package Progress</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-my-analysis></app-cockpit-my-analysis>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionProjectProgress">
            <ng-template mat-tab-label>
                <mat-icon>point_of_sale</mat-icon>
                <small>Project Analysis</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-project-analysis></app-cockpit-project-analysis>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionProjectProgress">
            <ng-template mat-tab-label>
                <mat-icon>shopping_cart_checkout</mat-icon>
                <small>Proposed Packages</small>
            </ng-template>
            <ng-template matTabContent>
                <app-proposed-package-list></app-proposed-package-list>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionUpcomingPackages">
            <ng-template mat-tab-label>
                <mat-icon>batch_prediction</mat-icon>
                <small>Upcoming Packages</small>
            </ng-template>
            <ng-template matTabContent>
                <app-upcoming-package-data-grid></app-upcoming-package-data-grid>
            </ng-template>
        </mat-tab>

        <mat-tab *ngIf="isPermissionEstimationAnalysis">
            <ng-template mat-tab-label>
                <mat-icon>currency_rupee</mat-icon>
                <small>Estimation Analysis</small>
            </ng-template>
            <ng-template matTabContent>
                <app-project-estmation-analysis></app-project-estmation-analysis>
            </ng-template>
        </mat-tab>

        <!-- <mat-tab *ngIf="isPermissionLastBiteAnalysis">
            <ng-template mat-tab-label>
                <mat-icon>calendar_month</mat-icon>
                <small>Agenda Schedule</small>
            </ng-template>
            <ng-template matTabContent>
                <app-cockpit-my-calendar></app-cockpit-my-calendar>
            </ng-template>
        </mat-tab> -->

         <mat-tab *ngIf="isPermissionBillAnalysisView">
            <ng-template mat-tab-label>
                <mat-icon>receipt_long</mat-icon>
                <small>Bill Analysis</small>
            </ng-template>
            <ng-template matTabContent>
                <!-- <app-project-estmation-analysis></app-project-estmation-analysis> -->
           <app-bill-analysis></app-bill-analysis>
            </ng-template>
        </mat-tab>
        <mat-tab *ngIf="isPermissionExpenseEdit">
            <ng-template mat-tab-label>
                <mat-icon>request_quote</mat-icon>
                <small>Expense Analysis</small>
            </ng-template>
            <ng-template matTabContent>
                <!-- <app-project-estmation-analysis></app-project-estmation-analysis> -->
              <app-expense-analysis></app-expense-analysis>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>