import { Routes } from '@angular/router';

export const ROOT_ROUTES: Routes = [
    
    { path: 'minutes', loadChildren: () => import('./meeting-minutes').then(m => m.MEETING_MINUTES_ROUTES) },
    { path: 'inspection-report', loadChildren: () => import('./inspection-report').then(m => m.INSPECTION_REPORT_ROUTES) },
    { path: 'submission', loadChildren: () => import('./package-submission').then(m => m.SUBMISSION_ROUTES) },

   //USED IN OLD EMAILs, DO NOT REMOVE
    { path: 'download', loadChildren: () => import('./package-submission').then(m => m.SUBMISSION_ROUTES) },
   //USED IN OLD EMAILs, DO NOT REMOVE

    { path: 'cockpit', loadChildren: () => import('./cockpit').then(m => m.COCKPIT_VIEW_ROUTES) },
    { path: 'process-library', loadChildren: () => import('./process-library').then(m => m.PROCESS_LIBRARY_ROUTES) },
    { path: 'design-script-editor', loadChildren: () => import('./design-script').then(m => m.DESIGN_SCRIPT_ROUTES) },
    { path: 'wftask-list', loadChildren: () => import('./wf-task').then(m => m.WFTASK_ROUTES) },
    { path: 'todo-list', loadChildren: () => import('./todo').then(m => m.TODO_ROUTES) },
    { path: 'habit-list', loadChildren: () => import('./mcv-habit').then(m => m.MCV_HABIT_LIST_ROUTES) },
    { path: 'package-list', loadChildren: () => import('./package').then(m => m.PACKAGE_ROUTES) },
    { path: 'agenda-list', loadChildren: () => import('./agenda-list').then(m => m.AGENDALIST_ROUTES) },
    { path: 'expense-list', loadChildren: () => import('./expense').then(m => m.EXPENSE_ROUTES) },
    { path: 'meeting-list', loadChildren: () => import('./meeting').then(m => m.MEETING_ROUTES) },
    { path: 'inspection-list', loadChildren: () => import('./inspection').then(m => m.INSPECTION_ROUTES) },
    { path: 'leave-list', loadChildren: () => import('./leave-application').then(m => m.LEAVE_APPLICATION_ROUTES) },
    { path: 'project-list', loadChildren: () => import('./project').then(m => m.PROJECT_ROUTES) },
    { path: 'team-list', loadChildren: () => import('./team').then(m => m.TEAM_ROUTES) },
    { path: 'request-ticket-list', loadChildren: () => import('./request-ticket').then(m => m.REQUEST_TICKET_ROUTES) },
    { path: 'contact-list', loadChildren: () => import('./contact').then(m => m.CONTACT_ROUTES) },
    { path: 'library-view', loadChildren: () => import('./library').then(m => m.LIBRARY_ROUTES) },
    { path: 'payroll-list', loadChildren: () => import('./payroll').then(m => m.PAYROLL_ROUTES) },
    // { path: 'expense-analysis', loadChildren: () => import('./expense-anaylsis').then(m => m.EXPENSE_ANALYSIS_ROUTES) },
    { path: 'time-analysis', loadChildren: () => import('./time-analysis').then(m => m.TIME_ANALYSIS_ROUTES) },
    // { path: 'bill-analysis', loadChildren: () => import('./bill-analysis').then(m => m.BILL_ANALYSIS_ROUTES) },
    { path: 'inquiry-analysis', loadChildren: () => import('./inquiry-analysis').then(m => m.INQUIRY_ANALYSIS_ROUTES) },
    { path: 'time-line', loadChildren: () => import('./time-line').then(m => m.TIME_LINE_ROUTES) },
    { path: 'design-script-presentation', loadChildren: () => import('./design-script-presentation-view').then(m => m.DESIGN_SCRIPT_PRESENTATION_ROUTES) },
    { path: 'masters', loadChildren: () => import('./master-view').then(m => m.MASTER_ROUTES) },
    { path: 'project-scope', loadChildren: () => import('./project-scope').then(m => m.PROJECT_SCOPE_ROUTES) },
    { path: 'sendgrid-analysis', loadChildren: () => import('./sendgrid-analysis').then(m => m.SENDGRID_ROUTES) },
    { path: 'expense-master', loadChildren: () => import('./expense-head-masters').then(m => m.EXPENSE_MASTER_ROUTES) },
    { path: 'permission', loadChildren: () => import('./permissions').then(m => m.PERMISSIONS_ROUTES) },
    { path: 'design-script-item', loadChildren: () => import('./design-script-item').then(m => m.DESIGN_SCRIPT_ITEM_ROUTES) },
    { path: 'app-setting-master', loadChildren: () => import('./mcv-app-setting-master').then(m => m.APP_SETTING_MASTER_ROUTES) },
    { path: 'company-master', loadChildren: () => import('./mcv-company-master').then(m => m.COMPANY_MASTER_ROUTES) },
    { path: 'type-master', loadChildren: () => import('./mcv-type-master').then(m => m.TYPE_MASTER_ROUTES) },
    { path: 'status-master', loadChildren: () => import('./mcv-status-master').then(m => m.STATUS_MASTER_ROUTES) },
  
    { path: 'auth', loadChildren: () => import('./auth').then(m => m.AUTH_ROUTES) },
    // { path: '', redirectTo: 'cockpit', pathMatch: 'full' },
    { path: '', loadChildren: () => import('./cockpit').then(m => m.COCKPIT_VIEW_ROUTES) },
    { path: '**', redirectTo: '/cockpit', pathMatch: 'full' },
];
