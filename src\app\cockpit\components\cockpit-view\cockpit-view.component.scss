@import 'variables';

.cockpit-view-wrapper {
    background-color: $page-background;
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    top: $app-header-height;
    left: 0;
    right: 0;
    bottom: 0;

    ::ng-deep .mat-mdc-tab-group {
        height: 100%;
        overflow: hidden;

        .mat-mdc-tab-body {
            overflow: hidden !important;
        }

        .mat-mdc-tab-list {
            border-bottom: 1px solid $gray-200;
            padding: 0 0.3rem 0.2rem;

            .mdc-tab {
                min-width: 4rem !important;
                height: 48px !important;
                padding: 0 0.8rem !important;
                opacity: 1 !important;
                margin: 0 0.1rem !important;
                background-color: $white !important;
                // box-shadow: $box-shadow !important;
            }

            .mat-mdc-tab .mdc-tab__text-label {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                white-space: nowrap;

                small {
                    font-size: 0.6rem;
                }
            }

            .mat-mdc-tab.mdc-tab {
                flex-grow: 0 !important;
            }
        }

        .mat-mdc-tab-label,
        .mat-mdc-tab-link {
            font-family: $font-family-sans-serif !important;
        }

        .mat-mdc-tab-body-wrapper {
            height: 100%;

            .mat-mdc-tab-body-content {
                overflow-x: hidden !important;
            }
        }
    }


    ::ng-deep .mat-mdc-tab-list {
        padding: 0 !important;
    }

    ::ng-deep .mat-mdc-tab.mdc-tab {
        min-width: 4rem !important;
        height: 48px;
        padding: 0 .8rem !important;
        opacity: 1 !important;
        background-color: $gray-200 !important;
        box-shadow: none !important;
    }
}

@media (max-width: 768px) {
    .cockpit-view-wrapper {
        top: 92px;
    }
}