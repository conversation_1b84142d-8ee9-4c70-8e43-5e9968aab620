import { Injectable } from "@angular/core";
import { MatDialogConfig } from "@angular/material/dialog";
import { Router, ActivatedRoute } from "@angular/router";
import { BehaviorSubject, Observable, Subject } from "rxjs";
import { map } from "rxjs/operators";
import { Package } from "src/app/package/models/package.model";
import { Project } from "src/app/project/models/project.model";
import { McvBaseApiService } from "src/app/services/mcv-base-api.service";
import { DesignScriptItemDialogComponent } from "../components/design-script-item-dialog/design-script-item-dialog.component";
import { DesignScriptLibraryDialogComponent } from "../components/design-script-library-dialog/design-script-library-dialog.component";
import { DesignScriptPhaseEditorDialogComponent } from "../components/design-script-phase-editor-dialog/design-script-phase-editor-dialog.component";
import { DesignScriptPresentationEntityAttachmentDialogComponent } from "../components/design-script-presentation-entity-attachment-dialog/design-script-presentation-entity-attachment-dialog.component";
import { DesignScriptEntity } from "../models/design-script-entity.model";
import { DesignScriptBreadcrumb, DesignScriptListConfig, DesignScriptQueryFragment } from "../models/design-script-list-config.model";
import { DesignScriptLibraryEntityFormDialogComponent } from '../components/design-script-library-entity-form-dialog/design-script-library-entity-form-dialog.component';
import { DesignScriptLibraryEntityAttachmentDialogComponent } from '../components/design-script-library-entity-attachment-dialog/design-script-library-entity-attachment-dialog.component';
import { DesignScriptDataCard } from "../models/design-script-data-card.model";
import { DesignScriptDatacardPhaseLinkDialogComponent } from "../components/design-script-datacard-phase-link-dialog/design-script-datacard-phase-link-dialog.component";
import { DesignDetailMatrixEditorDialogComponent } from "src/app/design-script/components/design-detail-matrix-editor-dialog/design-detail-matrix-editor-dialog.component";
import { DesignScriptEntityMoveDialogComponent } from "../components/design-script-entity-move-dialog/design-script-entity-move-dialog.component";
import { LibraryEntityAttachment } from "src/app/library/model/library-entity.model";
import { DesignScriptSpaceEstimateEditorComponent } from "src/app/design-script/components/design-script-space-estimate-editor/design-script-space-estimate-editor.component";
import { DesignScriptElementEstimateEditorComponent } from "src/app/design-script/components/design-script-element-estimate-editor/design-script-element-estimate-editor.component";
import { DesignScriptBoqEditorComponent } from "src/app/design-script/components/design-script-boq-editor/design-script-boq-editor.component";
import { DesignScriptDataCardDetailsComponent } from "../components/design-script-data-card-details/design-script-data-card-details.component";
import { DesignScriptDataCardEditComponent } from "../components/design-script-data-card-edit/design-script-data-card-edit.component";
import { ApiFilter } from "src/app/models/api-filters";
import { DesignScriptReportsComponent } from "../components/design-script-reports/design-script-reports.component";
import { DesingScriptProject } from "../interface/design-script-project-interface";
import { DesignScriptZoneBoqComponent } from "src/app/design-script/components/design-script-zone-boq/design-script-zone-boq.component";


@Injectable({
  providedIn: 'root'
})
export class DesignScriptApiService extends McvBaseApiService {

  override apiRoute = this.config.apiDesignScriptEntity;

  private _queryFragments: DesignScriptQueryFragment[] = [];
  get queryFragments() { return this._queryFragments; }
  set queryFragments(value) { this._queryFragments = value; }

  private _selectedLibraryItems: DesignScriptDataCard[] = [];
  get selectedLibraryItems() { return this._selectedLibraryItems; }
  set selectedLibraryItems(value) { this._selectedLibraryItems = value; }

  private _currentDesignScriptEntity!: DesignScriptEntity;
  get currentDesignScriptEntity() { return this._currentDesignScriptEntity; }
  set currentDesignScriptEntity(value) { this._currentDesignScriptEntity = value; }

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
    super();
    this.$libraryItemSelectionChange = new BehaviorSubject(false);
  }

  exportReport(uid: string, reportName: string, size: string = 'a4', output: 'PDF' | 'EXCELOPENXML' = 'PDF', filters?: ApiFilter[], search?: string, sort?: string) {

    let url = this.apiRoute + `/report/${reportName}/${uid}`;
    if (filters && filters.length !== 0) {
      const filtersParam = encodeURIComponent(JSON.stringify({ filters: filters }));
      url += `?filters=${filtersParam}`;
    }

    if (size) {
      if (url.includes('?')) {
        url += `&size=${size}`;
      } else {
        url += `?size=${size}`;
      }
    }

    if (search) {
      if (url.includes('?')) {
        url += `&search=${search}`;
      } else {
        url += `?search=${search}`;
      }
    }

    if (sort) {
      if (url.includes('?')) {
        url += `&sort=${sort}`;
      } else {
        url += `?sort=${sort}`;
      }
    }

    if (output) {
      if (url.includes('?')) {
        url += `&output=${output}`;
      } else {
        url += `?output=${output}`;
      }
    }

    window.open(url, '_blank');
  }

  private readonly DESIGN_SCRIPT_CATEGORY_OPTIONS = [
    { label: 'CIVIL', class: 'ds-category-civil', icon: 'business' },
    { label: 'FINISHING', class: 'ds-category-finishing', icon: 'format_color_fill' },
    { label: 'FURNITURE', class: 'ds-category-furniture', icon: 'chair' },
    { label: 'LIGHTING', class: 'ds-category-lighting', icon: 'light' },
    { label: 'PLANTING', class: 'ds-category-planting', icon: 'forest' },
    { label: 'SERVICES', class: 'ds-category-services', icon: 'electrical_services' },
  ];

  resetView() {
    // console.log('resetView');
    this.activeProjectID = null;
    this.activeProject = null;
    this.selectedProjectPackages = [];
    this.breadcrumbs = [];
    this.listConfigs = [];
    this.selectedEntities = [];
    this.entityList = [];
    this.searchValue.next('');
  }

  private searchValue = new Subject<string>();
  searchValue$ = this.searchValue.asObservable();

  setSearchValue(value: string) {
    this.searchValue.next(value);
  }

  loadLists(dataList: DesignScriptEntity[], levelQueryParams: DesignScriptQueryFragment[], featureRoot: string, breadcrumbProperty: string) {
    // console.log('loadLists', this.listConfigs.length,levelQueryParams.length);
    if (dataList && dataList.length != 0) {
      if (this.listConfigs && this.listConfigs.length > 1) {
        this.listConfigs.splice(1);
      }


      if (levelQueryParams.length != 0) {
        this.selectLevel(Number(levelQueryParams[0].value), 0);
        for (let i = 1; i < levelQueryParams.length; i++) {
          this.selectLevel(Number(levelQueryParams[i].value), i);
        }
      } else {
        this.listConfigs.push(this.getListConfig(0));
      }

      this.buildBreadcrumbs(levelQueryParams, featureRoot);

    }
  }

  private selectLevel(id: number, index: number) {
    // console.log('select level', index, id, this.dataList.find(x => x.id === id));
    const _selectedItem = this.entityList.find(x => x.id === id);
    if (!_selectedItem) {
      this.goBack();
    } else {
      this.selectedEntities.splice(index);
      this.selectedEntities.push(_selectedItem);
      this.listConfigs.splice(index + 1);
      this.listConfigs.push(this.getListConfig(index + 1, _selectedItem));
      // console.log('listConfigs',this.listConfigs);
    }
  }

  goBack() {

    if (this.queryFragments.length === 0) {
      // this.utilityService.locationBack();
      this.resetView();
    } else {
      // this.utilityService.locationBack();
      this.queryFragments
        .splice(this.queryFragments.length - 1);

      // console.log('back',this.queryParams);

      let _queryString = '{';

      this.queryFragments.forEach(param => {
        _queryString = _queryString + `"${param.key}":"${param.value}",`;
      });

      _queryString = _queryString.lastIndexOf(',') == _queryString.length - 1 ?
        _queryString.substring(0, _queryString.lastIndexOf(',')) :
        _queryString;

      _queryString = _queryString + '}';

      // console.log('_queryString', _queryString);
      this.router.navigate(['..' + this.URL_ROOT], { queryParams: JSON.parse(_queryString), relativeTo: this.activatedRoute });
    }
  }

  get categoryOptions() { return this.DESIGN_SCRIPT_CATEGORY_OPTIONS; }

  getNextCode(projectID: number, TypeFlag: number, parentID?: number): Observable<any> {
    let params: any = {
      parentID: parentID,
    };

    Object.entries(params).forEach(o => (o[1] === null || o[1] === undefined ? delete params[o[0]] : 0));

    return this.http.get<any>(this.apiRoute + '/nextcode/' + TypeFlag + '/' + projectID, { params: params });
  }

  tagOptions: string[] = [];
  getSearchTagOptionsByProject(projectID: number): Observable<string[]> {
    return this.http.get<string[]>(this.apiRoute + "/SearchTagOptions/" + projectID)
      .pipe(
        map(result => {
          this.tagOptions = result;
          return this.tagOptions;
        })
      );
  }

  addToTagOptions(tags: string | string[]) {
    if (typeof tags == 'string') {
      if (!this.tagOptions.includes(tags)) {
        this.tagOptions.push(tags);
      }
    } else {
      tags.forEach(x => {
        if (!this.tagOptions.includes(x)) {
          this.tagOptions.push(x);
        }
      });
    }
  }


  getFilteredDataCards(source: DesignScriptDataCard[],
    typeFlag: number,
    showDeleted: boolean = false): DesignScriptDataCard[] {
    let cards: DesignScriptDataCard[] = [];
    if (!showDeleted) {
      source = source.filter(x => !x.isDeleted);
    }
    cards.push(...source
      .filter(x => x.typeFlag == typeFlag)
    );
    return cards.sort((a, b) => a.orderFlag - b.orderFlag);
  }

  get isPermissionView(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_VIEW]);
  }

  get isPermissionEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_EDIT]);
  }

  get isPermissionCostingEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_COSTING_EDIT]);
  }
  get IsPermissionDesignDetailEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_DESIGN_DETAIL_EDIT]);
  }

  get isPermissionDelete(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_DELETE]);
  }

  get isPermissionSpecialPhaseEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_SPECIAL_PHASE_EDIT]);
  }

  get isPermissionSpecialSpaceEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_SPECIAL_SPACE_EDIT]);
  }

  get isPermissionSpecialElementEdit(): boolean {
    return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_SPECIAL_ELEMENT_EDIT]);
  }

  get isPermissionSpecialShowAllProjects(): boolean {
    return this.authService.isInAnyRole([
      this.permissions.DESIGN_SCRIPT_SPECIAL_SHOW_ALL_PROJECTS
    ]);
  }

  get isPermissionReports(): boolean {
    return this.authService.isInAnyRole([
      this.permissions.DESIGN_SCRIPT_REPORTS
    ]);
  }

  get isPermissionDeliveries(): boolean {
    return this.authService.isInAnyRole([
      this.permissions.DESIGN_SCRIPT_DELIVERIES
    ]);
  }

  get isPermissionIntentEdit(): boolean {
    return this.authService.isInAnyRole([
      this.permissions.DESIGN_SCRIPT_INTENT_EDIT
    ]);
  }


  get TYPEFLAG_ZONE() { return this.config.DESIGN_SCRIPT_TYPEFLAG_ZONE; }
  get TYPEFLAG_SPACE() { return this.config.DESIGN_SCRIPT_TYPEFLAG_SPACE; }
  get TYPEFLAG_ELEMENT() { return this.config.DESIGN_SCRIPT_TYPEFLAG_ELEMENT; }
  get TYPEFLAG_VISION() { return this.config.DESIGN_SCRIPT_DATA_CARD_TYPE_FLAG_VISION; }
  get TYPEFLAG_GOAL() { return this.config.DESIGN_SCRIPT_DATA_CARD_TYPE_FLAG_GOAL; }
  get TYPEFLAG_STRATEGY() { return this.config.DESIGN_SCRIPT_DATA_CARD_TYPE_FLAG_STRATEGY; }

  openPhaseEditorDialog(data: any) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;

    dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    dialogConfig.data = data;

    return this.dialog.open(
      DesignScriptPhaseEditorDialogComponent,
      dialogConfig
    );
  }

  getTreeMapExcel(projectID: number) {
    window.open(`${this.apiRoute}/Excel/TreeMap/${projectID}`);
  }


  private _listConfigs: DesignScriptListConfig[] = [];
  get listConfigs() { return this._listConfigs; }
  set listConfigs(value: DesignScriptListConfig[]) { this._listConfigs = value; }

  private _breadcrumbs: DesignScriptBreadcrumb[] = [];
  get breadcrumbs() { return this._breadcrumbs; }
  set breadcrumbs(value: DesignScriptBreadcrumb[]) { this._breadcrumbs = value; }

  private _projects: any[] = [];
  get projects() { return this._projects; }
  set projects(value: any[]) { this._projects = value; }

  private _activeProject!: Project | null;
  get activeProject() { return this._activeProject; }
  set activeProject(value: Project | null) { this._activeProject = value; }

  private _activeProjectID!: number | null;
  get activeProjectID() { return this._activeProjectID; }
  set activeProjectID(value: number | null) { this._activeProjectID = value; }

  private _orientation: 'vertical' | 'horizontal' = 'vertical';
  get orientation() { return this._orientation; }
  set orientation(value: 'vertical' | 'horizontal') { this._orientation = value; }

  private _selectedDataCard: LibraryEntityAttachment[] = [];
  get selectedDataCard() { return this._selectedDataCard; }
  set selectedDataCard(value: LibraryEntityAttachment[]) { this._selectedDataCard = value; }

  private _selectedEntities: DesignScriptEntity[] = [];
  get selectedEntities() { return this._selectedEntities; }
  set selectedEntities(value: DesignScriptEntity[]) { this._selectedEntities = value; }

  private _entityList: DesignScriptEntity[] = [];
  get entityList() { return this._entityList; }
  set entityList(value: DesignScriptEntity[]) { this._entityList = value; }

  get masterPhase(): DesignScriptEntity { return this.entityList.find(x => x.isMasterPhase) ?? new DesignScriptEntity(); }

  get entityListCopy() { return this._entityList.map(x => Object.assign({}, x)); }
  // set entityList(value: DesignScriptEntity[]) { this._entityList = value.map(x => Object.assign({}, x)); }

  get entityTree() { return this.getTreeMapFromList(this.entityList); }

  private _showDeleted: boolean = false;
  get showDeleted() { return this._showDeleted; }
  set showDeleted(value: boolean) { this._showDeleted = value; }

  private _isReadOnly: boolean = false;
  get isReadOnly() { return this._isReadOnly; }
  set isReadOnly(value: boolean) { this._isReadOnly = value; }

  private _selectedProjectPackages: Package[] = [];
  get selectedProjectPackages() { return this._selectedProjectPackages; }
  set selectedProjectPackages(value: Package[]) { this._selectedProjectPackages = value; }

  private _toggleCard: boolean = false;
  get toggleCard() { return this._toggleCard }
  set toggleCard(value: boolean) { this._toggleCard = value }

  private _showEditForm: boolean = false;
  get showEditForm() { return this._showEditForm; }
  set showEditForm(value: boolean) { this._showEditForm = value; }

  private _unlinkDataCard: { entity: DesignScriptEntity, dataCard: DesignScriptDataCard }[] = [];
  get unlinkDataCard() { return this._unlinkDataCard; }
  set unlinkDataCard(value: { entity: DesignScriptEntity, dataCard: DesignScriptDataCard }[]) { this._unlinkDataCard = value; }

  private _toggleDataCard: boolean = false;
  get toggleDataCard() { return this._toggleDataCard; }
  set toggleDataCard(value: boolean) { this._toggleDataCard = value; }

  //Card to replace with
  private _replaceCard!: DesignScriptDataCard;
  get replaceCard() { return this._replaceCard; }
  set replaceCard(value: DesignScriptDataCard) { this._replaceCard = value; }

  get URL_ROOT() { return this.config.ROUTE_DESIGN_SCRIPT; }


  /**
   * Toggles orientation
   */
  toggleOrientation() {
    if (this.orientation == 'horizontal') {
      this.orientation = 'vertical';
    } else {
      this.orientation = 'horizontal';
    }
  }

  /**
   * Gets query param object
   * @param levelIndex
   * @param [lastLevelItem]
   * @returns query param object
   */
  getQueryParamObject(levelIndex: number, lastLevelItem?: DesignScriptEntity): any {
    // console.log('queryParams',listConfig);
    let _queryString = '{';
    if (this.activeProjectID) {
      _queryString = _queryString + `"project":"${this.activeProjectID}",`;
    }
    var i = 0;
    for (; i < levelIndex; i++) {
      if (lastLevelItem && lastLevelItem.id == this.selectedEntities[i].id) {
        // console.log('breaking..');
        break;
      }
      _queryString = _queryString + `"${'level' + i}":"${this.selectedEntities[i].id}",`;
      // console.log('continuing..');
    }

    if (lastLevelItem) {
      _queryString = _queryString + `"${'level' + i}":"${lastLevelItem.id}"`;
    }


    _queryString = _queryString.lastIndexOf(',') == _queryString.length - 1 ?
      _queryString.substring(0, _queryString.lastIndexOf(',')) :
      _queryString;
    _queryString = _queryString + '}';

    // console.log('_queryString', _queryString);
    return JSON.parse(_queryString);
  }

  /**
  * Builds breadcrumbs
  * @param queryFragments
  * @param featureRoot
  * @param property
  * @returns breadcrumbs
  */
  buildBreadcrumbs(
    queryFragments: DesignScriptQueryFragment[],
    featureRoot: string,
  ) {

    // Each breadcrumb represents the ability to jump an arbitrary point in the
    // hierarchical data. As such, it will be easiest if we map each name to a root-
    // relative point below the feature.
    var runningPath = featureRoot + "/";
    this.breadcrumbs = queryFragments.filter(x => x.key.includes('level'))
      .map((fragment) => {
        // Each name becomes the next URL-segment in the running path.
        runningPath += encodeURIComponent(fragment.value);
        const _item = this.selectedEntities.find(x => x.id === Number(fragment.value));
        var _queryParams = this.getQueryParamObject(
          fragment.order + 1,
          _item
        );
        // console.log('breadcrumb',fragment, fragment.order,queryFragments.filter(x => x.key.includes('level')).length);
        return ({
          key: fragment.key,
          value: fragment.value,
          order: fragment.order,
          title: _item ? _item.title : fragment.value.toString(),
          code: _item ? _item.code : '',
          path: runningPath,
          queryParams: _queryParams,
          isActive: fragment.order == queryFragments.filter(x => x.key.includes('level')).length
        });
      });


  }


  /**
   * Filters items
   * @param dataList
   * @param typeFlag
   * @param [parentID]
   * @returns items
   */
  filterItems(
    dataList: DesignScriptEntity[],
    typeFlag: number,
    parentID: number | null = null
  ): DesignScriptEntity[] {

    // console.log('parentID',parentID,dataList)
    const _items = dataList.filter((item) => item.parentID == parentID
      // && item.typeFlag == typeFlag
      //&& this.dmsItemService.isPermissible(item.permissions, this.dmsItemService.DMS_PERMISSION_TYPEFLAG_READ)
    );
    // console.log('filterItems', typeFlag, parentID, _items);
    return (_items);
  }

  /**
   * Determines whether master phase is
   * @param item
   * @returns
   */
  isMasterPhase(item: DesignScriptEntity) {
    return item ? item.typeFlag == 0 && item.codeFlag == 0 : false;
  }

  openProjectView(projectID: number) {

    this.router.navigate([this.URL_ROOT], {
      queryParams: JSON.parse(`{"project":"${projectID}"}`),
      relativeTo: this.activatedRoute
    });

  }

  openEntityView(levelIndex: number, entity: DesignScriptEntity) {
    this.router.navigate([this.URL_ROOT], {
      queryParams: this.getQueryParamObject(levelIndex, entity),
      relativeTo: this.activatedRoute
    });

  }

  getListConfig(
    levelIndex: number,
    parentItem: DesignScriptEntity | null = null,
  ) {
    const _filteredItems = this.filterItems(this.entityList, 0, parentItem ? parentItem.id : null);
    // console.log('_filteredItems', _filteredItems);
    const _listConfig = new DesignScriptListConfig();
    _listConfig.entities = _filteredItems;
    _listConfig.levelIndex = levelIndex;
    // _listConfig.selectedItems = selectedItems;
    _listConfig.parentItem = parentItem ?? undefined;


    return _listConfig;
  }

  openEntityEditDialog(entity: DesignScriptEntity) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;

    dialogConfig.data = {
      dialogTitle: `Edit | ${entity.type}`,

      config: {
        entity: entity,
        isCreateMode: false,
      },
    };

    return this.dialog.open(
      DesignScriptItemDialogComponent,
      dialogConfig
    );

  }

  openEntityMoveDialog(entity: DesignScriptEntity) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;

    dialogConfig.data = {
      dialogTitle: `Move | ${entity.type} | ${entity.code}-${entity.title}`,

      entity: entity
    };

    return this.dialog.open(
      DesignScriptEntityMoveDialogComponent,
      dialogConfig
    );

  }

  openNewEntityDialog(parent: DesignScriptEntity) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;

    dialogConfig.data = {
      dialogTitle: `New | ${this.getNewEntityType(parent.typeFlag)}`,
      config: {
        entity: new DesignScriptEntity({
          projectID: this.activeProjectID ?? 0,
          parentID: parent ? parent.id : 0,
          typeFlag: parent ?
            (parent.typeFlag == this.TYPEFLAG_ZONE ? this.TYPEFLAG_SPACE :
              (parent.typeFlag == this.TYPEFLAG_SPACE ? this.TYPEFLAG_ELEMENT : this.TYPEFLAG_ZONE)) :
            this.TYPEFLAG_ZONE,
        }),
        isCreateMode: true,
      },
      // childComponent: DesignBoardItemDetailComponent
    };

    return this.dialog.open(
      DesignScriptItemDialogComponent,
      dialogConfig
    );

  }

  getNewEntityType(parentTypeFlag: number) {
    return `${parentTypeFlag == this.TYPEFLAG_SPACE ? 'Element' :
      (parentTypeFlag == this.TYPEFLAG_ZONE ? 'Space' : 'Phase')}`;
  }


  getTreeMapFromList(data: DesignScriptEntity[], onlyWithAttachments: boolean = false): DesignScriptEntity[] {
    const idMapping = data.reduce((acc: any, el: any, i: any) => {
      acc[el.id] = i;
      return acc;
    }, {});
    let roots: any[] = [];
    let _list = data.map(x => Object.assign({}, x));
    _list.forEach(el => {
      // Handle the root element
      if (el.parentID === null) {
        roots.push(el);
        return;
      }
      // Use our mapping to locate the parent element in our data array
      const parentEl = _list[idMapping[el.parentID]];
      if (parentEl) {
        // Add our current el to its parent's `children` array
        if (onlyWithAttachments) {
          parentEl.children = parentEl.children ? [...(parentEl.children.filter(x => x.dataCards.length != 0) || []), el] : [];
        } else {
          parentEl.children = parentEl.children ? [...(parentEl.children || []), el] : [];
        }
      }
    });
    if (onlyWithAttachments) {
      roots = roots.filter(x => x.children.length != 0 || x.attachments.length != 0);
    }
    return roots;
  }

  getListFromTreeMap(tree: DesignScriptEntity[]): DesignScriptEntity[] {
    let children: any[] = [];
    const flattenMembers = tree.map(m => {
      if (m.children && m.children.length) {
        children = [...children, ...m.children];
      }
      return m;
    });

    return flattenMembers.concat(children.length ? this.getListFromTreeMap(children) : children);
  }

  openEntityAttachmentEditorDialog(designScriptDataCard: DesignScriptDataCard, scriptEntity: DesignScriptEntity, tagOptions: string[] = []) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    // if (this.isReadOnly)
    // {
    //   dialogConfig.maxWidth = '100vw';
    //   dialogConfig.maxHeight = '100vh';
    //   dialogConfig.height = '100%';
    //   dialogConfig.width = '100%';
    // }
    dialogConfig.width = '100%';
    dialogConfig.height = '100%';
    dialogConfig.data = {
      dialogTitle: `${scriptEntity.code} | ${scriptEntity.title} `,
      isReadOnly: this.isReadOnly,
      config: {
        designScriptDataCard: designScriptDataCard,
        scriptEntity: scriptEntity,
        tagOptions: tagOptions
      },
    };

    if (this.isPermissionEdit) {
      return this.dialog.open(
        DesignScriptDataCardEditComponent,
        dialogConfig,
      );
    } else {
      return this.dialog.open(
        DesignScriptDataCardDetailsComponent,
        dialogConfig,
      );
    }
  }

  openEntityAttachmentPresentationDialog(attachments: DesignScriptDataCard[], selectedItem: DesignScriptDataCard, tagOptions: string[] = []) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;

    dialogConfig.panelClass = 'design-script-fullscreen-dialog';
    dialogConfig.maxWidth = '100vw';
    dialogConfig.maxHeight = '100vh';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';

    dialogConfig.data = {
      dialogTitle: ``,
      isReadOnly: this.isReadOnly,
      config: {
        attachments,
        selectedItem,
        tagOptions,
      },
    };

    return this.dialog.open(
      DesignScriptPresentationEntityAttachmentDialogComponent,
      dialogConfig,
    );
  }

  openLibraryDialog(designScriptEntity: DesignScriptEntity) {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;

    dialogConfig.panelClass = 'design-script-fullscreen-dialog';
    dialogConfig.maxWidth = '100vw';
    dialogConfig.maxHeight = '100vh';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';

    dialogConfig.data = {
      dialogTitle: `Library`,
      isReadOnly: this.isReadOnly,
      designScriptEntity: designScriptEntity
    };
    return this.dialog.open(
      DesignScriptLibraryDialogComponent,
      dialogConfig,
    );
  }

  openReportFilterDialog(project: Project, zones: DesignScriptEntity[]) {

    return this.openDialog(
      DesignScriptReportsComponent,
      {
        zones,
        project
      }, true
    );
  }

  protected $libraryItemSelectionChange: BehaviorSubject<boolean>;
  get libraryItemSelectionChange(): Observable<boolean> {
    return this.$libraryItemSelectionChange.asObservable();
  }

  libraryItemSelectionChanged() {
    this.$libraryItemSelectionChange.next(true);
    this.$libraryItemSelectionChange.next(false);
  }



  openLibraryEntityFormDialog(dialogData: any) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';
    // dialogConfig.width = '80%';
    dialogConfig.data = dialogData;
    return this.dialog.open(DesignScriptLibraryEntityFormDialogComponent, dialogConfig);
  }

  openDesignScriptLibraryImageAttachment(itemData: any, itemDataGroup: any) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.width = '80%';
    dialogConfig.data = {
      itemData: itemData,
      itemDataGroup: itemDataGroup
    }
    return this.dialog.open(DesignScriptLibraryEntityAttachmentDialogComponent, dialogConfig)
  }

  openSpaceEstimate() {

    const dialogConfig = {
      dialogTitle: `Space Estimate | ${this.activeProject?.code}-${this.activeProject?.title}`,
    };
    return this.openDialog(
      DesignScriptSpaceEstimateEditorComponent,
      dialogConfig, true
    );

  }
  openElementEstimate() {

    const dialogConfig = {
      dialogTitle: `Element Estimate | ${this.activeProject?.code}-${this.activeProject?.title}`,
    };
    return this.openDialog(
      DesignScriptElementEstimateEditorComponent,
      dialogConfig, true
    );

  }


  openDesignDetailMatrixDialog() {

    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.maxWidth = '100vw';
    dialogConfig.maxHeight = '100vh';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';
    dialogConfig.data = {
      dialogTitle: `${this.activeProject?.code}-${this.activeProject?.title} | Design Detail Matrix`,
    };


    return this.dialog.open(
      DesignDetailMatrixEditorDialogComponent,
      dialogConfig
    );
  }

  openLinkDataCardDialog(entity: any, phaseToPhase: any) {

    const _superParent = this.getSuperParent(entity);
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.maxWidth = '100vw';
    dialogConfig.maxHeight = '100vh';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';
    dialogConfig.data = {
      phaseToPhase: phaseToPhase,
      entity: entity,
      dataCards: _superParent ? _superParent.dataCards
        .filter(x => !x.isDeleted && x.typeFlag == this.config.DESIGN_SCRIPT_DATA_CARD_TYPE_FLAG_GENERAL && x.category) : [],
    }

    return this.dialog.open(
      DesignScriptDatacardPhaseLinkDialogComponent,
      dialogConfig
    );
  }

  getSuperParent(entity: DesignScriptEntity): DesignScriptEntity | null {
    if (!entity) return null;

    if (!entity.parentID) return entity;

    const _parent = this.entityList.find(x => x.id == entity.parentID);
    if (_parent)
      return this.getSuperParent(_parent);
    return null;
  }


  openPhaseToPhaseLink(entity: DesignScriptEntity, dsCards: DesignScriptDataCard[]) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.autoFocus = true;
    dialogConfig.maxWidth = '100vw';
    dialogConfig.maxHeight = '100vh';
    dialogConfig.height = '100%';
    dialogConfig.width = '100%';
    dialogConfig.data = {
      dsEntity: entity,
      dsCollection: dsCards.filter(x => !x.isDeleted && x.typeFlag == this.config.DESIGN_SCRIPT_DATA_CARD_TYPE_FLAG_GENERAL)
    }

    // return this.dialog.open()
  }
}
