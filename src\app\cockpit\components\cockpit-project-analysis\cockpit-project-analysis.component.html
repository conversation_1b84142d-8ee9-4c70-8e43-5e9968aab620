<div class="project-analysis-wrapper">
  <div class="data-filter-row" >
    <div class="filter-header" *ngIf="selectedProject.length > 0 " >
       <h6 class="font-focused ">Filters:</h6> 
  </div>
      
      <h6 *ngIf="selectedProject.length > 0">
          <b>Project:</b> {{selectedProject.join(', ')}}
          <span class="clear-icon" (click)="clearSelection('project')">✖</span>
      </h6>
     
      <h6  (click)="resetFilter()" *ngIf="selectedProject.length > 0 " >
        <b> Clear All</b>
       
      </h6>
  </div>
  <div class="project-analysis-table-wrapper">
    <table>
      <thead>
        <tr>
          <th mat-button [matMenuTriggerFor]="assignedToMenu" [ngClass]="{'filter': selectedProject.length > 0}">

            <div class="analysis-table-header">
              <h6>Project</h6>
              <mat-icon>filter_alt</mat-icon>
  
              <mat-menu #assignedToMenu="matMenu">
                <div class="search-container p-1">
                  <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                    <input matInput placeholder="Search" [(ngModel)]="projectKeySearch"
                      (input)="filterDistinctProject()" />
                    <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                  </mat-form-field>
                </div>
                <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('project')">
                  {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
                </button>
                <mat-option *ngFor="let project of filteredProject"
                  (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                  <mat-checkbox [checked]="selectedProject.includes(project)">{{ project }}</mat-checkbox>
                </mat-option>
              </mat-menu>
            </div>
          </th>
       
          <th>Status</th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'totalFee'}">
            <div class="analysis-table-header" (click)="sortData('totalFee')">
              <h6>Total Fee</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalFee' && sortState['totalFee'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalFee' && sortState['totalFee'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'totalFee' || !sortState['totalFee'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'totalBillAmount'}">
            <div class="analysis-table-header" (click)="sortData('totalBillAmount')">
              <h6>Total Bill Amount</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalBillAmount' && sortState['totalBillAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalBillAmount' && sortState['totalBillAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'totalBillAmount' || !sortState['totalBillAmount'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'totalPaymentAmount'}"> 
            <div class="analysis-table-header" (click)="sortData('totalPaymentAmount')">
              <h6>Total Payment received</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalPaymentAmount' && sortState['totalPaymentAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'totalPaymentAmount' && sortState['totalPaymentAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'totalPaymentAmount' || !sortState['totalPaymentAmount'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'proformaAmount'}">
            <div class="analysis-table-header" (click)="sortData('proformaAmount')">
              <h6>Proforma Sent</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proformaAmount' && sortState['proformaAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proformaAmount' && sortState['proformaAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'proformaAmount' || !sortState['proformaAmount'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'proformaDate'}">           
            <div class="analysis-table-header" (click)="sortData('proformaDate')">
              <h6>Proforma Date</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proformaDate' && sortState['proformaDate'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proformaDate' && sortState['proformaDate'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'proformaDate' || !sortState['proformaDate'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHr'}">          
            <div class="analysis-table-header" (click)="sortData('vHr')">
              <h6> vHR</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHr' && sortState['vHr'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHr' && sortState['vHr'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHr' || !sortState['vHr'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrCost'}">
            <div class="analysis-table-header" (click)="sortData('vHrCost')">
              <h6>vHR Cost</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrCost' && sortState['vHrCost'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrCost' && sortState['vHrCost'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHrCost' || !sortState['vHrCost'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'xCost'}">
            <div class="analysis-table-header" (click)="sortData('xCost')">
              <h6>5x Cost</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'xCost' && sortState['xCost'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'xCost' && sortState['xCost'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'xCost' || !sortState['xCost'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrAfterLastPayment'}">
            <div class="analysis-table-header" (click)="sortData('vHrAfterLastPayment')">
              <h6>vHR After Last Payment</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrAfterLastPayment' && sortState['vHrAfterLastPayment'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrAfterLastPayment' && sortState['vHrAfterLastPayment'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHrAfterLastPayment' || !sortState['vHrAfterLastPayment'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'vHrCostAfterLastPayment'}">
            <div class="analysis-table-header" (click)="sortData('vHrCostAfterLastPayment')">
              <h6>vHR Cost After Last Payment</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrCostAfterLastPayment' && sortState['vHrCostAfterLastPayment'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrCostAfterLastPayment' && sortState['vHrCostAfterLastPayment'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHrCostAfterLastPayment' || !sortState['vHrCostAfterLastPayment'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'completedAmount'}">
            <div class="analysis-table-header" (click)="sortData('completedAmount')">
              <h6>Completed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'completedAmount' && sortState['completedAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'completedAmount' && sortState['completedAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'completedAmount' || !sortState['completedAmount'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'activeAmount'}">
            <div class="analysis-table-header" (click)="sortData('activeAmount')">
              <h6>Active</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'activeAmount' && sortState['activeAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'activeAmount' && sortState['activeAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'activeAmount' || !sortState['activeAmount'] ">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'proposedAmount'}">
            <div class="analysis-table-header" (click)="sortData('proposedAmount')">
              <h6>Proposed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proposedAmount' && sortState['proposedAmount'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'proposedAmount' && sortState['proposedAmount'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'proposedAmount' || !sortState['proposedAmount'] ">import_export</mat-icon>
            </div>
          </th>
        </tr>
        <tr class="total">
          <td colspan="2">Total</td>
          <td>{{total.totalFee | currency:'INR':'symbol':'1.0-0'}}</td>
          <td>
            {{total.totalBillAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.totalPaymentAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.proformaAmount |currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td></td>
          <td>{{ total.vHr | number:'1.2-2' }}</td>
          <td>
            {{total.vHrCost | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.xCost | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.vHrAfterLastPayment | number:'1.0-1'}}
          </td>
          <td>{{total.vHrCostAfterLastPayment | currency:'INR':'symbol':'1.0-0'}}</td>
          <td>
            {{total.completedAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.activeAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
          <td>
            {{total.proposedAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of projectCashFlow">
          <td class="text-nowrap"> {{item.code}} - {{item.project}}</td>
      
          <td>{{item.status}}</td>
          <td class="align-right font-focused pr-2">{{item.totalFee | currency:'INR':'symbol':'1.0-0'}}</td>
         <td class="align-right font-focused pr-2">
            ({{item.totalBillPercentage | number:'1.0-1'}}%)
            {{item.totalBillAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.totalPaymentPercentage | number:'1.0-1'}}%)
            {{item.totalPaymentAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.proformaPercentage | number:'1.0-1'}}%)
            {{item.proformaAmount |currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">{{item.proformaDate | date:'dd MMM yyyy'}}</td>
         <td class="align-right font-focused pr-2">{{item.vHr | number:'1.0-1'}}</td>
         <td class="align-right font-focused pr-2">
            ({{item.vHrCostPercentage | number:'1.0-1'}}%)
            {{item.vHrCost | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.xCostPercentage | number:'1.0-1'}}%)
            {{item.xCost | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.vHrCostPercentage | number:'1.0-1'}}%)
            {{item.vHrAfterLastPayment | number:'1.0-1'}}
          </td>
         <td class="align-right font-focused pr-2">{{item.vHrCostAfterLastPayment | currency:'INR':'symbol':'1.0-0'}}</td>
         <td class="align-right font-focused pr-2">
            ({{item.completedPercentage | number:'1.0-1'}}%)
            {{item.completedAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.activePercentage | number:'1.0-1'}}%)
            {{item.activeAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
         <td class="align-right font-focused pr-2">
            ({{item.proposedPercentage | number:'1.0-1'}}%)
            {{item.proposedAmount | currency:'INR':'symbol':'1.0-0'}}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<app-footer>
  <div class="nav-footer-actions">
    <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
      <mat-icon matPrefix>search</mat-icon>
      <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
    </div>
    <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>

    <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
      Export Excel
    </button>
  </div>

  <div class="nav-footer-mobile-actions">
    <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>

    
    <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
      Export Excel
    </button>
  </div>

  <div class="nav-filters">
    <div class="inline-list">
      <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
        <mat-icon matPrefix>search</mat-icon>
        <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
      </div>
      <mat-form-field appearance="outline">
        <mat-select [formControl]="companyFC" placeholder="Company">
          <mat-option *ngFor="let item of companyOptions" [value]="item">
            {{item.title }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
          <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-select [formControl]="statusFC" multiple placeholder="Status">
          <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav >
  <div>
    <mat-form-field appearance="outline">
      <mat-select [formControl]="companyFC" placeholder="Company">
        <mat-option *ngFor="let item of companyOptions" [value]="item">
          {{item.title }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field appearance="outline">
      <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
        <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field appearance="outline">
      <mat-select [formControl]="statusFC" multiple placeholder="Status">
        <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  </app-mcv-filter-sidenav>