
@import 'variables';
@import 'theme';
mat-dialog-content {
  width: 1024px !important;
  height: 90vh !important;
  max-height: 100% !important;
}
::ng-deep .mat-pseudo-checkbox {
  display: none !important;
}

::ng-deep .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) {
  background-color: transparent !important;
}
.mcv-dialog-content {
  display: flex;
  flex-direction: column;
  overflow: hidden !important;

  .table-container {
    flex: 1 1 auto;
    overflow: auto;
    height: calc(100% - 2.5rem);
    .filter-header {
      display: flex;
      align-items: start;
      justify-content: left;
      padding: 0rem 0.5rem;

  }
  .data-filter-row {
      // Dark background for contrast

      padding: 0.3rem 0.8rem;

      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 0.5rem; // Spacing between items

      h6 {
          padding: 0.2rem;
          margin: 0;
          font-size: 0.6rem;
          background: rgba(124, 124, 124, 0.1);
          border-radius: 3px;
          display: flex;
          align-items: center;
          gap: 0.3rem;
          cursor: pointer;

          b {
              font-weight: 600;
              color: mat-color($mcv-primary, 500); // Highlighting filter type
          }

          .clear-icon {
              cursor: pointer;
              font-size: 0.75rem;
              margin-left: 0.3rem;
              transition: opacity 0.3s;
              opacity: 0.6;

              &:hover {
                  opacity: 1;
              }
          }
      }
  }
    .table {
      width: 100%;
      position: relative;
      border: 1px solid $gray-300;
      border-collapse: collapse;

      .table-header {
        position: sticky;
        top: 0; // Ensure header stays at the top when scrolling
        background-color: $gray-100;
        z-index: 2;
      }

      th, td {
        white-space: nowrap;
        border: 1px solid $gray-300;
        padding: 3px;
      }

      th {
        background-color: $gray-100;
        text-align: center;
        font-size: 0.8rem;  
        font-weight: 600;
        vertical-align: middle;
        padding: 0.3rem;
        .myagenda-table-header {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.8rem;
  
          h6 {
            font-size: 0.8rem;
              font-weight: 600;
          }
  
          mat-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 1rem;
  
          }
  
          .menu-mat-option {
              mat-pseudo-checkbox {
                  display: none !important;
              }
          }
        
      }

          
      &.filter {
        h6 {
            color: white;
        }

        background-color: mat-color($mcv-primary, 500);

        mat-icon {
            color: white;
        }
    }
      }

      td mat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        cursor: pointer;
      }

      .title-column,
      .subtitle-column{
        white-space: normal !important;
        word-break: break-word;
        min-width: 4rem;
        max-width: 15rem;
      }
      .comment-column{
       text-align: center;
      }

   
    }
  }

  .pagination-footer {
    height: 2.5rem;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding: 0 1rem;
    border-top: 1px solid $gray-300;
    background: $gray-100;
  }
}
