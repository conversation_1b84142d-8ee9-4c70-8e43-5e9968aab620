
@import 'variables';
@import 'theme';
mat-dialog-content {
  width: 1024px !important;
  height: 85vh !important;
  max-height: 100% !important;
}
::ng-deep .mat-pseudo-checkbox {
  display: none !important;
}
::ng-deep .mat-mdc-menu-panel {
  max-height: 30rem !important;
  overflow-y: auto;
} 
::ng-deep .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) {
  background-color: transparent !important;
}
.mcv-dialog-content {
  display: flex;
  flex-direction: column;
  overflow: hidden !important;

  .table-container {
    flex: 1 1 auto;
    overflow: hidden auto;
    height: calc(100% - 2.5rem);
  .data-filter-row {
      // Dark background for contrast

      padding: 0.3rem 0rem;

      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 0.5rem; // Spacing between items

      h6 {
          padding: 0.2rem;
          margin: 0;
          font-size: 0.6rem;
          background: rgba(124, 124, 124, 0.1);
          border-radius: 3px;
          display: flex;
          align-items: center;
          gap: 0.3rem;
          cursor: pointer;

          b {
              font-weight: 600;
              color: mat-color($mcv-primary, 500); // Highlighting filter type
          }

          .clear-icon {
              cursor: pointer;
              font-size: 0.75rem;
              margin-left: 0.3rem;
              transition: opacity 0.3s;
              opacity: 0.6;

              &:hover {
                  opacity: 1;
              }
          }
      }
  }

       table {
        max-width: 100%;
        width:100%;
        font-size: 0.8rem;
        cursor: pointer;
    
        thead {
            background-color: $gray-100;
            position: sticky;
            position: -webkit-sticky;
            top: 0;
    
            tr {
                &.total {
                    background-color: $gray-600;
                    color: $white;
                }
    
                td,
                th {
                    border-bottom: 1px solid $gray-400;
                    white-space: nowrap;
                    border-right: 1px solid $gray-300;
                    padding: 0 0.3rem;
                    vertical-align: middle;
                    text-align: center;
    
                    &.align-right {
                        text-align: right;
                    }
    
                    &.text-pre-wrap {
                        white-space: pre-wrap;
                    }
    
                    &.collapsed {
                        padding: 0 !important;
                    }
                    &.filter , &.sort{
                        h6 {
                            color: white;
                        }
    
                        background-color: mat-color($mcv-primary, 500);
    
                        mat-icon {
                            color: white;
                        }
                    }
    
                }
            }
            .myagenda-table-header {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.8rem;
    
                h6 {
                    font-weight: 600;
                    font-size: 0.8rem !important;
                }
    
                mat-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1rem;
    
                }
    
                .menu-mat-option {
                    mat-pseudo-checkbox {
                        display: none !important;
                    }
                }
              
            }
        }
    
        tbody {
            tr {
                td {
                    border-bottom: 1px solid $gray-400;
                    // white-space: nowrap;
                    border-right: 1px solid $gray-300;
                    padding: 0.3rem;
    
                    &.text-right {
                        text-align: right;
                    }
    
                    &.text-wrap {
                        white-space: pre-wrap;
                    }
    
                    &.collapsed {
                        padding: 0 !important;
                    }
    
                }
    
    
            }
        }
    }
    .filter-header {
      display: flex;
      align-items: start;
      justify-content: left;
      padding: 0rem 0.5rem;

  }

 
  }

  .pagination-footer {
    height: 2.5rem;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding: 0 1rem;
    border-top: 1px solid $gray-300;
    background: $gray-100;
  }
}
