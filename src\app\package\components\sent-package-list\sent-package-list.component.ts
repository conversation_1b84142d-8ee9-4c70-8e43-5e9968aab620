import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { AppConfig } from 'src/app/app.config';
import { AuthService } from 'src/app/auth/services/auth.service';
import { Contact } from 'src/app/contact/models/contact';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { PackageAnalysisDto } from '../../models/package-analysis-dto';
import { PackageApiService } from '../../services/package-api.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { AppPermissions } from 'src/app/app.permissions';
import { ApiFilter } from 'src/app/models/api-filters';
import { firstValueFrom } from 'rxjs';
import { McvManHourPipe } from '../../../mcv-pipe/pipes/mcv-man-hour.pipe';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
    selector: 'app-sent-package-list',
    templateUrl: './sent-package-list.component.html',
    styleUrls: ['./sent-package-list.component.scss'],
    standalone: true,
    imports: [ FooterComponent,MatCheckboxModule,McvFilterSidenavComponent, MatMenuModule,MatOptionModule,FormsModule, CommonModule, MatIconModule, MatFormFieldModule, MatInputModule, ReactiveFormsModule, MatDatepickerModule, MatSelectModule, MatOptionModule, MatButtonModule, MatTooltipModule, FilterToggleDirective, McvManHourPipe]
})
export class SentPackageListComponent implements OnInit {
  searchKey!: string;
  isSorted: boolean = false;
  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();
  dateFilters = new FormGroup({
    start: new FormControl(this.utilityService.getMonthStart()),
    end: new FormControl(this.utilityService.getMonthEnd())
  });

  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];
  showAll: boolean = false;
  projectSearchKey: string = '';
  partnerSearchKey: string = '';
  associateSearchKey: string = '';
  
  packageSearchKey: string = '';

  filteredProject: string[] = [];
  filteredPartner: string[] = [];
  filteredAssociate: string[] = [];
  
  filteredPackage: string[] = [];

  distinctProject: string[] = [];
  distinctPartner: string[] = [];
  distinctAssociate: string[] = [];
 
  distinctPackage: string[] = [];

  selectedProject: string[] = [];
  selectedPartner: string[] = [];
  selectedAssociate: string[] = [];
  
  selectedPackage: string[] = [];

  partnerFilter = [
    { key: 'usersOnly', value: 'true' },
    { key: 'projectPartnersOnly', value: 'true' }
  ];

  sortState: { 
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
  
    startDate: '',
    finalDate: '',
    submissionDate: '',
    vHrAssigned: '',
    vHrConsumed: '',
    vHrBalance: '',
    delay: ''
  };

  showFilters: boolean = true;
  filters: ApiFilter[] = [
    { key: 'partnerOrAssociateContactID', value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '0' },
    { key: 'rangeStart', value: this.utilityService.getMonthStart().toISOString() },
    { key: 'rangeEnd', value: this.utilityService.getMonthEnd().toISOString() },
    // { key: 'projectstatusFlag', value: this.config.PROJECT_STATUS_FLAG_INPROGRESS },
    // { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_ONHOLD },
    // { key: 'projectstatusFlag', value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL },
    // { key: 'projectstatusFlag', value: this.config.PROJECT_STATUS_FLAG_LOCKED },
    // { key: 'companyID', value: '1' },
    // { key: 'typeFlag', value: this.packageService.PACKAGE_TYPEFLAG_PROPOSED },
    { key: 'typeFlag', value: this.packageService.PACKAGE_TYPEFLAG_ACTIVE.toString() },
    // { key: 'statusFlag', value: this.packageService.PACKAGE_STATUSFLAG_ACTIVE },
    { key: 'statusFlag', value: this.packageService.PACKAGE_STATUSFLAG_SENT.toString() },
  ];

  dataList: PackageAnalysisDto[] = [];
  originalDataList: any[] = [];
  total: PackageAnalysisDto = new PackageAnalysisDto();


  get PACKAGE_TYPE_FLAG_ACTIVE() { return this.packageService.PACKAGE_TYPEFLAG_ACTIVE; }
  get PACKAGE_TYPE_FLAG_PROPOSED() { return this.packageService.PACKAGE_TYPEFLAG_PROPOSED; }

  get isMobileView(): boolean { return this.utilityService.isMobileView; }
  get isPermissionSpecialShowAll() { return this.authService.isInAnyRole([this.permissions.COCKPIT_PACKAGE_ANALYSIS_SPECIAL_SHOW_ALL]); }

  constructor(
    private authService: AuthService,
    private packageService: PackageApiService,
    private contactService: ContactApiService,
    private config: AppConfig,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
    private utilityService: UtilityService,
    private permissions: AppPermissions
  ) { }
  ngOnInit() {

  
    this.getPartnerOptions();
    this.getStatusOptions();
    this.getCompanyOptions();
    this.refresh();
    this.dateFilters.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.filters = this.filters.filter(x => x.key !== 'rangeStart' && x.key != 'rangeEnd');
          this.filters.push({ key: 'rangeStart', value: value.start.toISOString() });
          this.filters.push({ key: 'rangeEnd', value: value.end.toISOString() });

          this.refresh();
        }
      });


    this.statusFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "projectstatusFlag");
            value.forEach((element: any) => {
              this.addFilter('projectstatusFlag', element);
            });
            this.refresh();
          }
        }
      );

    this.searchFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    if (this.isPermissionSpecialShowAll) {
      this.partnerFC.valueChanges
        .pipe(
          debounceTime(400),
          distinctUntilChanged()
        )
        .subscribe(
          (value) => {
            if (value) {
              this.filters = this.filters.filter(i => i.key !== "projectPartnerContactID");
              value.forEach((contact: Contact) => {
                if (contact && contact.id) {
                  this.addFilter('projectPartnerContactID', contact.id.toString());
                }
              });
              this.refresh();
            }
          }
        );
    }

    this.companyFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "companyID");
            if (value && value.id) {
              this.addFilter('companyID', value.id)
            }
            this.refresh();
          }
        }
      );

     
  }



  private async refresh() {
  
    this.dataList = await firstValueFrom(this.packageService.getAnalysis('full', this.filters, this.searchKey, 'finaldate'));

    this.dataList = this.dataList.sort((a, b) => b.proposedProbablity - a.proposedProbablity).map(x => {
      x.expectedVhr =
        x.vHrAssigned != 0 && (x.vHrAssignedCost / x.vHrAssigned) != 0 ? (x.stageServiceAmount / 5.0) / (x.vHrAssignedCost / x.vHrAssigned) : 0;

      return x;
    });

    this.calculateTotal(this.dataList);
    this.extractDistinctValues();
    this.originalDataList = [...this.dataList];
  }
  calculateTotal(dataList: any) {
    this.total = new PackageAnalysisDto();
    this.dataList.forEach(x => {
      this.total.vHrAssigned += x.vHrAssigned;
      this.total.vHrAssignedCost += x.vHrAssignedCost;
      this.total.expectedVhr += x.expectedVhr;
      this.total.proposedVHrAssigned += x.proposedVHrAssigned;
      this.total.proposedVHrAssignedCost += x.proposedVHrAssignedCost;
      this.total.stageServiceAmount += x.stageServiceAmount;
      this.total.delay += x.delay;
      this.total.vHrBalance += x.vHrBalance;
      this.total.vHrConsumed += x.vHrConsumed;
    });
  }

  private addFilter(key: string, value: string) {
    const _filter = this.filters.find(obj => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  private async getCompanyOptions() {
    this.companyOptions = await firstValueFrom(this.companyAccountService.get());
    this.companyFC.setValue(this.companyOptions.find(x => x.id == 1), { emitEvent: false });
  }

  private async getPartnerOptions() {
    this.partnerOptions = await firstValueFrom(this.contactService.get(this.partnerFilter));
    if (!this.authService.isRoleMaster && this.partnerOptions.find(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))) {
      this.partnerFC.setValue(this.partnerOptions.filter(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))
      );
    }

  }

  private async getStatusOptions() {
    this.statusOptions = await firstValueFrom(this.statusMasterService.get([{ key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT}]));
    this.statusFC.setValue(this.filters.map(x => x.value), { emitEvent: false });
  
  }

  onRefresh() {
    this.refresh();
  }

  onExportExcel() {
    this.packageService.exportAnalysisExcel('full', this.filters, this.searchKey, this.sortFC.value);
  }

  onToggleShowAll() {
    this.showAll = !this.showAll;
    if (this.showAll) {
      this.filters = this.filters.filter(x => x.key !== 'partnerOrAssociateContactID')
    } else {
      this.filters.push({ key: 'partnerOrAssociateContactID', value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '0' });
    }
    this.refresh();
  }

  filterDistinctProject() {
    const searchLower = this.projectSearchKey.toLowerCase();
    this.filteredProject = this.distinctProject.filter(element =>
      element.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctPartner() {
    const searchLower = this.partnerSearchKey.toLowerCase();
    this.filteredPartner = this.distinctPartner.filter(element =>
      element.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctAssociate() {
    const searchLower = this.associateSearchKey.toLowerCase();
    this.filteredAssociate = this.distinctAssociate.filter(element =>
      element.toLowerCase().includes(searchLower)
    );
  }

  
  filterDistinctPackage() {
    const searchLower = this.packageSearchKey.toLowerCase();
    this.filteredPackage = this.distinctPackage.filter(element =>
      element.toLowerCase().includes(searchLower)
    );
  }

  

  clearSearch(event: Event) {
    this.projectSearchKey = '';
    this.partnerSearchKey = '';
    this.associateSearchKey = '';
   
    this.packageSearchKey = '';
    this.filterDistinctProject(); // Reset options
    this.filterDistinctPartner(); // Reset options
    this.filterDistinctAssociate(); // Reset options
  
    this.filterDistinctPackage(); // Reset options
  }

  toggleSelectAll(filterType: 'project' | 'partner' | 'associate' | 'status' | 'package'): void {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'project':
        this.selectedProject = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
    
      case 'partner':
        this.selectedPartner = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;

      case 'associate':
        this.selectedAssociate = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;

     
    
      case 'package':
        this.selectedPackage = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
    
    }
    
    this.applyFilters(); // Ensure filters update when selecting/deselecting all
  }

  toggleSelection(value: string, type: string) {
   
    if (type === 'project') {
      this.selectedProject.includes(value)
        ? this.selectedProject.splice(this.selectedProject.indexOf(value), 1)
        : this.selectedProject.push(value);
    } 
    else if (type === 'partner') {
      this.selectedPartner.includes(value)
        ? this.selectedPartner.splice(this.selectedPartner.indexOf(value), 1)
        : this.selectedPartner.push(value);
    } 
    else if (type === 'associate') {
      this.selectedAssociate.includes(value)
        ? this.selectedAssociate.splice(this.selectedAssociate.indexOf(value), 1)
        : this.selectedAssociate.push(value);
    } 

   
    else if (type === 'package') {
      this.selectedPackage.includes(value)
        ? this.selectedPackage.splice(this.selectedPackage.indexOf(value), 1)
        : this.selectedPackage.push(value);
    }
  
    this.applyFilters();
  }

  getDistinctValues(filterType: 'project' | 'partner' | 'associate' | 'status' | 'package'): string[] {
    switch (filterType) {
      case 'project':
        return this.distinctProject;
      case 'partner':
        return this.distinctPartner;
      case 'associate':
        return this.distinctAssociate;
     
      case 'package':
        return this.distinctPackage;
      default:
        return [];
    }
  }

  isAllSelected(filterType: 'project' | 'partner' | 'associate' | 'status' | 'package'): boolean {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'project':
        return this.selectedProject.length === distinctValues.length && this.selectedProject.length > 0;
      case 'partner':
        return this.selectedPartner.length === distinctValues.length && this.selectedPartner.length > 0;
      case 'associate':
        return this.selectedAssociate.length === distinctValues.length && this.selectedAssociate.length > 0;
     
      case 'package':
        return this.selectedPackage.length === distinctValues.length && this.selectedPackage.length > 0;
      default:
        return false;
    }
  }
 


sortData(column: keyof PackageAnalysisDto  | '') {
  if (column === '') {
    // Reset to default (original data) but apply the filters again
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
        (this.selectedProject.length === 0 || this.selectedProject.includes(item.person)) &&
        (this.selectedPartner.length === 0 || this.selectedPartner.includes(item.entityTitle)) && 
        (this.selectedAssociate.length === 0 || this.selectedAssociate.includes(item.taskTitle)) &&
      
        (this.selectedPackage.length === 0 || this.selectedPackage.includes(item.taskTitle)) 
      
      );
    });

    // Reset the sort state to default
    this.sortState = { 
      activeColumn: '',
      startDate: '',
      finalDate: '',
      submissionDate: '',
      vHrAssigned: '',
      vHrConsumed: '',
      vHrBalance: '',
      delay: ''
    };
    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if no sorting is selected
  }

  // If the clicked column is already the active column, cycle through the three states
  const currentSort = this.sortState[column];

  if (currentSort === 'newFirst') {
    // If it's 'newFirst', change to 'oldFirst'
    this.sortState[column] = 'oldFirst';
  } else if (currentSort === 'oldFirst') {
    // If it's 'oldFirst', reset to default (no sorting)
    this.sortState[column] = '';
    this.sortState.activeColumn = '';
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
       

        (this.selectedProject.length === 0 || this.selectedProject.includes(item.person)) &&
        (this.selectedPartner.length === 0 || this.selectedPartner.includes(item.entityTitle)) && 
        (this.selectedAssociate.length === 0 || this.selectedAssociate.includes(item.taskTitle)) &&
       
        (this.selectedPackage.length === 0 || this.selectedPackage.includes(item.taskTitle)) 
      );
    });

    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if reset is selected
  } else {
    // If no sorting is applied, set it to 'newFirst' (ascending order)
    this.sortState[column] = 'newFirst';
  }

  // Set the active column
  this.sortState['activeColumn'] = column;

  // Reset other columns' sort state to '' (no sort)
  for (let key in this.sortState) {
    if (key !== column && key !== 'activeColumn') {
      this.sortState[key] = ''; // Reset other columns' sort state to no sort
    }
  }

  // Sorting logic: Compare dates for the active column
  const sortedData = [...this.dataList].sort((a, b) => {
    const dateA = new Date(a[column] as string);
    const dateB = new Date(b[column] as string);

    if (this.sortState[column] === 'newFirst') {
      return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
    } else if (this.sortState[column] === 'oldFirst') {
      return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
    }
    return 0; // If no sorting, return unchanged order
  });

  // Update the dataList with the sorted data
  this.dataList = sortedData;
  this.isSorted = true; // Set isSorted to true when sorting is applied
}





  applyFilters() {
    
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if(this.searchKey){

      const searchLower = this.searchKey.toLowerCase();
  
     filteredList = filteredList.filter(item =>
        (item.project?.toLowerCase().includes(searchLower) || '') ||
        (item.partner?.toLowerCase().includes(searchLower) || '') ||
        (item.associate?.toLowerCase().includes(searchLower) || '') ||
        (item.package?.toLowerCase().includes(searchLower) || '') 
      
      );
    }
    // Apply the filter for Assigned To if selected
    if (this.selectedProject.length > 0) {
      filteredList = filteredList.filter(item => this.selectedProject.includes(item.project));
    }
    if (this.selectedPartner.length > 0) {
      filteredList = filteredList.filter(item => this.selectedPartner.includes(item.partner));
    }

    if (this.selectedAssociate.length > 0) {
      filteredList = filteredList.filter(item => this.selectedAssociate.includes(item.associate));
    }

    

    if (this.selectedPackage.length > 0) {
      filteredList = filteredList.filter(item => this.selectedPackage.includes(item.package));
    }

    
    
    this.dataList = filteredList;
    this.calculateTotal(this.dataList);
  }



  extractDistinctValues() {
    this.distinctProject = [...new Set(this.dataList.map(item => item.project))];
    this.distinctPartner = [...new Set(this.dataList.map(item => item.partner))];
    this.distinctAssociate = [...new Set(this.dataList.map(item => item.associate))];
 
    this.distinctPackage = [...new Set(this.dataList.map(item => item.package))];
  
   
    this.filteredProject = [...this.distinctProject]; 
    this.filteredPartner = [...this.distinctPartner]; 
    this.filteredAssociate = [...this.distinctAssociate]; 
 
    this.filteredPackage = [...this.distinctPackage];
   
  }

  clearSelection(type: string) {
    if (type === 'project') {
        this.selectedProject = [];
    } else if (type === 'partner') {
        this.selectedPartner = [];
    } 
    else if (type === 'associate') {
        this.selectedAssociate = [];
    } 
    else if (type === 'status') {
        this.selectedAssociate = [];
    } 
    else if (type === 'package') {
        this.selectedPackage = [];
    }
    this.applyFilters(); // Apply filters after clearing the selection
}


resetFilter() {
  this.selectedAssociate = [];
  this.selectedPartner = [];
  this.selectedProject = [];
  
  this.selectedPackage = [];
  this.dataList = [...this.originalDataList]; // Restore full list
  this.calculateTotal(this.dataList);
}



}
