<mcv-header [title]="headerTitle"></mcv-header>
<div class="page-wrapper no-overflow">
    <div class="mat-table-wrapper">

        <mat-table mat-table [dataSource]="dataSource" matSort matSortActive="billDate" matSortDirection="desc"
            matSortDisableClear>

            <ng-container matColumnDef="project">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Project</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.projectCode}} {{item.project}}</mat-cell>
                <mat-footer-cell *matFooterCellDef><strong>Total</strong></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="projectStatus">
                <mat-header-cell *matHeaderCellDef mat-sort-header>ProjectStatus</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.projectStatus}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="partner">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Partner</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.partner}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="client">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Client</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.client}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="workCompletion">
                <mat-header-cell *matHeaderCellDef>Work</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.workCompletion/100 | percent:'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="billDate">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Invoice Date</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.billDate | date:'dd MMM y'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="billNo">
                <mat-header-cell *matHeaderCellDef>Invoice No</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.billNo || 'PROFORMA'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="totalAmount">
                <mat-header-cell *matHeaderCellDef>Total Fee</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.totalAmount | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef matTooltip="{{ total?.totalAmount | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.totalAmount | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="totalReceived">
                <mat-header-cell *matHeaderCellDef>Total Fee Received</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.totalReceived | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef
                    matTooltip="{{ total?.totalReceived | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.totalReceived | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="currentAmount">
                <mat-header-cell *matHeaderCellDef>Bill Amount</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.currentAmount | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef
                    matTooltip="{{ total?.currentAmount | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.currentAmount | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="currentRecieved">
                <mat-header-cell *matHeaderCellDef>Payment Recieved</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.currentRecieved | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef
                    matTooltip="{{ total?.currentRecieved | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.currentRecieved | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="score">
                <mat-header-cell *matHeaderCellDef>Bill Amount Recieved</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.score | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef matTooltip="{{ total?.score | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.score | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="paymentDate">
                <mat-header-cell *matHeaderCellDef>Payment Date</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.paymentDate | date:'dd MMM y'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
            </ng-container>



            <ng-container matColumnDef="igstAmount">
                <mat-header-cell *matHeaderCellDef>IGST</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.igstAmount | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef matTooltip="{{ total?.igstAmount | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.igstAmount | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="cgstAmount">
                <mat-header-cell *matHeaderCellDef>CGST</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.cgstAmount | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef matTooltip="{{ total?.cgstAmount | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.cgstAmount | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <ng-container matColumnDef="sgstAmount">
                <mat-header-cell *matHeaderCellDef>SGST</mat-header-cell>
                <mat-cell *matCellDef="let item">{{item.sgstAmount | currency:'INR':'symbol':'1.0'}}</mat-cell>
                <mat-footer-cell *matFooterCellDef matTooltip="{{ total?.sgstAmount | currency:'INR':'symbol':'1.0'}}">
                    <strong>{{ total?.sgstAmount | currency:'INR':'symbol':'1.0'}}</strong>
                </mat-footer-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns();sticky: true"></mat-header-row>

            <mat-row *matRowDef="let row; columns: displayedColumns()" (click)="onRowClick(row)"
                [ngClass]="{'bg-primary': selectedRow==row,'text-white': selectedRow==row}">
            </mat-row>
            <mat-footer-row *matFooterRowDef="displayedColumns();sticky: true"></mat-footer-row>
        </mat-table>

    </div>

</div>


<app-footer>
    <div class="nav-footer-actions">


        <form class="form-inline" (ngSubmit)="search()" novalidate autocomplete="off">
            <input  class="form-control mr-2 " name="search" [(ngModel)]="searchFish"
                placeholder="Search for..." matTooltip="Search" aria-label="Search">
        </form>
        <button mat-icon-button (click)="refresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>
        <button mat-raised-button *ngFor="let item of typeOptions"
            [color]="isFilter('typeFlag',item.value.toString()) ? 'accent':''" (click)="toggleType(item.value)"
            [matTooltip]="item.title" [attr.aria-label]="item.title">
            {{item.title}}
        </button>

        <!-- <div [formGroup]="form" autocomplete="off" >
            <mat-form-field appearance="outline">
                <mat-select formControlName="typeFilter" multiple placeholder="Type">
                    <mat-option *ngFor="let item of typeOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div> -->
        <div *ngIf="companyOptions && companyOptions.length > 0">
            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company" multiple>
                    <mat-option *ngFor="let item of companyOptions" [value]="item">{{ item.title }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div *ngIf="statusOptions && statusOptions.length > 0">
            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor=" let item of statusOptions" [value]="item.value">{{
                        item.title
                        }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div [formGroup]="form" autocomplete="off">
            <mat-form-field appearance="outline">
                <mat-label>Date From</mat-label>
                <input  placeholder="Date From" aria-label="Date From" matInput
                    formControlName="startDateFilter" [matDatepicker]="pickerStartDate" readonly>
                <mat-datepicker-toggle matSuffix [for]="pickerStartDate"></mat-datepicker-toggle>
                <mat-datepicker [touchUi]="isMobileView" #pickerStartDate></mat-datepicker>
            </mat-form-field>
        </div>
        <div [formGroup]="form" autocomplete="off">
            <mat-form-field appearance="outline">
                <mat-label>Date To</mat-label>
                <input  placeholder="Date To" aria-label="Date To" matInput formControlName="endDateFilter"
                    [matDatepicker]="pickerEndDate" readonly>
                <mat-datepicker-toggle matSuffix [for]="pickerEndDate"></mat-datepicker-toggle>
                <mat-datepicker [touchUi]="isMobileView" #pickerEndDate></mat-datepicker>
            </mat-form-field>
        </div>
        <!-- <button mat-raised-button 
            [ngClass]="{'btn-light':!filters || filters.length==0,'btn-warning':filters && filters.length!=0 }"
             (click)="openFilters()" matTooltip="filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button> -->

        <!-- <button mat-raised-button   (click)="exportExcel()" matTooltip="export excel"
            aria-label="export excel">Export Excel</button> -->
    </div>

    <div class="nav-footer-mobile-actions">
        <button mat-icon-button (click)="refresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>

        <button mat-icon-button class="btn btn-dark" appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>
    </div>

    <div class="nav-filters">
        <div class="inline-list">
            <form class="form-inline " (ngSubmit)="search()" novalidate autocomplete="off">
                <input  class="form-control" name="search" [(ngModel)]="searchFish"
                    placeholder="Search for..." matTooltip="Search" aria-label="Search">
            </form>
        </div>
    </div>
</app-footer>