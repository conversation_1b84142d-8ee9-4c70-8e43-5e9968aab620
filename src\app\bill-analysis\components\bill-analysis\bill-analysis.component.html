<div class="data-filter-row">
    <div class="filter-header"
        *ngIf="selectedProjects.length > 0 || selectedProjectStatus.length > 0 || selectedPartner.length > 0 || selectedClient.length > 0">
        <h6 class="font-focused ">Filters:</h6>
    </div>
    <h6 *ngIf="selectedProjects.length > 0">
        <b>Project:</b> {{selectedProjects.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('project')">✖</span>
    </h6>
    <h6 *ngIf="selectedProjectStatus.length > 0">
        <b>Project Status:</b> {{selectedProjectStatus.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('projectStatus')">✖</span>
    </h6>
    <h6 *ngIf="selectedPartner.length > 0">
        <b>Partner:</b> {{selectedPartner.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('partner')">✖</span>
    </h6>
    <h6 *ngIf="selectedClient.length > 0">
        <b>Client:</b> {{selectedClient.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('client')">✖</span>
    </h6>
    <h6 (click)="resetFilter()"
        *ngIf="selectedProjects.length > 0 || selectedProjectStatus.length > 0 || selectedPartner.length > 0 || selectedClient.length > 0">
        <b> Clear All</b>

    </h6>



</div>
<div class="bill-analysis-wrapper">


    <table>
        <thead>
            <tr>
                <th mat-button [matMenuTriggerFor]="projectMenu" [ngClass]="{'filter': selectedProjects.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Project</h6>
                        <mat-icon>filter_alt</mat-icon>
                        <mat-menu #projectMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="projectKeySearch"
                                        (input)="filterDistinctProjects()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('project')">
                                {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let project of filteredProjects"
                                (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                                <mat-checkbox [checked]="selectedProjects.includes(project)">{{ project
                                    }}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>

                </th>
                <th mat-button [matMenuTriggerFor]="projectStatusMenu"
                    [ngClass]="{'filter': selectedProjectStatus.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Project Status</h6>
                        <mat-icon>filter_alt</mat-icon>
                        <mat-menu #projectStatusMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="projectStatusKeySearch"
                                        (input)="filterDistinctProjectStatus()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('projectStatus')">
                                {{ isAllSelected('projectStatus') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let projectStatus of filteredProjectStatus"
                                (click)="$event.stopPropagation(); toggleSelection(projectStatus, 'projectStatus')">
                                <mat-checkbox [checked]="selectedProjectStatus.includes(projectStatus)">{{
                                    projectStatus}}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>
                </th>
                <th mat-button [matMenuTriggerFor]="partnerMenu" [ngClass]="{'filter': selectedPartner.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Partner</h6>
                        <mat-icon>filter_alt</mat-icon>
                        <mat-menu #partnerMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="partnerKeySearch"
                                        (input)="filterDistinctPartner()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('partner')">
                                {{ isAllSelected('projectStatus') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let partner of filteredPartner"
                                (click)="$event.stopPropagation(); toggleSelection(partner, 'partner')">
                                <mat-checkbox [checked]="selectedPartner.includes(partner)">{{ partner}}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>

                </th>
                <th mat-button [matMenuTriggerFor]="clientMenu" [ngClass]="{'filter': selectedClient.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Client</h6>
                        <mat-icon>filter_alt</mat-icon>
                        <mat-menu #clientMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="clientKeySearch"
                                        (input)="filterDistinctClient()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('client')">
                                {{ isAllSelected('client') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let client of filteredClient"
                                (click)="$event.stopPropagation(); toggleSelection(client, 'client')">
                                <mat-checkbox [checked]="selectedClient.includes(client)">{{ client}}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'workCompletion'}">
                    <div class="analysis-table-header" (click)="sortData('workCompletion')">
                        <h6>Work</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'workCompletion' && sortState['workCompletion'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'workCompletion' && sortState['workCompletion'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'workCompletion' || !sortState['workCompletion'] ">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'billDate'}">
                    <div class="analysis-table-header" (click)="sortData('billDate')">
                        <h6>Invoice Date</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'billDate' && sortState['billDate'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'billDate' && sortState['billDate'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'billDate' || !sortState['billDate']">import_export</mat-icon>
                    </div>

                </th>
                <th>Invoice No</th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'totalAmount'}">

                    <div class="analysis-table-header" (click)="sortData('totalAmount')">
                        <h6>Total Fee</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'totalAmount' && sortState['totalAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'totalAmount' && sortState['totalAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'totalAmount' || !sortState['totalAmount']">import_export</mat-icon>
                    </div>

                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'totalReceived'}">
                    <div class="analysis-table-header" (click)="sortData('totalReceived')">
                        <h6>Total Fee Received</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'totalReceived' && sortState['totalReceived'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'totalReceived' && sortState['totalReceived'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'totalReceived' || !sortState['totalReceived']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'currentAmount'}">
                    <div class="analysis-table-header" (click)="sortData('currentAmount')">
                        <h6>Bill Amount</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'currentAmount' && sortState['currentAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'currentAmount' && sortState['currentAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'currentAmount' || !sortState['currentAmount']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'currentRecieved'}">
                    <div class="analysis-table-header" (click)="sortData('currentRecieved')">
                        <h6>Payment Recieved</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'currentRecieved' && sortState['currentRecieved'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'currentRecieved' && sortState['currentRecieved'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'currentRecieved' || !sortState['currentRecieved']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'score'}">
                    <div class="analysis-table-header" (click)="sortData('score')">
                        <h6>Bill Amount Recieved</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'score' && sortState['score'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'score' && sortState['score'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'score' || !sortState['score']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'igstAmount'}">
                    <div class="analysis-table-header" (click)="sortData('igstAmount')">
                        <h6>IGST</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'igstAmount' && sortState['igstAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'igstAmount' && sortState['igstAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'igstAmount' || !sortState['igstAmount']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'cgstAmount'}">
                    <div class="analysis-table-header" (click)="sortData('cgstAmount')">
                        <h6>CGST</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'cgstAmount' && sortState['cgstAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'cgstAmount' && sortState['cgstAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'cgstAmount' || !sortState['cgstAmount']">import_export</mat-icon>
                    </div>

                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'sgstAmount'}">
                    <div class="analysis-table-header" (click)="sortData('sgstAmount')">
                        <h6>SGST</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'sgstAmount' && sortState['sgstAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'sgstAmount' && sortState['sgstAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'sgstAmount' || !sortState['sgstAmount']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'paymentDate'}">
                    <div class="analysis-table-header" (click)="sortData('paymentDate')">
                        <h6> Payment Date</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'paymentDate' && sortState['paymentDate'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'paymentDate' && sortState['paymentDate'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'paymentDate' || !sortState['paymentDate']">import_export</mat-icon>
                    </div>
                </th>
            </tr>
            <tr class="total">
                <td colspan="7">Total</td>
                <td class="text-right">{{ total?.totalAmount | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.totalReceived | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.currentAmount | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.currentRecieved | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.score | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.igstAmount | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.cgstAmount | currency:'INR':'symbol':'1.0'}}</td>
                <td class="text-right">{{ total?.sgstAmount | currency:'INR':'symbol':'1.0'}}</td>
                <td></td>

            </tr>
        </thead>
        <tbody *ngIf="dataList">
            <ng-container *ngFor="let item of dataList; let i=index">
                <tr>
                    <td>{{item.projectCode}} {{item.project}}</td>
                    <td>{{item.projectStatus}}</td>
                    <td>{{item.partner}}</td>
                    <td>{{item.client}}</td>
                    <td>{{item.workCompletion/100 | percent:'1.0'}}</td>
                    <td>{{item.billDate | date:'dd MMM y'}}</td>
                    <td>{{item.billNo || 'PROFORMA'}}</td>
                    <td>{{item.totalAmount | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.totalReceived | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.currentAmount | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.currentRecieved | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.score | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.igstAmount | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.cgstAmount | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.sgstAmount | currency:'INR':'symbol':'1.0'}}</td>
                    <td>{{item.paymentDate | date:'dd MMM y'}}</td>



                </tr>
            </ng-container>
        </tbody>
        <!-- <mat-header-row *matHeaderRowDef="displayedColumns();sticky: true"></mat-header-row> -->

        <!-- <mat-row *matRowDef="let row; columns: displayedColumns()" (click)="onRowClick(row)"
                [ngClass]="{'bg-primary': selectedRow==row,'text-white': selectedRow==row}">
            </mat-row>
            <mat-footer-row *matFooterRowDef="displayedColumns();sticky: true"></mat-footer-row> -->
    </table>



</div>


<app-footer>
    <div class="nav-footer-actions">


        <!-- <form class="form-inline" (ngSubmit)="search()" novalidate autocomplete="off">
            <input  class="form-control mr-2 " name="search" 
                placeholder="Search for..." matTooltip="Search" aria-label="Search">
        </form> -->
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

        </div>
        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>



        <button mat-raised-button *ngFor="let item of typeOptions"
            [color]="isFilter('typeFlag',item.value.toString()) ? 'accent':''" (click)="toggleType(item.value)"
            [matTooltip]="item.title" [attr.aria-label]="item.title">
            {{item.title}}
        </button>

        <!-- <div [formGroup]="form" autocomplete="off" >
            <mat-form-field appearance="outline">
                <mat-select formControlName="typeFilter" multiple placeholder="Type">
                    <mat-option *ngFor="let item of typeOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div> -->

    </div>

    <div class="nav-footer-mobile-actions">


        <button mat-icon-button class="btn btn-dark" appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>
    </div>

    <div class="nav-filters">
        <div class="inline-list">
            <!-- <form class="form-inline " (ngSubmit)="search()" novalidate autocomplete="off">
                <input  class="form-control" name="search" [(ngModel)]="searchFish"
                    placeholder="Search for..." matTooltip="Search" aria-label="Search">
            </form> -->
            <div *ngIf="companyOptions && companyOptions.length > 0">
                <mat-form-field appearance="outline">
                    <mat-select [formControl]="companyFC" placeholder="Company" multiple>
                        <mat-option *ngFor="let item of companyOptions" [value]="item">{{ item.title }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div *ngIf="statusOptions && statusOptions.length > 0">
                <mat-form-field appearance="outline">
                    <mat-select [formControl]="statusFC" multiple placeholder="Status">
                        <mat-option *ngFor=" let item of statusOptions" [value]="item.value">{{
                            item.title
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <mat-form-field appearance="outline">
                <mat-label>Select Range</mat-label>
                <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                    <input matStartDate placeholder="Start date" formControlName="start" readonly>
                    <input matEndDate placeholder="End date" formControlName="end" readonly>
                </mat-date-range-input>
                <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
                <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
            </mat-form-field>
            <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
        </div>
    </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav>
    <div *ngIf="companyOptions && companyOptions.length > 0">
        <mat-form-field appearance="outline">
            <mat-select [formControl]="companyFC" placeholder="Company" multiple>
                <mat-option *ngFor="let item of companyOptions" [value]="item">{{ item.title }}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div *ngIf="statusOptions && statusOptions.length > 0">
        <mat-form-field appearance="outline">
            <mat-select [formControl]="statusFC" multiple placeholder="Status">
                <mat-option *ngFor=" let item of statusOptions" [value]="item.value">{{
                    item.title
                    }}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <mat-form-field appearance="outline">
        <mat-label>Select Range</mat-label>
        <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
            <input matStartDate placeholder="Start date" formControlName="start" readonly>
            <input matEndDate placeholder="End date" formControlName="end" readonly>
        </mat-date-range-input>
        <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
        <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
    </mat-form-field>



</app-mcv-filter-sidenav>