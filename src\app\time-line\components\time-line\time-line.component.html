<mcv-header [title]="headerTitle"></mcv-header>
<div class="page-wrapper" style="overflow: hidden;">
  <!-- <mcv-time-line [start]="TIMELINE_START_TIME" [end]="TIMELINE_END_TIME" [events]="events" [resources]="resources"
    (rangeChange)="onRangeChange($event)" (eventClick)="onEventClick($event)" (slotSelection)="onSlotSelection($event)"
    (refresh)="onTimeLineRefresh($event)">
  </mcv-time-line> -->
  <!-- 
  <app-mcv-time-line-view [componentFlag]="'maintimeline'" (contactsEmitter)="handleContacts($event)"
    [contactsList]=" filteredContactList" [searchTerm]="searchValue"></app-mcv-time-line-view> -->
  <app-mcv-time-line-view [componentFlag]="'maintimeline'" (contactsEmitter)="handleContacts($event)" (associateListEmitter)="getAssociateList($event)" (partnerListEmitter)="getPartnerList($event)"
    [contactsList]=" filteredContactList" [searchTerm]="searchValue"></app-mcv-time-line-view>
</div>
<app-footer>

  <div class="nav-footer-actions ">
    <div *ngIf="groupList && groupList.length>0">
      <mat-form-field appearance="outline" class="col-md-12">
        <mat-select [formControl]="group" placeholder="Groups" (selectionChange)="onGroupSelect($event.value)">
          <mat-option (click)="clearSelection()">
            <span>Clear Selection</span><mat-icon>clear</mat-icon>
          </mat-option>
          <mat-option *ngFor="let group of groupList" [value]="group">
            <span>{{ group.title | uppercase}}</span>
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="search-wrapper">
      <mat-icon matPrefix>search</mat-icon>
      <input aria-label="Enter project name and select" [formControl]="searchFC" matInpung t />
      <mat-icon matSuffix (click)="searchFC.setValue('')" class="clear">clear</mat-icon>
    </div>

    <div class="nav-filters">
      <div class="inline-list">
        <div>
        </div>
        <button mat-raised-button color="primary" (click)="openGroupDialog()">
          <mat-icon>group</mat-icon> Groups
        </button>
      </div>
    </div>
    <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>
  </div>

  <div class="nav-footer-mobile-actions">
    <div class="mobile-actions">

      <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
        <mat-icon>filter_list</mat-icon>
      </button>
      <button mat-raised-button color="primary" (click)="openGroupDialog()">
        <mat-icon>group</mat-icon> Groups
      </button>

    </div>


  </div>
  <div class="nav-filters">
    <div class="inline-list">

      <div class="search-wrapper">
        <mat-icon matPrefix>search</mat-icon>
        <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        <mat-icon matSuffix (click)="searchFC.setValue('')" class="clear">clear</mat-icon>
      </div>

      <mat-form-field appearance="outline" *ngIf="groupList && groupList.length>0">
        <mat-select [formControl]="group" placeholder="Groups" (selectionChange)="onGroupSelect($event.value)">
          <mat-option (click)="clearSelection()">
            <span>Clear Selection</span><mat-icon>clear</mat-icon>
          </mat-option>
          <mat-option *ngFor="let group of groupList" [value]="group">
            <span>{{ group.title | uppercase}}</span>
          </mat-option>
        </mat-select>
      </mat-form-field>



    </div>


  </div>

  <!-- <app-mcv-filter-sidenav #sidenav (refreshFiltersEvent)="refreshFilters()"> -->
 
</app-footer>

<app-mcv-filter-sidenav #sidenav >

  <div *ngIf="partnerOptions && partnerOptions.length > 0">
        <mat-form-field appearance="outline">
          <mat-select [formControl]="partnerFC" multiple placeholder="Project Partners">
            <mat-option *ngFor="let item of partnerOptions" [value]="item">
              {{item.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
  <div *ngIf="associateOptions && associateOptions.length > 0">
        <mat-form-field appearance="outline">
          <mat-select [formControl]="associateFC" multiple placeholder="Project Associates">
            <mat-option *ngFor="let item of associateOptions" [value]="item">
              {{item.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
          <!-- <div>
        <mat-form-field appearance="outline">
          <mat-select [formControl]="groupByFC" placeholder="Group By">
            <mat-option *ngFor="let item of groupByOptions" [value]="item">{{item.label}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    
      <div>
        <mat-form-field appearance="outline">
          <mat-select [formControl]="entityFC" placeholder="Meeting Type">
            <mat-option *ngFor="let item of entityOptions" [value]="item">{{item.label}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    
      <div>
        <mat-form-field appearance="outline">
          <mat-select [formControl]="partnerFC" placeholder="Project Partner">
            <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
     -->
      </app-mcv-filter-sidenav>