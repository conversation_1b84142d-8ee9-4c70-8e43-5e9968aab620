{"name": "newarch-v5", "version": "********", "scripts": {"ng": "ng", "start": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng serve", "build": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build", "build-prod": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --aot --base-href=/newarch/  --output-path=dist/prod", "build-staging": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build -c=staging --aot --base-href=/newarch-staging/  --output-path=dist/staging", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@angular/service-worker": "^16.2.12", "@azure/abort-controller": "^2.1.2", "@azure/storage-blob": "^12.18.0", "@fullcalendar/angular": "^6.1.10", "@fullcalendar/core": "^6.1.13", "@fullcalendar/daygrid": "^6.1.13", "@fullcalendar/list": "^6.1.13", "@fullcalendar/timegrid": "^6.1.13", "bootstrap": "^4.6.2", "chart.js": "^4.4.3", "cropro": "^1.6.0", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "luxon": "^3.4.4", "markerjs2": "^2.32.1", "ng2-charts": "^5.0.4", "ngx-device-detector": "^6.0.2", "ngx-doc-viewer": "^15.0.1", "ngx-mat-select-search": "^8.0.0", "ngx-material-timepicker": "^13.1.1", "qrcode": "1.5.3", "rxjs": "~7.8.0", "smoothscroll-polyfill": "^0.4.4", "sweetalert2": "^11.11.0", "tippy.js": "^6.3.7", "tslib": "^2.3.0", "uuid": "^9.0.1", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.13", "@angular/cli": "~16.2.13", "@angular/compiler-cli": "^16.2.12", "@types/file-saver": "^2.0.7", "@types/hammerjs": "^2.0.45", "@types/jasmine": "~5.1.4", "@types/lodash": "^4.17.0", "@types/node": "^20.11.17", "@types/qrcode": "1.5.0", "@types/uuid": "^9.0.8", "file-saver": "^2.0.5", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ts-node": "~5.0.1", "typescript": "~5.1.6", "webpack": "^5.88.2", "webpack-dev-server": "^4.15.1"}}