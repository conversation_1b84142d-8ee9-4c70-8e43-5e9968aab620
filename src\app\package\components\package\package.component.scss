@import 'variables';

.package-studio-assessed-value {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    h6{
      font-size: 0.8rem;
      font-weight: 500;
    }

}

.agenda-list {
    list-style: none;
    margin: 0;
    padding: 0.3rem 0;

    &>li {
        box-sizing: border-box;
    }
}

.package-playlist-wrapper {
    border: 1px solid lightgray;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.1rem;
    margin-bottom: 0.2rem;

    .handle-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    h6 {
        padding: 0.3rem;
    }

    .title {
        font-size: 0.8rem;
        font-weight: 600;
    }

    .description {
        font-size: 0.7rem;
    }
}

mat-icon {
    cursor: pointer;
}

.cdk-drag-handle {
    color: $gray-600;
    cursor: move;
    height: 35px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cdk-drag-preview {
    padding: 0.3rem 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px $gray-400 solid;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);
    background-color: $gray-100;
    border-radius: $border-radius;
}


.studiowork-deliverables-ul {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
        border: 1px solid $gray-300;
        border-radius: $border-radius;
        padding: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.2rem;
    }
}

.project-detail-progress-wrapper {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 1;

    .mcv-progress-group {
        height: 1rem;
        display: flex;
        overflow: hidden;
        background-color: $gray-200;

        .mcv-progress-bar {
            display: flex;
            gap: 0.2rem;
            /* flex-direction: column; */
            justify-content: flex-end;
            align-items: center;
            overflow: hidden;
            color: #fff;
            /* text-align: center; */
            white-space: nowrap;
            background-color: #003a5f;
            transition: width 0.6s ease;
            font-size: 0.7rem;
            font-weight: 600;

        }
    }
}

.progress-progress-title {
    font-size: 0.8rem;
    font-weight: 700;
    padding-left: 0.2rem;
  }
