<div class="title-bar" *ngIf="!isCreateMode && !isTaskMode">
  <h5 class="title-bar-item">{{ currentEntity.title }}</h5>
  <h6 class="title-bar-item">{{currentEntity.projectTitle}}</h6>
  <div>
    <small>Submission Date</small>
    <h6 class="title-bar-item">
      {{ currentEntity.finalDate | date: "dd MMM y HH:mm" }}
    </h6>
  </div>
  <div>
    <small>Active Stage</small>
    <h6 class="title-bar-item">
      {{ currentEntity.activeStage }}
    </h6>
  </div>
</div>

<app-wftask-title-bar [task]="task" *ngIf="isTaskMode"></app-wftask-title-bar>


<div class="container-fluid">
  <div class="row">
    <!-- <div class="col-md-12" *ngIf="isTaskMode">
      <mat-expansion-panel [expanded]="true">
        <mcv-time-entry-time-line #timeline
          [readonly]="task.stageIndex==taskConstants.STAGEINDEX.BRAINSTORMING || task.stageIndex==taskConstants.STAGEINDEX.STUDIOWORK"
          [wfTask]="task" (created)="onUpdateTimeEntries($event)" (updated)="onUpdateTimeEntries($event)"
          (deleted)="onUpdateTimeEntries($event)"></mcv-time-entry-time-line>
      </mat-expansion-panel>
    </div> -->
    <div class="col-md-7">
      <mat-expansion-panel [expanded]="true"
        *ngIf=" (isTaskMode && task && task?.stageIndex == taskConstants.STAGEINDEX.STUDIOWORK)">
        <mat-expansion-panel-header>
          <mat-panel-title> Studio Task Details </mat-panel-title>
        </mat-expansion-panel-header>
        <app-package-studio-task [item]="task" [timeEntries]="task.timeEntries" (delete)="deleteStudioTask($event)"
          (update)="updateStudioTask($event)">
        </app-package-studio-task>
      </mat-expansion-panel>


      <mat-expansion-panel  *ngIf="((projectFlowData && isPermissionMaster) || isCurrentUserPartner)">
        <mat-expansion-panel-header>
          <mat-panel-title>Project Progress</mat-panel-title>
        </mat-expansion-panel-header>
        <div class="pb-2" *ngIf="projectFlowData && isPermissionMaster || isCurrentUserPartner">
          <!-- <h6 class="progress-progress-title mb-1 text-muted">Project Progress</h6> -->
          <!-- <app-package-progress-bar [title]="'Project VHr %'" [valueLabel]="'Consumed'"
                    [value]="totalProjectConsumedVHr" [totalLabel]="'Total %'" [total]="totalProjectVHr"
                    *ngIf="isPermissionMaster || isCurrentUserPartner"></app-package-progress-bar> -->
          <div class="project-detail-progress-wrapper" (click)="openProgressDialog()">
            <div class="mcv-progress-group">
              <div class="mcv-progress-bar" *ngIf="projectFlowData.vHrCostPercentage > 0"
                [style.width]="projectFlowData.vHrCostPercentage + '%'" [style.background-color]="VHR_CONSUMED_COLOR"
                [attr.aria-valuenow]="projectFlowData.vHrCostPercentage" aria-valuemin="0" aria-valuemax="100"
                matTooltip="({{projectFlowData.vHrCostPercentage | number:'1.0-1'}}%) VHrCost: {{projectFlowData.vHrCost | currency:'INR':'symbol':'1.0-0'}}">
                <span>{{projectFlowData.vHrCostPercentage | number:'1.0-1'}}%</span>
              </div>
        
              <ng-container *ngIf="projectFlowData.xCostPercentage > 0">
                <div class="mcv-progress-bar" [style.background-color]="VHR_X_COLOR"
                  [style.width]="(projectFlowData.xCostPercentage - projectFlowData.vHrCostPercentage) + '%'"
                  [attr.aria-valuenow]="(projectFlowData.xCostPercentage-projectFlowData.vHrCostPercentage)" aria-valuemin="0"
                  aria-valuemax="100"
                  matTooltip="({{(projectFlowData.xCostPercentage-projectFlowData.vHrCostPercentage |
                                              number:'1.0-1')}}%) xCost: {{projectFlowData.xCost | currency:'INR':'symbol':'1.0-0'}}">
                  <span>{{(projectFlowData.xCostPercentage-projectFlowData.vHrCostPercentage |
                    number:'1.0-1')}}%</span>
                  <small>of {{projectFlowData.xCostPercentage | number:'1.0-1'}}%</small>
                </div>
              </ng-container>
            </div>
            <div class="mcv-progress-group">
              <div class="mcv-progress-bar" *ngIf="projectFlowData.totalPaymentPercentage > 0"
                [style.background-color]="PAYMENT_RECEIVED_COLOR" [style.width]="projectFlowData.totalPaymentPercentage + '%'"
                [attr.aria-valuenow]="projectFlowData.totalPaymentPercentage" aria-valuemin="0" aria-valuemax="100"
                matTooltip="({{projectFlowData.totalPaymentPercentage | number:'1.0-1'}}%) TotalPaymentAmount: {{projectFlowData.totalPaymentAmount | currency:'INR':'symbol':'1.0-0'}}">
                {{projectFlowData.totalPaymentPercentage | number:'1.0-1'}}%</div>
        
              <ng-container *ngIf="projectFlowData.proformaPercentage > 0">
                <div class="mcv-progress-bar" [style.width]="projectFlowData.proformaPercentage + '%'"
                  [style.background-color]="PAYMENT_DUE_COLOR" [attr.aria-valuenow]="projectFlowData.proformaPercentage"
                  aria-valuemin="0" aria-valuemax="100"
                  matTooltip="({{projectFlowData.proformaPercentage | number:'1.0-1'}}%) ProformaAmount: {{projectFlowData.proformaAmount | currency:'INR':'symbol':'1.0-0'}}">
        
                  <span>{{(projectFlowData.proformaPercentage) | number:'1.0-1'}}%</span>
                  <small>of {{(projectFlowData.totalPaymentPercentage+projectFlowData.proformaPercentage) |
                    number:'1.0-1'}}%</small>
                </div>
              </ng-container>
            </div>
        
            <div class="mcv-progress-group">
              <div class="mcv-progress-bar" [style.background-color]="PACKAGE_COMPLETED_COLOR"
                [style.width]="projectFlowData.completedPercentage + '%'" [attr.aria-valuenow]="projectFlowData.completedPercentage"
                aria-valuemin="0" aria-valuemax="100"
                matTooltip="({{projectFlowData.completedPercentage | number:'1.0-1'}}%) Completed">
                <span>{{projectFlowData.completedPercentage | number:'1.0-1'}}%</span>
              </div>
        
              <div class="mcv-progress-bar" [style.background-color]="PACKAGE_ACTIVE_COLOR"
                [style.width]="projectFlowData.activePercentage + '%'" [attr.aria-valuenow]="projectFlowData.activePercentage"
                aria-valuemin="0" aria-valuemax="100"
                matTooltip="({{(projectFlowData.activePercentage) | number:'1.0-1'}}%) Active">
                <span>{{(projectFlowData.activePercentage) | number:'1.0-1'}}%</span>
                <small>of
                  {{(projectFlowData.completedPercentage+projectFlowData.activePercentage+projectFlowData.proposedPercentage)
                  | number:'1.0-1'}}%</small>
              </div>
        
              <div class="mcv-progress-bar" [style.background-color]="PACKAGE_PROPOSED_COLOR"
                [style.width]="projectFlowData.proposedPercentage + '%'" [attr.aria-valuenow]="projectFlowData.proposedPercentage"
                aria-valuemin="0" aria-valuemax="100"
                matTooltip="({{(projectFlowData.proposedPercentage) | number:'1.0-1'}}%) Proposed">
                <span>{{(projectFlowData.proposedPercentage) | number:'1.0-1'}}%</span>
                <small>of
                  {{(projectFlowData.completedPercentage+projectFlowData.activePercentage+projectFlowData.proposedPercentage)
                  | number:'1.0-1'}}%</small>
              </div>
            </div>
          </div>
        </div>

        </mat-expansion-panel>


      <mat-expansion-panel [expanded]="true" *ngIf="(isPermissionShowAll || isCurrentUserPartner || isCurrentUserAssociate) && (!isTaskMode || (isTaskMode && task?.stageIndex != taskConstants.STAGEINDEX.STUDIOWORK))">
        <mat-expansion-panel-header>
          <mat-panel-title> Package Details</mat-panel-title>
        </mat-expansion-panel-header>

        <app-package-vhr-progress-bar [title]="'Assigned VHr'" [vhrAssigned]="true" [valueLabel]="''"
        [totalVHrAssigned]="totalVHrAssigned" [mode]="'slim'" [packageVhr]="packageVhr">
      </app-package-vhr-progress-bar>

      <app-package-vhr-progress-bar [title]="'Assessed VHr'" [vhrAssessed]="true" [valueLabel]="''"
        [totalVHrAssigned]="totalVHrAssigned" [totalVHrAssessed]="totalVHrAssessed" [mode]="'slim'"
        [packageVhr]="packageVhr">
      </app-package-vhr-progress-bar>
     
        <!-- <app-package-vhr-progress-bar [title]="'Consumed VHr'" [vhrConsumed]="true" [valueLabel]="''"
          [totalVHrConsumed]="totalVHrConsumed" [mode]="'slim'" [packageVhr]="packageVhr">
        </app-package-vhr-progress-bar> -->

        <form [formGroup]="form" autocomplete="off">
          <div class="row">
            <div class="col-md-4 d-flex align-items-center">
              <mat-form-field appearance="outline"  *ngIf="allowEdit">
                <mat-label>VHr (Value Hours)</mat-label>
                <input type="number" matInput formControlName="valueHours" placeholder="Enter Value Hours" min="0"
                  step="1" style="text-align: left;" />
                <mat-error>{{ getErrorMessage(f.valueHours) }}</mat-error>
                <!-- <mat-hint align="end">Can be updated during Brainstorming only!</mat-hint> -->
              </mat-form-field>
              <button mat-icon-button *ngIf="allowEdit" (click)="onUpdatePackageVHr()">
                <mat-icon>done</mat-icon>
              </button>
            </div>

            <mat-form-field appearance="outline" class="col-md-4">
              <mat-label>Start Date</mat-label>
              <input  id="startDate" placeholder="Start Date" aria-label="Start Date" matInput
                formControlName="startDate" [matDatepickerFilter]="dateFilter" [matDatepicker]="pickerStartDate"
                readonly>
              <mat-datepicker-toggle matSuffix [for]="pickerStartDate"></mat-datepicker-toggle>
              <mat-datepicker [touchUi]="isMobileView" #pickerStartDate></mat-datepicker>
              <mat-error>{{getErrorMessage(f.startDate)}}</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="col-md-4">
              <mat-label>Final Date</mat-label>
              <input  id="finalDate" placeholder="Final Date" aria-label="Final Date" matInput
                formControlName="finalDate" [matDatepickerFilter]="dateFilter" [matDatepicker]="pickerFinalDate"
                readonly>
              <mat-datepicker-toggle matSuffix [for]="pickerFinalDate"></mat-datepicker-toggle>
              <mat-datepicker [touchUi]="isMobileView" #pickerFinalDate></mat-datepicker>
              <mat-error>{{getErrorMessage(f.finalDate)}}</mat-error>
              <!-- <mat-hint align="end">Require Special Permissions. Due dates for all tasks affected.</mat-hint> -->
            </mat-form-field>

            <div class="clear-fix"></div>
          </div>
        </form>

        <div class="row">
          <form class="col-6" [formGroup]="form" autocomplete="off"
            *ngIf="isPermissionMaster && getFilteredAssociates(currentEntity.associations,0).length==0">
            <mat-form-field appearance="outline" class="col-12 col-md-12">
              <mat-label>Select Partner</mat-label>
              <input placeholder="Enter name of person to select from the list" aria-label="Select Associate" matInput
                formControlName="contact" [matAutocomplete]="autoContact2" />

              <mat-autocomplete #autoContact2="matAutocomplete" [displayWith]="displayFnContact"
                (optionSelected)="onSelectContact($event,0)">
                <mat-option *ngFor="let option of filteredContacts$ | async" [value]="option">
                  {{ option.name }}
                </mat-option>
              </mat-autocomplete>
              <mat-error>{{ getErrorMessage(f.contact) }}</mat-error>
            </mat-form-field>
          </form>
          <ul class="col-sm-6 col-12 simple-list-hover"
            *ngIf="getFilteredAssociates(currentEntity.associations,0).length!==0">
            <li *ngFor="let item of getFilteredAssociates(currentEntity.associations,0); let i=index">
              <app-package-association-contact [item]="item" [showPhoto]="true"
                [showRemove]="false" (remove)="onDeleteAssociate(item)">
              </app-package-association-contact>
            </li>
          </ul>

          <!-- Package Associates  -->
          <form class="col-6" form [formGroup]="form" autocomplete="off" novalidate
            *ngIf=" !isReadOnly && isPermissionEdit && getFilteredAssociates(currentEntity.associations,1).length==0">
            <mat-form-field appearance="outline" class="col-md-12">
              <mat-label>Select Associate</mat-label>
              <input placeholder="Enter name of person to select from the list" aria-label="Select Associate" matInput
                formControlName="contact" [matAutocomplete]="autoContact2" />

              <mat-autocomplete #autoContact2="matAutocomplete" [displayWith]="displayFnContact"
                (optionSelected)="onSelectContact($event,1)">
                <mat-option *ngFor="let option of filteredContacts$ | async" [value]="option">
                  {{ option.name }}
                </mat-option>
              </mat-autocomplete>
              <mat-error>{{ getErrorMessage(f.contact) }}</mat-error>
            </mat-form-field>
          </form>
          <ul class="col-sm-6 col-12 simple-list-hover"
            *ngIf="getFilteredAssociates(currentEntity.associations,1).length!==0">
            <li *ngFor="let item of getFilteredAssociates(currentEntity.associations,1); let i=index">
              <app-package-association-contact [item]="item" [showPhoto]="true"
                [showRemove]="!isReadOnly && isPermissionEdit" (remove)="onDeleteAssociate(item)">
              </app-package-association-contact>
            </li>
          </ul>
        </div>
      </mat-expansion-panel>


      <mat-expansion-panel [expanded]="!isTaskMode"
        *ngIf="!isTaskMode || (isTaskMode && task?.stageIndex != taskConstants.STAGEINDEX.STUDIOWORK)">
        <mat-expansion-panel-header>
          <mat-panel-title> Invitees </mat-panel-title>
        </mat-expansion-panel-header>
        <form [formGroup]="form" autocomplete="off" novalidate
          *ngIf="!isReadOnly && isTaskMode && task && (task?.stageIndex == taskConstants.STAGEINDEX.BRAINSTORMING || task?.stageIndex == taskConstants.STAGEINDEX.FINALREVIEW)">

          <div class="row m-0">
            <mat-form-field appearance="outline" class="col-md-12">
              <mat-label>Select Invitee</mat-label>
              <input placeholder="Enter name of person to select from the list" aria-label="Select Invitee" matInput
                formControlName="contact" [matAutocomplete]="autoContact2" />

              <mat-autocomplete #autoContact2="matAutocomplete" [displayWith]="displayFnContact"
                (optionSelected)="onSelectContact($event,2)">
                <mat-option *ngFor="let option of filteredContacts$ | async" [value]="option">
                  {{ option.name }}
                </mat-option>
              </mat-autocomplete>
              <mat-error>{{ getErrorMessage(f.contact) }}</mat-error>
            </mat-form-field>

          </div>
        </form>
        <ul class="simple-list-hover">
          <li *ngFor="let item of getFilteredAssociates(currentEntity.associations,2); let i=index">
            <app-contact-list-item [item]="item.contact" [showPhoto]="true" [showRemove]="!isReadOnly && allowEdit"
              (remove)="onDeleteAssociate(item)"></app-contact-list-item>
          </li>
        </ul>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true"
        *ngIf="isPermissionShowAll || isCurrentUserPartner || isCurrentUserAssociate">
        <mat-expansion-panel-header>
          <mat-panel-title> Meeting Agenda {{meetingAgendaList?.length}}</mat-panel-title>
        </mat-expansion-panel-header>
        <div class="d-flex justify-content-end pb-2"
          *ngIf="!isReadOnly && (isPermissionShowAll || isCurrentUserPartner || isCurrentUserAssociate)">
          <button mat-raised-button [color]="'primary'" (click)="onClickAddAgenda()"><mat-icon>add</mat-icon> Pick
            Agenda</button>
        </div>

        <ng-container *ngFor="let item of meetingAgendaList; let i = index;">
          <app-meeting-agenda [value]="item" [index]="i"
            [allowEdit]="!isReadOnly && (isPermissionShowAll || isCurrentUserPartner || isCurrentUserAssociate)"
            (afterUpdate)="onAgendaUpdate($event)"
            [allowDelete]="!isReadOnly && meetingAgendaList.length>1 && isPermissionEdit"
            (deleteClick)="onDeleteMeetingAgenda(item)">
          </app-meeting-agenda>
        </ng-container>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true" *ngIf="isPermissionShowAll || isCurrentUserPartner || isCurrentUserAssociate">
        <mat-expansion-panel-header>
          <mat-panel-title> Package Deliverables </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="text-right">
          <mat-icon (click)="openDeliverablesItemDialog()" *ngIf="currentEntity.deliverables.length > 0">add</mat-icon>
        </div>
        <div *ngIf="currentEntity.deliverables.length > 0">
          <div class="package-playlist-wrapper" *ngFor="let item of currentEntity.deliverables, let i = index">
            <h6><span class="title">{{i + 1}} - {{item.title}}</span><span class="description" *ngIf="item.description">
                |
                {{item.description}}</span></h6>
            <!-- <div class="handle-wrapper">
              <div cdkDragHandle>
                <svg width="24px" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z">
                  </path>
                  <path d="M0 0h24v24H0z" fill="none"></path>
                </svg>
              </div>
              <mat-icon (click)="removePlaylistItem(item,i)" matTooltip="Remove">close</mat-icon>
            </div> -->
          </div>
        </div>
        <div class="package-playlist-wrapper p-2 text-center" *ngIf="currentEntity.deliverables.length == 0">
          <h6 class="font-focused" *ngIf="currentEntity.deliverables.length == 0">PlayList Empty!!</h6>
          <mat-icon (click)="openDeliverablesItemDialog()">add</mat-icon>
        </div>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true"
        *ngIf=" (isTaskMode && task && task?.stageIndex == taskConstants.STAGEINDEX.STUDIOWORK)">
        <mat-expansion-panel-header>
          <mat-panel-title> Studio Work Deliverables </mat-panel-title>
        </mat-expansion-panel-header>
        <ul class="studiowork-deliverables-ul"
          *ngIf="currentEntity.deliverables && currentEntity.deliverables.length > 0">
          <ng-container *ngFor="let item of currentEntity.deliverables">
            <li *ngIf="deliverableTaskLinked(item)">
              <div class="d-flex align-items-center justify-content-between">
                <div class="pl-1">
                  <div class="d-flex align-items-center">
                    <h6 class="title">{{item.title}}</h6>
                  </div>
                  <div>
                    <div><small>StageService :</small> {{item.stageService}}</div>
                  </div>
                </div>
              </div>
              <div class="d-flex align-items-center">
                <div [mcvPopover]="myPopover"
                  class="pop-icon d-flex align-items-center justify-content-center mr-1 ml-1">
                  <mat-icon>info</mat-icon>
                  <mcv-popover-content #myPopover placement="left" [animation]="true" [closeOnClickOutside]="true">
                    <strong>Description</strong>
                    <p>{{item.description}}</p>
                  </mcv-popover-content>
                </div>
              </div>
            </li>
          </ng-container>
        </ul>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title> Submittals/Deliverables <small>For external mail/submission</small> </mat-panel-title>
        </mat-expansion-panel-header>
        <app-package-transmittal class="mt-2 d-block" [isReadOnly]="isReadOnly || currentEntity.statusFlag==1"
          [package]="currentEntity">
        </app-package-transmittal>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true" *ngIf="editableAttachmentConfig">
        <mat-expansion-panel-header>
          <mat-panel-title> Internal Audit <small>For internal records</small> </mat-panel-title>
        </mat-expansion-panel-header>
        <app-package-attachment class="mt-2 d-block" [attachments]="currentEntity.attachments"
          [config]="editableAttachmentConfig" (delete)="onDeleteAttachment($event)"
          (uploadComplete)="onUploadAttachments($event)"></app-package-attachment>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>SpaceDive™</mat-panel-title>
        </mat-expansion-panel-header>
        <div class="ml-2 mr-2">
          <form [formGroup]="spaceDiveForm"> <!-- Apply formGroup here -->
            <!-- FormArray for Space Dive URLs -->
            <div formArrayName="spaceDiveUrls">
              <div *ngFor="let group of spaceDiveUrls.controls; let i = index" [formGroupName]="i" class="row mb-2 align-items-center">
                <!-- Serial Number -->
                <div class="col-auto">
                  <h6 class="font-focused mb-0">{{ i + 1 }}.</h6>
                </div>
        
                <!-- Title Field -->
                <div class="col">
                  <mat-form-field class="w-100" appearance="outline">
                    <input matInput formControlName="title" placeholder="Enter title" (change)="updateUrls(i)">
                  </mat-form-field>
                  <mat-form-field class="w-100" appearance="outline">
                    <textarea matInput formControlName="url" placeholder="Enter URL" cdkTextareaAutosize 
                              #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="3" (input)="updateUrls(i)">
                    </textarea>
                  </mat-form-field>
                </div>
        
             
        
                <!-- Open in New Icon -->
                <div class="col-auto">
                  <button mat-icon-button color="primary" (click)="openSpaceDiveUrls(group.get('url')?.value)">
                    <mat-icon color="accent">open_in_new</mat-icon>
                  </button>
                </div>
        
                <!-- Add Button (Only for Last Field) -->
                <div class="col-auto" *ngIf="i === spaceDiveUrls.length - 1">
                  <button mat-icon-button color="primary" (click)="addSpaceDiveUrl()">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
        
                <!-- Delete Button -->
                <div class="col-auto" *ngIf="spaceDiveUrls.length > 1">
                  <button mat-icon-button color="warn" (click)="removeSpaceDiveUrl(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
        
      </mat-expansion-panel>
      
      

      <mat-expansion-panel [expanded]="false">
        <mat-expansion-panel-header>
          <mat-panel-title>History</mat-panel-title>
        </mat-expansion-panel-header>
        <div style="height: 319px;overflow-x: hidden;overflow-y: auto;">
          <div class="ml-2 mr-2 d-flex align-items-center justify-content-between">
            <form class="d-flex align-items-center justify-content-between" (ngSubmit)="onSearchPreviousPackages()"
              novalidate autocomplete="off">
              <mat-form-field appearance="outline">
                <input  class="mr-2 " name="search" [(ngModel)]="searchKeyPreviousPackages"
                  placeholder="Search for..." matInput matTooltip="Search" aria-label="Search">
                <mat-icon matPrefix>search</mat-icon>
                <mat-icon matSuffix (click)="searchKeyPreviousPackages=''">clear</mat-icon>
              </mat-form-field>
            </form>
          </div>
          <app-package-paged-list [openAsDialog]="true" [config]="previousPackagesPagedListConfig"
            (itemClick)="onPreviousPackageClick($event)"></app-package-paged-list>
        </div>
      </mat-expansion-panel>
    </div>

    <div class="col-md-5">
      <mat-expansion-panel *ngIf="!isReadOnly && !isTaskMode" [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>Action</mat-panel-title>
        </mat-expansion-panel-header>
        <div class="row">
          <div class="col-12">
            <div class="inline-list inline-list-justify-right">
              <button class="flex-grow-1" (click)="onUpdate()" mat-raised-button
                *ngIf="currentEntity.statusFlag == 0 && isPermissionEdit" color="primary" class="flex-grow-1"
                matTooltip="Save" aria-label="Save">Save</button>
              <button class="flex-grow-1" (click)="onDelete()" mat-raised-button
                *ngIf="currentEntity.statusFlag == 0 && isPermissionDelete" color="warn" class="flex-grow-1"
                matTooltip="delete" aria-label="delete">Delete</button>
            </div>
          </div>
        </div>
      </mat-expansion-panel>



      <mat-expansion-panel [expanded]=" true"
        *ngIf="!isTaskMode || (isTaskMode && task?.stageIndex != taskConstants.STAGEINDEX.STUDIOWORK)">
        <mat-expansion-panel-header>
          <mat-panel-title> Studio Tasks </mat-panel-title>
        </mat-expansion-panel-header>
        <!-- <div class="row">
          <div class="col-12">
            <button  mat-raised-button color="primary" class="btn float-right" (click)="assignStudioTask()"
              *ngIf="isTaskMode && task && task?.stageIndex == taskConstants.STAGEINDEX.PACKAGE">
              Assign
            </button>

          </div>
        </div> -->

        <div class="package-studio-assessed-value">
          <div class="d-flex" *ngIf="totalVHrAssigned">
            <small>Assigned VHr: </small>
            <h6 class="font-focused pl-1">{{totalVHrAssigned | number: '1.2-2' }}</h6>
          </div>
          <!-- <div class="d-flex" *ngIf="totalVHrConsumed">
            <small>Consumed VHr: </small>
            <h6 class="font-focused pl-1">{{totalVHrConsumed | number: '1.2-2' }}</h6>
          </div> -->
          <div class="d-flex" *ngIf="totalVHrAssessed">
            <small>Assessed VHr: </small>
            <h6 class="font-focused pl-1">{{totalVHrAssessed | number: '1.2-2' }}</h6>
          </div>
          <div class="d-flex" *ngIf="pendingVhr">
            <small>Assessement PendingVHr:</small>
            <h6 class="font-focused pl-1">{{pendingVhr | number: '1.2-2' }}</h6>
          </div>
        </div>
        <span *ngIf="!studioTasks || studioTasks.length==0">No Tasks Assigned!</span>
        <ul class="agile-list">
          <li *ngFor="let item of studioTasks" [ngClass]="{'highlight-red': item.isDelayed}">
            <app-package-studio-task [item]="item" [timeEntries]="item.timeEntries"
              [showActions]="isTaskMode && task && task?.stageIndex == taskConstants.STAGEINDEX.PACKAGE"
              (delete)="deleteStudioTask($event)" (update)="updateStudioTask($event)">
            </app-package-studio-task>
          </li>
        </ul>
      </mat-expansion-panel>

      <mat-expansion-panel [expanded]="true" *ngIf="isTaskMode">
        <mat-expansion-panel-header>
          <mat-panel-title> Time Entries </mat-panel-title>
        </mat-expansion-panel-header>
        <mcv-time-entry-list [wfTask]="task" (deleted)="onRemoveTimeEntry($event)"
          [readonly]="task.stageIndex==taskConstants.STAGEINDEX.BRAINSTORMING || task.stageIndex==taskConstants.STAGEINDEX.STUDIOWORK">
        </mcv-time-entry-list>
      </mat-expansion-panel>

      <app-package-studio-action *ngIf="isTaskMode && task.stageIndex==taskConstants.STAGEINDEX.STUDIOWORK"
        [task]="task" (update)="refresh()" (cancel)="onCancel()" (complete)="onTaskCompleted($event)">
      </app-package-studio-action>

      <app-package-task-action-box
        *ngIf="isTaskMode && task.stageIndex!=taskConstants.STAGEINDEX.STUDIOWORK && task.stageIndex!=taskConstants.STAGEINDEX.SUBMISSION"
        [task]="task" [showAssignTaskButton]="studioTaskAction" (cancel)="onCancel()"
        (complete)="onTaskCompleted($event)" (send)="onTaskCompleted($event)" [canSubmit]="isAssessmentPending" (updateEntity)="onUpdate($event)"
        (secondaryAction)="onSecondaryAction($event)">
      </app-package-task-action-box>

      <app-package-submission-action *ngIf="isTaskMode && task.stageIndex==taskConstants.STAGEINDEX.SUBMISSION"
        [task]="task" [Package]="currentEntity" (cancel)="onCancel()" (complete)="onTaskCompleted($event)">
      </app-package-submission-action>


      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title> Activity </mat-panel-title>

          <mat-panel-description></mat-panel-description>
        </mat-expansion-panel-header>

        <div class="mcv-activity-list-wrapper">
          <mcv-activity-list [entity]="nameOfEntity" [entityID]="entityID" *ngIf="entityID && entityID!=-1">
          </mcv-activity-list>
        </div>
      </mat-expansion-panel>
      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title> QR Code</mat-panel-title>
        </mat-expansion-panel-header>
        <div class="d-flex align-items-center justify-content-center">
          <a [href]="qrCodeDownloadLink" download="qrcode"> <mcv-qr-code [elementType]="'canvas'"
              [qrdata]="submissionUrl" [width]="256" [errorCorrectionLevel]="'M'" (qrCodeURL)="onChangeURL($event)"
              [title]="currentEntity.title"></mcv-qr-code>
          </a>
        </div>
      </mat-expansion-panel>
    </div>
  </div>
</div>