<nav class="mcv-header">
  <div class="mcv-header-part">
    <div class="nav-item nav-icon " appMenuToggle>
      <mat-icon>apps</mat-icon>
    </div>
    <div class="navbar-page-title">
      <h3 class="font-focused">{{ title }}</h3>
      <small class="caption" *ngIf="titleCount > 0">{{ titleCount }}</small>
      <ng-content select=".search-filter"></ng-content>
    </div>
    <ng-content select=".header-design-script-search-filter"></ng-content>

    <ng-content select=".nav-header-actions"></ng-content>
  </div>


  <div class="mcv-header-part mcv-header-part-right">


    <!-- <div class="nav-item nav-icon nav-item-hover" (click)="openRoute('/library-view')" aria-label="Library"
      matTooltip="Library" *ngIf="isPermissionLibraryView && !isMobileView">
 
      <h5>L</h5>
    </div> -->
    <div class="nav-item nav-icon nav-item-hover" (click)="openRoute('/time-line')" aria-label="Timeline"
      matTooltip="Timeline" *ngIf="isPermissionOfficeTimelineView ">
      <mat-icon>view_timeline</mat-icon>
    </div>

    <div class="nav-item nav-icon nav-item-hover" *ngIf="isPermissionDesignScriptView"
      (click)="openRoute(ROUTE_DESIGN_SCRIPT, false)" matTooltip="DesignScript">
      <h4>DS</h4>
    </div>

    <div class="nav-item  flex-grow-0 flex-shrink-0" *ngIf="!isMobileView">
      <div class="nav-item nav-icon" [matMenuTriggerFor]="analysisMenu" matTooltip="Analysis">
        <mat-icon>assessment</mat-icon>
      </div>
      <mat-menu #analysisMenu="matMenu" xPosition="before">
        <button mat-menu-item (click)="openRoute('/time-analysis')" aria-label="Time Analysis"
          *ngIf="isPermissionTimeAnalysisView">
          Time Analysis
        </button>

        <button mat-menu-item (click)="getExcel()" aria-label="Project Analysis"
          *ngIf=" isPermissionProjectAnalysisView">
          Project Analysis
        </button>
        <button mat-menu-item (click)="openRoute('/bill-analysis')" aria-label="Bill Analysis"
          *ngIf=" isPermissionBillAnalysisView">
          Bill Analysis
        </button>
        <button mat-menu-item (click)="openRoute('/inquiry-analysis')" aria-label="Inquiry Analysis"
          *ngIf=" isPermissionProjectAnalysisView">
          Inquiry Analysis
        </button>

        <button mat-menu-item (click)="openRoute('/sendgrid-analysis')" aria-label="Sendgrid Analysis"
          *ngIf=" isPermissionProjectAnalysisView">
          Sendgrid Analysis
        </button>
        <button mat-menu-item (click)="openRoute('/expense-analysis')" aria-label="Expense Analysis"
          *ngIf=" isPermissionExpenseEdit">
          Expense Analysis
        </button>
      </mat-menu>
    </div>

    <div class="nav-item hover-link flex-grow-0 flex-shrink-0">
      <div class="nav-item nav-icon nav-item-hover" [matMenuTriggerFor]="createMenu" matTooltip="New">
        <mat-icon>add</mat-icon>
      </div>
      <mat-menu #createMenu="matMenu" xPosition="before">
        <button mat-menu-item (click)="openMeetingDialog('New Meeting', -1, 0, true)" aria-label="New Meeting"
          *ngIf=" isPermissionMeetingEdit">
          Meeting
        </button>

        <!-- <button mat-menu-item (click)="openMeetingDialog('New C-Note', -1, 1, true)" aria-label="New C-Note"
        *ngIf=" isPermissionMeetingCNoteEdit">
        Communication Note
      </button> -->

        <button mat-menu-item (click)="openInspectionDialog('New JEMS Inspection', -1, 2, true)"
          aria-label="New Inspection Visit" *ngIf="isPermissionMeetingInspectionEdit">
          Inspection Visit
        </button>

        <button mat-menu-item (click)="openInquiryDialog('New Project Inquiry', -1, 0, true)"
          aria-label="New Project Inquiry" *ngIf=" isPermissionInquiryEdit">
          Project Inquiry
        </button>
        <button mat-menu-item (click)="openTodoDialog('New Todo', -1, 0, true)" aria-label="New Todo"
          *ngIf="isPermissionTodoEdit">
          Todo
        </button>
        <button mat-menu-item (click)="openHabitDialog('New Habit', -1, 0, true)" aria-label="New Habit"
          *ngIf="isPermissionHabitEdit">
          Habit
        </button>
        <button mat-menu-item (click)="openRequestTicketDialog('New Request Ticket', -1, 0, true)"
          aria-label="New Request Ticket" *ngIf="isPermissionRequestTicketEdit">
          Request Ticket
        </button>
        <button mat-menu-item (click)="openNewContactDialog('New Contact')" aria-label="New Contact"
          *ngIf=" isPermissionContactEdit">
          Contact
        </button>
        <button mat-menu-item (click)="openExpenseDialog(false,false,'New Expense', -1, 0, true)"
          aria-label="New Expense" *ngIf=" isPermissionExpenseEdit">
          Expense
        </button>
        <button mat-menu-item (click)="openExpenseDialog(true,true,'Credit to Petty Cash', -1, 0, true)"
          aria-label="Credit to Petty Cash" *ngIf=" isPermissionExpensePettyCashEdit">
          Petty Cash (Credit)
        </button>
        <button mat-menu-item (click)="openExpenseDialog(false,true,'Debit From Petty Cash', -1, 0, true)"
          aria-label="Debit From Petty Cash" *ngIf=" isPermissionExpensePettyCashEdit">
          Petty Cash (Debit)
        </button>
        <!-- <button mat-menu-item (click)="openPackageDialog('New Package', -1, 0, true)" aria-label="New Package"
          *ngIf=" isPermissionMaster">
          Package
        </button> -->
        <button mat-menu-item (click)="openLeaveDialog('New Application', -1, 0, true)" aria-label="New Application">
          Leave/Break Application</button>

        <button mat-menu-item (click)="openBillDialog('New Proforma/Bill', -1, 1, true)" aria-label="Proforma/Bill"
          *ngIf=" isPermissionProjectBillEdit">
          Proforma/Bill
        </button>
        <button mat-menu-item (click)="openTeamMemberDialog('New Team Member', -1, 0, true)"
          aria-label="New Team Member" *ngIf=" isPermissionTeamMemberEdit">
          Team Member
        </button>
        <button mat-menu-item (click)="openLibraryEntityDialog('New Library Entity')" aria-label="Library Entity"
          *ngIf=" isPermissionLibraryEdit">
          Library Entity
        </button>
      </mat-menu>
    </div>
   

    <!-- <div class="nav-item nav-icon " (click)="openRoute('/contact-list')" matTooltip="Contact"
      *ngIf="isPermissionContactList">
      <mat-icon>phone</mat-icon>
    </div> -->

    

    <div class="nav-item nav-icon" (click)="refresh()" matTooltip="Refresh"
    aria-label="Refresh" >
    <mat-icon>refresh</mat-icon>
  </div>

    <div class="nav-item nav-icon " (click)="openRoute('/cockpit')" matTooltip="MyCockpit">
      <mat-icon>dashboard</mat-icon>
    </div>
    <div class="nav-item version-update" *ngIf="isNewVersionAvailable" (click)="updateVersion()">
      <h5 class="font-focused">New Version</h5>
      <small>click to update</small>
    </div>
    <div class="nav-item " *ngIf="!isNewVersionAvailable">
      <div class="account-dropdown" [matMenuTriggerFor]="userAccount" *ngIf="user">
        <img *ngIf="user.contact" src="{{ user.contact?.photoUrl || 'assets/images/user.png' }}"
          class="account-thumbnail" alt="" />

        <div class="account-username">
          <span *ngIf="user.contact" class="mb-0">{{ user.contact?.name || user.username }} </span>

          <!-- <span class="text-muted">{{ user.username }}</span> -->
        </div>
        <mat-icon>arrow_drop_down</mat-icon>
      </div>
      <mat-menu #userAccount="matMenu" xPosition="before">
        <button mat-menu-item (click)="openRoute(routeChangePassword)">
          <mat-icon>vpn_key</mat-icon>
          <span>Change Password</span>
        </button>
        <button mat-menu-item (click)="changePhoto()">
          <mat-icon>portrait</mat-icon>
          <span>Change Photo</span>
        </button>
        <button mat-menu-item (click)="ruleBook()">
          <mat-icon>auto_stories</mat-icon>
          <span>Rule Book</span>
        </button>
        <button mat-menu-item (click)="subscribeToPush()">
          <mat-icon>notifications_active</mat-icon>
          <span>Subscribe to Notifications</span>
        </button>
        <button mat-menu-item (click)="unsubscribeToPush()">
          <mat-icon>notifications_off</mat-icon>
          <span>Unsubscribe All Devices</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>exit_to_app</mat-icon>
          <span>Log Out</span>
        </button>
      </mat-menu>
    </div>
  </div>
</nav>

<app-menu-sidebar></app-menu-sidebar>