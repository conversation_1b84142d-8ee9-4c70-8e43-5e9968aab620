<div class="mat-dialog-header mcv-dialog-header">
  <h5 class="font-focused">{{data.dialogTitle}}</h5>
  <div>
    <button mat-icon-button aria-label="delete" (click)="onDelete()" matTooltip="Delete"
    *ngIf="!data.isCreateMode && data.task.statusFlag == 0">
    <mat-icon>delete</mat-icon>
  </button>
  <!-- <button mat-icon-button aria-label="save" (click)="onSave()" matTooltip="Save"
    *ngIf="data.isCreateMode && data.task.statusFlag != 1">
    <mat-icon>done</mat-icon>
  </button> -->
   <button mat-icon-button aria-label="save" (click)="onSave()" matTooltip="Save"
    *ngIf="!data.isCreateMode && data.task.statusFlag != 1">
    <mat-icon>done</mat-icon>
  </button>

    <button mat-icon-button aria-label="close" (click)="onClose(null)">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<mat-dialog-content>
  <!-- <app-package-studio-assign [config]="data" (create)="onClose($event)" (cancel)="onClose(null)"
    (update)="onClose($event)" *ngIf="data && data.isCreateMode" #createActionButton>
  </app-package-studio-assign>-->


       <app-mcv-time-line-view [package]="data" [componentFlag]="'packagetimeline'"  *ngIf="data && data.isCreateMode"
               ></app-mcv-time-line-view>

  <app-package-studio-edit [config]="data" (create)="onClose($event)" (cancel)="onClose(null)"
    (update)="onClose($event)" (delete)="onClose($event)" *ngIf="data && !data.isCreateMode" #editActionButton>
  </app-package-studio-edit> 
</mat-dialog-content>
