import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AuthService } from 'src/app/auth/services/auth.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';

import { AppConfig } from 'src/app/app.config';
import { Contact } from 'src/app/contact/models/contact';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { ApiFilter } from 'src/app/models/api-filters';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { CommonModule } from '@angular/common';
import { ProjectSummary } from 'src/app/project/models/project-summary.model';
import { firstValueFrom } from 'rxjs';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ProjectLastBite } from 'src/app/mcv-last-bite/models/project-last-bite.model';

@Component({
    selector: 'app-cockpit-project-analysis',
    templateUrl: './cockpit-project-analysis.component.html',
    styleUrls: ['./cockpit-project-analysis.component.scss'],
    standalone: true,
    imports: [ FooterComponent,MatMenuModule,MatOptionModule,CommonModule,MatCheckboxModule,FormsModule,  McvFilterSidenavComponent,  MatIconModule, MatFormFieldModule, MatInputModule, ReactiveFormsModule, MatSelectModule, MatOptionModule, MatButtonModule, MatTooltipModule, FilterToggleDirective ]
})
export class CockpitProjectAnalysisComponent implements OnInit {

  searchKey!: string;

  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();

  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];
  projectCashFlow: ProjectSummary[] = [];
  originalDataList: any[] = [];
  projectKeySearch: string = '';
  filteredProject: string[] = [];
  distinctProject: string[] = [];
  selectedProject: string[] = [];
  isSorted: boolean = false;
  partnerFilter = [
    { key: 'usersOnly', value: 'true' },
    { key: 'projectPartnersOnly', value: 'true' }
  ];

  filters: ApiFilter[] = [
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString() },
    // { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_ONHOLD.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString() },
    { key: 'companyID', value: '1' },
  ];

  total: ProjectSummary = new ProjectSummary();
  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    projectName: '',
    totalFee: '',
    totalBillAmount: '',
    totalPaymentAmount: '',
    proformaAmount: '',
    proformaDate: '',
    vHr: '',
    vHrCost: '',
    xCost: '',
    vHrAfterLastPayment: '',
    vHrCostAfterLastPayment: '',
    completedAmount: '',
    activeAmount: '',
    proposedAmount: '',
  };
  constructor(
    private config: AppConfig,
    private authService: AuthService,
    private contactService: ContactApiService,
    private projectService: ProjectApiService,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
  ) { }

  ngOnInit() {

    this.refresh();
    this.getPartnerOptions();
    this.getStatusOptions();
    this.getCompanyOptions();

    this.statusFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "statusFlag");
            value.forEach((element: ApiFilter) => {
              this.addFilter('statusFlag', element);
            });
            this.refresh();
          }
        }
      );

    this.searchFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    this.partnerFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "projectPartnerContactID");
            value.forEach((contact: Contact) => {
              if (contact && contact.id) {
                this.addFilter('projectPartnerContactID', contact.id);
              }
            });
            this.refresh();
          }
        }
      );

    this.companyFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          // console.log(value);
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "companyID");
            if (value && value.id) {
              this.addFilter('companyID', value.id)
            }
            this.refresh();
          }
        }
      );
  }



  private refresh() {
    this.projectCashFlow = [];
    this.projectService.getAnalysis('cashflow', this.filters, this.searchKey).subscribe(val => {
      if (val) {
        this.projectCashFlow = val;
        this.calculateTotal();
    this.extractDistinctValues();
    this.originalDataList = [...this.projectCashFlow];
      }
    });
   
  }
  private calculateTotal() {
    this.total = new ProjectSummary();
    
    this.projectCashFlow.forEach(x => {
      this.total.totalFee += x.totalFee;
      this.total.totalBillAmount += x.totalBillAmount;
      this.total.totalPaymentAmount += x.totalPaymentAmount;
      this.total.vHr += x.vHr;
      this.total.vHrCost += x.vHrCost;
      this.total.xCost += x.xCost;
      this.total.vHrAfterLastPayment += x.vHrAfterLastPayment;
      this.total.completedAmount += x.completedAmount;
      this.total.activeAmount += x.activeAmount;
      this.total.proposedAmount += x.proposedAmount;
      this.total.proformaAmount += x.proformaAmount;
      this.total.vHrCostAfterLastPayment += x.vHrCostAfterLastPayment;
    });

    
  }

  private addFilter(key: string, value: any) {
    const _filter = this.filters.find(obj => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  private getCompanyOptions() {
    this.companyAccountService.get().subscribe((data) => {
      this.companyOptions = data;
      this.companyFC.setValue(this.companyOptions.find(x => x.id == 1), { emitEvent: false });
    }
    );
  }

  private getPartnerOptions() {
    this.contactService.get(this.partnerFilter).subscribe((data) => {
      this.partnerOptions = data;
      if (!this.authService.isRoleMaster && this.partnerOptions.find(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))) {
        this.partnerFC.setValue(this.partnerOptions.filter(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))
        );
      }
    }
    );
  }

  protected async getStatusOptions() {
    this.statusOptions = await firstValueFrom(this.statusMasterService.get([{ key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT}]));
        this.statusFC.setValue(this.filters.map(x => x.value), { emitEvent: false })
     
  }
  onRefresh() {
    this.refresh();
  }
  onExportExcel() {
    this.projectService.exportAnalysisExcel('cashflow', this.filters, this.searchKey, this.sortFC.value);
  }


  filterDistinctProject() {
    const searchLower = this.projectKeySearch.toLowerCase();
    this.filteredProject = this.distinctProject.filter(element =>
      element.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: any) {
    this.projectKeySearch = '';
    this.filterDistinctProject();
  }

  toggleSelectAll(filterType: 'project' | 'status' | 'partner') {
    const distinctValues = this.getDistinctValues(filterType);
    switch (filterType) {
      case 'project':
        this.selectedProject = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
    }
    this.applyFilters();
  }
  getDistinctValues(filterType: 'project' | 'status'  | 'partner'): string[] {
    switch (filterType) {
      case 'project':
        return this.distinctProject;
      default:
        return [];
    }
  }

  isAllSelected(type: string): boolean {
    if (type === 'project') {
      return (
        this.selectedProject.length === this.distinctProject.length &&
        this.selectedProject.length > 0
      );
    }
    
    return false;
  }

  toggleSelection(value: string, type: string) {
    if (type === 'project') {
      this.selectedProject.includes(value)
        ? this.selectedProject.splice(this.selectedProject.indexOf(value), 1)
        : this.selectedProject.push(value);
    }
    
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if (this.searchKey) {
      const searchLower = this.searchKey.toLowerCase();
      filteredList = filteredList.filter(
        (item) =>
          item.project?.toLowerCase().includes(searchLower) ||
          '' ||
          item.status?.toLowerCase().includes(searchLower) ||
          ''
      );
    }
    if (this.selectedProject.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedProject.includes(item.project)
      );
    }

    this.projectCashFlow = filteredList;
    this.calculateTotal();
  }

  extractDistinctValues() {
    this.distinctProject = [...new Set(this.projectCashFlow.map((x) => x.project))];
    this.filteredProject = [...this.distinctProject];
  }

  clearSelection(type: string) {
    if (type === 'project') {
        this.selectedProject = [];
    } 
    this.applyFilters(); // Apply filters after clearing the selection
}

resetFilter() {
  this.selectedProject = [];
  this.projectCashFlow = [...this.originalDataList]; // Restore full list
  this.calculateTotal();
}

sortData(column: keyof ProjectSummary | '') {
  if (column === '') {
    // Reset to default (original data) but apply the filters again
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.projectCashFlow = dataToFilter.filter((item:any) => {
      return (
        (this.selectedProject.length === 0 || this.selectedProject.includes(item.person))
      );
    });

    // Reset the sort state to default
    this.sortState = { 
      activeColumn: '',
      projectName: '',
      totalFee: '',
      totalBillAmount: '',
      totalPaymentAmount: '',
      proformaAmount: '',
      proformaDate: '',
      vHr: '',
      vHrCost: '',
      xCost: '',
      vHrAfterLastPayment: '',
      vHrCostAfterLastPayment: '',
      completedAmount: '',
      activeAmount: '',
      proposedAmount: ''
    };
    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if no sorting is selected
  }

  // If the clicked column is already the active column, cycle through the three states
  const currentSort = this.sortState[column];

  if (currentSort === 'newFirst') {
    // If it's 'newFirst', change to 'oldFirst'
    this.sortState[column] = 'oldFirst';
  } else if (currentSort === 'oldFirst') {
    // If it's 'oldFirst', reset to default (no sorting)
    this.sortState[column] = '';
    this.sortState.activeColumn = '';
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.projectCashFlow = dataToFilter.filter((item:any) => {
      return (
        (this.selectedProject.length === 0 || this.selectedProject.includes(item.person))
       
      );
    });

    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if reset is selected
  } else {
    // If no sorting is applied, set it to 'newFirst' (ascending order)
    this.sortState[column] = 'newFirst';
  }

  // Set the active column
  this.sortState['activeColumn'] = column;

  // Reset other columns' sort state to '' (no sort)
  for (let key in this.sortState) {
    if (key !== column && key !== 'activeColumn') {
      this.sortState[key] = ''; // Reset other columns' sort state to no sort
    }
  }

  // Sorting logic: Compare dates for the active column
  const sortedData = [...this.projectCashFlow].sort((a, b) => {
    const dateA = new Date(a[column] as string);
    const dateB = new Date(b[column] as string);

    if (this.sortState[column] === 'newFirst') {
      return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
    } else if (this.sortState[column] === 'oldFirst') {
      return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
    }
    return 0; // If no sorting, return unchanged order
  });

  // Update the projectCashFlow with the sorted data
  this.projectCashFlow = sortedData;
  this.isSorted = true; // Set isSorted to true when sorting is applied
}


}
