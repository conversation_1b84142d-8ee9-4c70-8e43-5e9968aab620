import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { AppConfig } from 'src/app/app.config';
import { AppPermissions } from 'src/app/app.permissions';
import { Contact } from 'src/app/contact/models/contact';
import { AppService } from 'src/app/services/app.service';
import { AuthService } from 'src/app/auth/services/auth.service';
import { CockpitMyCalendarComponent } from '../cockpit-my-calendar/cockpit-my-calendar.component';
import { ProjectEstmationAnalysisComponent } from '../../../design-script/components/project-estmation-analysis/project-estmation-analysis.component';
import { UpcomingPackageDataGridComponent } from '../../../upcoming-packages/components/upcoming-package-data-grid/upcoming-package-data-grid.component';
import { ProposedPackageListComponent } from '../../../package/components/proposed-package-list/proposed-package-list.component';
import { CockpitProjectAnalysisComponent } from '../cockpit-project-analysis/cockpit-project-analysis.component';
import { CockpitMyAnalysisComponent } from '../cockpit-my-analysis/cockpit-my-analysis.component';
import { ProjectProgressChartComponent } from '../../../project-progress-chart/components/project-progress-chart/project-progress-chart.component';
import { ProjectCrmChartComponent } from '../../../project-charts/components/project-crm-chart/project-crm-chart.component';
import { ProjectCashflowChartComponent } from '../../../project-charts/components/project-cashflow-chart/project-cashflow-chart.component';
import { LastBiteAnalysisComponent } from '../../../mcv-last-bite/components/last-bite-analysis/last-bite-analysis.component';
import { VhrAnalysisComponent } from '../../../mcv-vhr-analysis/components/vhr-analysis/vhr-analysis.component';
import { CockpitTeamCalendarComponent } from '../cockpit-team-calendar/cockpit-team-calendar.component';
import { NgIf } from '@angular/common';
import { SentPackageListComponent } from '../../../package/components/sent-package-list/sent-package-list.component';
import { CockpitTaskAnalysisComponent } from '../cockpit-task-analysis/cockpit-task-analysis.component';
import { CockpitTimelineComponent } from '../cockpit-timeline/cockpit-timeline.component';
import { CockpitMetricsComponent } from '../cockpit-metrics/cockpit-metrics.component';
import { CockpitComponent } from '../cockpit/cockpit.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { HeaderComponent } from '../../../mcv-header/components/header/header.component';
import { McvTimeLineViewComponent } from 'src/app/mcv-time-line/components/mcv-time-line-view/mcv-time-line-view.component';
import { firstValueFrom } from 'rxjs';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { BillAnalysisComponent } from "../../../bill-analysis/components/bill-analysis/bill-analysis.component";
import { BillApiService } from 'src/app/project/services/bill-api.service';
import { ExpenseAnalysisComponent } from "../../../expense-anaylsis/expense-analysis/expense-analysis.component";
import { ExpenseApiService } from 'src/app/expense/services/expense-api.service';


@Component({
    selector: 'app-cockpit-view',
    templateUrl: './cockpit-view.component.html',
    styleUrls: ['./cockpit-view.component.scss'],
    standalone: true,
    imports: [HeaderComponent, McvTimeLineViewComponent, MatTabsModule, MatIconModule, CockpitComponent, CockpitMetricsComponent, CockpitTimelineComponent, CockpitTaskAnalysisComponent, SentPackageListComponent, NgIf, CockpitTeamCalendarComponent, VhrAnalysisComponent, LastBiteAnalysisComponent, ProjectCashflowChartComponent, ProjectCrmChartComponent, ProjectProgressChartComponent, CockpitMyAnalysisComponent, CockpitProjectAnalysisComponent, ProposedPackageListComponent, UpcomingPackageDataGridComponent, ProjectEstmationAnalysisComponent, CockpitMyCalendarComponent, BillAnalysisComponent, ExpenseAnalysisComponent]
})
export class CockpitViewComponent implements OnInit, OnDestroy {
  filteredContactList: any;
  selectedTabIndex = 0;
  headerTitle!: string;
  dummyContacts:any
  get isPermissionBillAnalysisView(): boolean { return this.billService.isPermissionAnalysisView; }
  get isPermissionExpenseEdit(): boolean {
    return this.expenseService.isPermissionEdit;
  }
  get isPermissionVHrAnalysis(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_VHR_ANALYSIS]);
  }
  get isPermissionLastBiteAnalysis(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_LAST_BITE_ANALYSIS]);
  }
  get isPermissionCashflow(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_PROJECT_CASHFLOW]);
  }
  get isPermissionCRM(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_PROJECT_CRM]);
  }

  get isPermissionProjectProgress(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_PROJECT_PROGRESS]);
  }
  get isPermissionUpcomingPackages(): boolean {
    return this.authService.isInAnyRole([this.permissions.UPCOMING_PACKAGE_LIST]);
  }
  get isPermissionPackagetProgress(): boolean
  {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_PACKAGE_PROGRESS]);
  }
  get isPermissionEstimationAnalysis(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_ESTIMATION_ANALYSIS]);
  }
  get currentUser(): Contact {

    return this.authService.currentUserStore ? this.authService.currentUserStore.contact : new Contact();
  }

  get IsPermissionWFTaskShowAll(): boolean { return this.authService.isInAnyRole([this.permissions.WFTASK_SPECIAL_SHOW_ALL]); }
  get IsPermissionInquiryActivity(): boolean { return this.authService.isInAnyRole([this.permissions.COCKPIT_INQUIRY_ACTIVITY]); }
  get IsPermissionTeamTasks(): boolean { return this.authService.isInAnyRole([this.permissions.COCKPIT_TEAM_TASKS]); }
  get IsPermissionTeamCalendar(): boolean { return this.authService.isInAnyRole([this.permissions.COCKPIT_TEAM_CALENDAR]); }
  get IsPermissionPhaseEditor(): boolean { return this.authService.isInAnyRole([this.permissions.DESIGN_SCRIPT_SPECIAL_PHASE_EDIT]); }

  constructor(
    private config: AppConfig,
    private authService: AuthService,
    private permissions: AppPermissions,
    private appService: AppService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private contactservice: ContactApiService,
    private billService: BillApiService,
    private expenseService: ExpenseApiService,
  ) { }

  ngOnDestroy(): void {
    this.appService.resetHeader();
  }

  ngOnInit() {
    this.headerTitle = this.activatedRoute.snapshot.data['title'];

    this.activatedRoute.queryParamMap.subscribe((params: ParamMap) => {
      // console.log('param',params);
      this.selectedTabIndex = Number(params.get('index'));
    });

    this.getContactTaskData()
  
  }

  onSelectedTabIndexChange(index: number) {
    // console.log('onSelectedTabIndexChange',index);
    this.router.navigate([this.config.ROUTE_COCKPIT], {
      queryParams: JSON.parse(`{"index":"${index}"}`),
      relativeTo: this.activatedRoute
    });
  }

  async getContactTaskData() {
    let contactFilter = [
      { "key": "usersOnly", "value": "true" },
      { "key": "appointmentStatusFlag", "value": "0" }
    ];
  
    // Fetch contacts
    this.dummyContacts = await firstValueFrom(this.contactservice.get(contactFilter));
  
    // Filter contacts based on current user ID
    this.filteredContactList = this.dummyContacts.filter(
      (contact: any) => this.currentUser.id === contact.id
    );
  
    // You may want to ensure `this.searchTerm` is correctly assigned
    // Assuming you want to store the names of the filtered contacts
   
  
   
  }
  
}
