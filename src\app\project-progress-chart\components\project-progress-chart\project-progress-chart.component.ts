import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AuthService } from 'src/app/auth/services/auth.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';
import { AppSettingMasterApiService } from 'src/app/services/app-setting-master-api.service';
import { ProjectScopeApiService } from 'src/app/project-scope/services/project-scope-api.service';

import { AppConfig } from 'src/app/app.config';
import { Contact } from 'src/app/contact/models/contact';

import { Project } from 'src/app/project/models/project.model';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { ProjectsPartnerFeePhaseEditorDialogComponent } from 'src/app/project-charts/components/projects-partner-fee-phase-editor-dialog/projects-partner-fee-phase-editor-dialog.component';
import { ApiFilter } from 'src/app/models/api-filters';
import { DesignScriptApiService } from 'src/app/design-script/services/design-script-api.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DecimalPipe, CurrencyPipe, CommonModule } from '@angular/common';
import { ProjectProgressBarsComponent } from 'src/app/project/components/project-progress-bars/project-progress-bars.component';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';
import { ProjectSummary } from 'src/app/project/models/project-summary.model';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'project-progress-chart',
  templateUrl: './project-progress-chart.component.html',
  styleUrls: ['./project-progress-chart.component.scss'],
  standalone: true,
  imports: [CommonModule, MatTooltipModule, FooterComponent, MatFormFieldModule, MatSelectModule, ReactiveFormsModule, MatOptionModule, MatInputModule, MatIconModule, MatButtonModule, DecimalPipe, CurrencyPipe,
    ProjectProgressBarsComponent, McvFilterSidenavComponent]
})
export class ProjectProgressChartComponent implements OnInit {

  // getProjectData;
  xCostFactor = 1;
  totalPaymentAmount = 0;
  totalProformaAmount = 0;
  totalPackageAmount = 0;
  totalPackageActiveAmount = 0;
  totalPackageProposed = 0;
  totalVHrCost = 0;
  total5xCost = 0;
  totalReceivedRatio = 0;
  get totalKPI() {
    return this.totalVHrCost > 0 ? this.totalPaymentAmount / this.totalVHrCost : 1;
  }

  dataList: ProjectSummary[] = [];
  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();


  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];

  get PACKAGE_COMPLETED_COLOR() { return this.config.PACKAGE_COMPLETED_COLOR; }
  get PACKAGE_PROPOSED_COLOR() { return this.config.PACKAGE_PROPOSED_COLOR; }
  get PACKAGE_ACTIVE_COLOR() { return this.config.PACKAGE_ACTIVE_COLOR; }

  get PAYMENT_RECEIVED_COLOR() { return this.config.PAYMENT_RECEIVED_COLOR; }
  get PAYMENT_DUE_COLOR() { return this.config.PAYMENT_DUE_COLOR; }

  get VHR_CONSUMED_COLOR() { return this.config.VHR_CONSUMED_COLOR; }
  get VHR_X_COLOR() { return this.config.VHR_X_COLOR; }

  filters: ApiFilter[] = [];

  readonly defaultFilters: ApiFilter[] = [
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString() },
    // { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_ONHOLD.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString() },
    { key: 'companyID', value: '1' },
  ];
  searchKey!: string;
  sort: string = 'Big Blue';
  sortOptions: any[] = [
    'Big Blue', 'Big Green', 'Highest KPI', 'Lowest KPI'];

  readonly nameOfEntity = this.config.NAMEOF_ENTITY_PROJECT_ASSOCIATION;
  get projectOptions(): Project[] { return this.designScriptUtilityService.projects; }

  constructor(
    private companyAccountService: CompanyAccountApiService,
    private contactService: ContactApiService,
    private authService: AuthService,
    private projectService: ProjectApiService,
    private config: AppConfig,
    private statusMasterService: StatusMasterService,
    private appSettingService: AppSettingMasterApiService,
    private designScriptUtilityService: DesignScriptApiService,
    public dialog: MatDialog,
    private projectScopeService: ProjectScopeApiService,
  ) { }

  async ngOnInit() {
    await this.appSettingService.loadPresets();

    this.refreshFilters();
    this.xCostFactor = Number(this.appSettingService.presets
      .find(x => x.presetKey == this.config.PRESET_XCOST_FACTOR)?.presetValue);


    this.getProjectOptions();
    this.sortFC.setValue(this.sort, { emitEvent: false });
    this.getPartnerOptions();
    this.getStatusOptions();
    this.getCompanyOptions();

    this.statusFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "statusFlag");
            value.forEach((element: any) => {
              this.addFilter('statusFlag', element);
            });
            this.refresh();
          }
        }
      );

    this.sortFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.sort = value;
            this.applySort();
          }
        }
      );

    this.searchFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    this.partnerFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "projectPartnerContactID");
            value.forEach((contact: Contact) => {
              if (contact && contact.id) {
                this.addFilter('projectPartnerContactID', contact.id);
              }
            });
            this.refresh();
          }
        }
      );

    this.companyFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          // console.log(value);
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "companyID");
            if (value && value.id) {
              this.addFilter('companyID', value.id)
            }
            // value.forEach(e => {
            //   if (e && e.id) {
            //     this.addFilter('companyID', e.id);
            //   }
            // });
            this.refresh();
          }
        }
      );
  }

  private getCompanyOptions() {
    this.companyAccountService.get().subscribe((data) => {
      this.companyOptions = data;
      this.companyFC.setValue(this.filters.filter(x => x.key == 'companyID').map(x => x.value), { emitEvent: false });
    }
    );
  }

  async refresh() {
    this.dataList = await firstValueFrom(this.projectService.getAnalysis('cashflow', this.filters, this.searchKey,
      this.sort));

        this.totalVHrCost = 0;
        this.total5xCost = 0;
        this.totalPaymentAmount = 0;
        this.totalProformaAmount = 0;
        this.totalReceivedRatio = 0
        this.totalPackageAmount = 0;
        this.totalPackageActiveAmount = 0;
        this.totalPackageProposed = 0;

        this.dataList.forEach((obj) => {
          this.totalVHrCost += obj.vHrCost;
          this.total5xCost += (obj.vHrCost * this.xCostFactor);
          this.totalPaymentAmount += obj.totalPaymentAmount;
          this.totalProformaAmount += obj.proformaAmount;
          this.totalPackageAmount += obj.completedAmount;
          this.totalPackageActiveAmount += obj.activeAmount;
          this.totalPackageProposed += obj.proposedAmount;
        });
        this.totalReceivedRatio = this.totalVHrCost > 0 ? (this.totalPaymentAmount / this.totalVHrCost) : 0;

        this.applySort();
  }

  private applySort() {
    if (this.sort == 'Highest KPI') {
      this.dataList.sort((a, b) => b.kpi - a.kpi);
    }
    else if (this.sort == 'Lowest KPI') {
      this.dataList.sort((a, b) => a.kpi - b.kpi);
    }
    else if (this.sort == 'Big Blue') {
      this.dataList.sort((a, b) => b.xCostPercentage - a.xCostPercentage || (a.totalPaymentPercentage + a.proformaPercentage) - (b.totalPaymentPercentage + b.proformaPercentage));
    }
    else {
      this.dataList.sort((a, b) => (b.totalPaymentPercentage + a.proformaPercentage) - (a.totalPaymentPercentage + b.proformaPercentage) || a.xCostPercentage - b.xCostPercentage);
    }
  }

  private getPartnerOptions() {
    this.contactService.get([
      { key: 'usersOnly', value: 'true' },
      { key: 'projectPartnersOnly', value: 'true' }
    ]).subscribe((data) => {
      this.partnerOptions = data;

    }
    );
  }

  protected async getStatusOptions() {
    this.statusOptions = await firstValueFrom(this.statusMasterService.get([{ key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT }]));
    this.statusFC.setValue(this.filters.map(x => x.value), { emitEvent: false })

  }



  private addFilter(key: string, value: any) {
    const _filter = this.filters.find(obj => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  onExportExcel() {
    this.projectService.exportAnalysisExcel('Cashflow', this.filters, this.searchKey,
      this.sort);
  }


  private getProjectOptions() {
    this.projectService.get([
      { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString() },
      { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString() },
      { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString() },
    ]).subscribe(
      data => {
        this.designScriptUtilityService.projects = data.filter(x => x.code && x.code !== 0);
      }
    );
  }

  openProgressDialog(data: ProjectSummary) {
    const dialogRef = this.projectScopeService.openDialog(ProjectsPartnerFeePhaseEditorDialogComponent, data, true);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        // console.log('data received', res);
        let itemIndex = this.dataList.findIndex(x => x.projectID == data.projectID);
        if (itemIndex !== -1) {
          this.dataList[itemIndex] = Object.assign({}, res);
          // console.log('data updated', this.dataList[itemIndex]);
        }
        this.refresh();
      }
    });
  }

  getStatusColor(flag: number): string {
    return this.projectService.getStatusColor(flag);
  }

  refreshFilters() {

    this.filters = this.defaultFilters;
    if (!this.authService.isRoleMaster && this.authService.currentUserStore && this.authService.currentUserStore.contact) {
      this.filters.push({ key: 'projectPartnerContactID', value: this.authService.currentUserStore.contact.id.toString() });
    }


    this.companyFC.reset();
    this.companyFC.setValue(this.filters.filter(x => x.key == 'companyID').map(x => x.value), { emitEvent: false });

    this.statusFC.reset();
    this.statusFC.setValue(this.filters.filter(x => x.key == 'statusFlag').map(x => x.value), { emitEvent: false });

    this.partnerFC.reset();
    this.partnerFC.setValue(this.filters.filter(x => x.key == 'projectPartnerContactID').map(x => x.value), { emitEvent: false });


    this.searchFC.reset();
    this.refresh();
  }
}
