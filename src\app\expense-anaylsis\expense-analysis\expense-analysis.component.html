<mcv-header [title]="headerTitle"></mcv-header>
<div class="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="row m-2">
                <div class="col-sm-3">
                    <mat-form-field appearance="outline">
                        <mat-label>Select Range</mat-label>
                        <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                            <input matStartDate placeholder="Start date" formControlName="start" readonly />
                            <input matEndDate placeholder="End date" formControlName="end" readonly />
                        </mat-date-range-input>
                        <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
                        <mat-date-range-picker #rangePicker1></mat-date-range-picker>
                    </mat-form-field>
                </div>
                <div class="col-sm-3">
                    <mat-form-field appearance="outline">
                        <mat-label>Search</mat-label>
                        <input placeholder="Enter text to search" aria-label="Search" matInput
                            [formControl]="searchFilter" />
                        <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                </div>
                <div class="col-sm-6">
                    <button mat-raised-button color="primary" (click)="refresh()" matTooltip="Refresh"
                        aria-label="Refresh">
                        Refresh
                    </button>

                    <button mat-raised-button (click)="onExportExcel()" matTooltip="Export excel"
                        aria-label="export excel">
                        Export Excel
                    </button>

                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="mcv-data-list-header mcv-data-list-header-dark">
                    <div class="mcv-data-list-header-row">
                        <div class="row">
                            <div class="col-sm-2">
                                <div class="row">
                                    <div class="col-sm-6 col-6">
                                        <div class="mcv-data-list-cell">
                                            <span>Date</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-6">
                                        <div class="mcv-data-list-cell"
                                            [ngClass]="{'mcv-data-list-cell-align-right':isMobileView}">
                                            <span>Head</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="row">
                                    <div class="col-sm-6 col-6">
                                        <div class="mcv-data-list-cell">
                                            <span>Particulars</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-6">
                                        <div class="mcv-data-list-cell"
                                            [ngClass]="{'mcv-data-list-cell-align-right':isMobileView}">
                                            <span>Pay To</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="row">
                                    <div class="col-sm-6 col-6">
                                        <div class="mcv-data-list-cell">
                                            <span>Transaction Mode</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 col-6 ">
                                        <div class="mcv-data-list-cell"
                                            [ngClass]="{'mcv-data-list-cell-align-right':isMobileView}">
                                            <span>Transaction Date</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="row">
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>Expense Amount </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>Amount Dr </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>Amount Cr</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>Balance</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-1">
                                <div class="row">
                                    <div class="col-sm-12 col-12 ">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>Approved By</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mcv-data-list-header-row mcv-data-list-total">
                        <div class="row">
                            <div class="col-sm-7">
                                <div class="mcv-data-list-cell">Total</div>
                            </div>

                            <div class="col-sm-4">
                                <div class="row">
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>{{totalExpenseAmount| currency:'INR':'symbol':'0.0'}}</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>{{totalAmountDr| currency:'INR':'symbol':'0.0'}}</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>{{totalAmountCr| currency:'INR':'symbol':'0.0'}}</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-6">
                                        <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                                            <span>{{totalAmountBalance| currency:'INR':'symbol':'0.0'}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-1">
                                <div class="mcv-data-list-cell"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <ul class="mcv-data-list mcv-data-list-dark">
                    <li *ngFor="let item of dataList">
                        <app-expense-analysis-item [item]="item" [isMobileView]="isMobileView" [showAll]="showAll">
                        </app-expense-analysis-item>
                    </li>
                    <li *ngIf="!isLoading && dataList.length == 0">
                        <div class="mcv-data-list-cell mcv-data-list-cell-align-center">
                            <span>No items found</span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>