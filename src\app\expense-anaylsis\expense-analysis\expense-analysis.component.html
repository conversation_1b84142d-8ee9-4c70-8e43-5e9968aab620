<div class="data-filter-row">
    <div class="filter-header"
        *ngIf="selectedParticulars.length > 0 || selectedPayTo.length > 0 || selectedApprovedBy.length > 0 || selectedHead.length > 0 || selectedHead.length > 0 || selectedParticulars.length > 0 || selectedPayTo.length > 0">
        <h6 class="font-focused ">Filters:</h6>
    </div>
    <h6 *ngIf="selectedParticulars.length > 0">
        <b>Particulars:</b> {{selectedParticulars.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('project')">✖</span>
    </h6>
    <h6 *ngIf="selectedPayTo.length > 0">
        <b>Project Status:</b> {{selectedPayTo.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('projectStatus')">✖</span>
    </h6>
    <h6 *ngIf="selectedApprovedBy.length > 0">
        <b>Partner:</b> {{selectedApprovedBy.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('partner')">✖</span>
    </h6>
    <h6 *ngIf="selectedHead.length > 0">
        <b>Client:</b> {{selectedHead.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('client')">✖</span>
    </h6>
    <h6 (click)="resetFilter()"
        *ngIf="selectedParticulars.length > 0 || selectedPayTo.length > 0 || selectedApprovedBy.length > 0 || selectedHead.length > 0 || selectedHead.length > 0 || selectedParticulars.length > 0 || selectedPayTo.length > 0">
        <b> Clear All</b>

    </h6>



</div>
<div class="expense-analysis-wrapper">
    <table>
        <thead>
            <tr>
                <th colspan="2"  [ngClass]="{'sort': sortState['activeColumn'] === 'expenseDate'}">
                    <div class="analysis-table-header" (click)="sortData('expenseDate')">
                        <h6>Date</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'expenseDate' && sortState['expenseDate'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'expenseDate' && sortState['expenseDate'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'expenseDate' || !sortState['expenseDate']">import_export</mat-icon>
                    </div>

                </th>
                <th colspan="2" mat-button [matMenuTriggerFor]="headMenu"
                    [ngClass]="{'filter': selectedHead.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Head</h6>
                        <mat-icon>filter_alt</mat-icon>

                        <mat-menu #headMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="headKeySearch"
                                        (input)="filterDistinctHead()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('head')">
                                {{ isAllSelected('head') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let head of filteredHead"
                                (click)="$event.stopPropagation(); toggleSelection(head, 'head')">
                                <mat-checkbox [checked]="selectedHead.includes(head)">{{ head
                                    }}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>

                </th>

                <th colspan="2" mat-button [matMenuTriggerFor]="particularsMenu"
                    [ngClass]="{'filter': selectedParticulars.length > 0}">
                    <div class="analysis-table-header">
                        <h6>Particulars</h6>
                        <mat-icon>filter_alt</mat-icon>

                        <mat-menu #particularsMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="particularsKeySearch"
                                        (input)="filterDistinctParticulars()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('particulars')">
                                {{ isAllSelected('particulars') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let particulars of filteredParticulars"
                                (click)="$event.stopPropagation(); toggleSelection(particulars, 'particulars')">
                                <mat-checkbox [checked]="selectedParticulars.includes(particulars)">{{ particulars
                                    }}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>
                </th>

                <th colspan="2" mat-button [matMenuTriggerFor]="payToMenu"
                    [ngClass]="{'filter': selectedPayTo.length > 0}">


                    <div class="analysis-table-header">
                        <h6>Pay To</h6>
                        <mat-icon>filter_alt</mat-icon>

                        <mat-menu #payToMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="payToKeySearch"
                                        (input)="filterDistinctPayTo()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('payTo')">
                                {{ isAllSelected('payTo') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let payTo of filteredPayTo"
                                (click)="$event.stopPropagation(); toggleSelection(payTo, 'payTo')">
                                <mat-checkbox [checked]="selectedPayTo.includes(payTo)">{{ payTo
                                    }}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>
                </th>
                <th colspan="2">Transaction Mode</th>
                <th colspan="2"  [ngClass]="{'sort': sortState['activeColumn'] === 'transactionDate'}">
                    <div class="analysis-table-header" (click)="sortData('transactionDate')">
                        <h6>Transaction Date</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'transactionDate' && sortState['transactionDate'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'transactionDate' && sortState['transactionDate'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'transactionDate' || !sortState['transactionDate']">import_export</mat-icon>
                    </div>
                </th>
                <th  [ngClass]="{'sort': sortState['activeColumn'] === 'expenseAmount'}">
                    <div class="analysis-table-header" (click)="sortData('expenseAmount')">
                        <h6>Expense Amount</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'expenseAmount' && sortState['expenseAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'expenseAmount' && sortState['expenseAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'expenseAmount' || !sortState['expenseAmount']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'amountDr'}">
                    <div class="analysis-table-header" (click)="sortData('amountDr')">
                        <h6>Amount Dr</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountDr' && sortState['amountDr'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountDr' && sortState['amountDr'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'amountDr' || !sortState['amountDr']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'amountCr'}">
                    <div class="analysis-table-header" (click)="sortData('amountCr')">
                        <h6>Amount Cr</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountCr' && sortState['amountCr'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountCr' && sortState['amountCr'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'amountCr' || !sortState['amountCr']">import_export</mat-icon>
                    </div>
                </th>
                <th [ngClass]="{'sort': sortState['activeColumn'] === 'amountBalance'}">
                    <div class="analysis-table-header" (click)="sortData('amountBalance')">
                        <h6>Balance</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountBalance' && sortState['amountBalance'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'amountBalance' && sortState['amountBalance'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'amountBalance' || !sortState['amountBalance']">import_export</mat-icon>
                    </div>
                </th>
                <th colspan="2" mat-button [matMenuTriggerFor]="approvedByMenu"
                    [ngClass]="{'filter': selectedApprovedBy.length > 0}">
                    <div class="analysis-table-header">
                        <h6> Approved By</h6>
                        <mat-icon>filter_alt</mat-icon>
                        <mat-menu #approvedByMenu="matMenu">
                            <div class="search-container p-1">
                                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                    <input matInput placeholder="Search" [(ngModel)]="approvedByKeySearch"
                                        (input)="filterDistinctPayTo()" />
                                    <mat-icon class="clear-icon" matSuffix
                                        (click)="clearSearch($event)">close</mat-icon>
                                </mat-form-field>
                            </div>
                            <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('approvedBy')">
                                {{ isAllSelected('approvedBy') ? 'Deselect All' : 'Select All' }}
                            </button>
                            <mat-option *ngFor="let approvedBy of filteredApprovedBy"
                                (click)="$event.stopPropagation(); toggleSelection(approvedBy, 'approvedBy')">
                                <mat-checkbox [checked]="selectedApprovedBy.includes(approvedBy)">{{ approvedBy
                                    }}</mat-checkbox>
                            </mat-option>
                        </mat-menu>
                    </div>

                </th>
            </tr>
            <tr class="total">
                <td colspan="12">Total</td>
                <td class="text-right">{{ totalExpenseAmount | currency:'INR':'symbol':'0.0' }}</td>
                <td class="text-right">{{ totalAmountDr | currency:'INR':'symbol':'0.0' }}</td>
                <td class="text-right">{{ totalAmountCr | currency:'INR':'symbol':'0.0' }}</td>
                <td class="text-right">{{ totalAmountBalance | currency:'INR':'symbol':'0.0' }}</td>
                <td></td>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let item of dataList">
                <td colspan="2">
                    <div class="mcv-data-list-cell">
                        <span>{{ item.expenseDate | date: 'dd MMM y HH:mm' }}</span>
                    </div>
                </td>
                <td colspan="2">
                    <div class="mcv-data-list-cell" [ngClass]="{'mcv-data-list-cell-align-right': isMobileView}">
                        <span>{{ item.expenseHead }}</span>
                    </div>
                </td>
                <td colspan="2">
                    <div class="mcv-data-list-cell">
                        <span>{{ item.particulars }}</span>
                    </div>
                </td>
                <td colspan="2">
                    <div class="mcv-data-list-cell" [ngClass]="{'mcv-data-list-cell-align-right': isMobileView}">
                        <span>{{ item.payTo }}</span>
                    </div>
                </td>
                <td colspan="2">
                    <div class="mcv-data-list-cell">
                        <span>{{ item.transactionMode }}</span>
                    </div>
                </td>
                <td colspan="2">
                    <div class="mcv-data-list-cell" [ngClass]="{'mcv-data-list-cell-align-right': isMobileView}">
                        <span>{{ item.transactionDate | date: 'dd MMM y' }}</span>
                    </div>
                </td>
                <td>
                    <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                        <span>{{ item.expenseAmount | currency:'INR':'symbol':'0.0' }}</span>
                    </div>
                </td>
                <td>
                    <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                        <span>{{ item.amountDr | currency:'INR':'symbol':'0.0' }}</span>
                    </div>
                </td>
                <td>
                    <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                        <span>{{ item.amountCr | currency:'INR':'symbol':'0.0' }}</span>
                    </div>
                </td>
                <td>
                    <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                        <span *ngIf="item.typeFlag == 1">{{ item.amountBalance | currency:'INR':'symbol':'0.0' }}</span>
                    </div>
                </td>
                <td>
                    <div class="mcv-data-list-cell mcv-data-list-cell-align-right">
                        <span>{{ item.approvedBy }}</span>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>


<app-footer>
    <div class="nav-footer-actions">
        <div>
            <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
        </div>
      

        <div>


            <button mat-raised-button (click)="onExportExcel()" matTooltip="Export excel" aria-label="export excel">
                Export Excel
            </button>

        </div>
    </div>
    <div class="nav-footer-mobile-actions">


        <button mat-icon-button class="btn btn-dark" appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>
    </div>

    <div class="nav-filters">
        <div class="inline-list">
           
            <mat-form-field appearance="outline">
                <mat-label>Select Range</mat-label>
                <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                    <input matStartDate placeholder="Start date" formControlName="start" readonly />
                    <input matEndDate placeholder="End date" formControlName="end" readonly />
                </mat-date-range-input>
                <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
                <mat-date-range-picker #rangePicker1></mat-date-range-picker>
            </mat-form-field>
       
        </div>
    </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav>
        <div>
            <mat-form-field appearance="outline">
                <mat-label>Select Range</mat-label>
                <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
                    <input matStartDate placeholder="Start date" formControlName="start" readonly />
                    <input matEndDate placeholder="End date" formControlName="end" readonly />
                </mat-date-range-input>
                <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
                <mat-date-range-picker #rangePicker1></mat-date-range-picker>
            </mat-form-field>
        </div>

</app-mcv-filter-sidenav>