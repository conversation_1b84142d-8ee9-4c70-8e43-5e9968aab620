<div class="project-progress-chart">
    <div class="project-progress-chart-header">
        <div class="legend"
        [ngClass]="{'red':totalKPI < 3,'orange':totalKPI >= 3 && totalKPI < 5,'green':totalKPI >= 5 && totalKPI < 7,'purple':totalKPI > 7}">

            <div class="legend-label">
                <small>KPI</small>
                <!-- <span class="legend-color" [style.background-color]="COLOR_DARK_BLUE"></span> -->

            </div>

            <h6 class="legend-total">
                <mat-icon *ngIf="totalKPI < 3">trending_down</mat-icon>
                <mat-icon *ngIf="totalKPI >= 3 && totalKPI < 5">trending_flat</mat-icon>
                <mat-icon *ngIf="totalKPI >= 5 && totalKPI < 7">trending_up</mat-icon>
                <mat-icon *ngIf="totalKPI >= 7">diamond</mat-icon>
                {{totalKPI | number:'1.0-3'}}X
            </h6>
        </div>
        <div class="legend bg-vhr-consumed" >

            <div class="legend-label">
                <small>vHR Cost</small>
                <span class="legend-color bg-vhr-consumed" ></span>
            </div>

            <h6 class="legend-total">{{totalVHrCost | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>

        <div class="legend bg-vhr-x" >


            <div class="legend-label">
                <small>5x Cost</small>
                <span class="legend-color bg-vhr-x" ></span>
            </div>

            <h6 class="legend-total">{{total5xCost | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>
        <div class="legend bg-payment-received" >


            <div class="legend-label">
                <small>Payment Recieved</small>
                <span class="legend-color bg-payment-received" ></span>
            </div>

            <h6 class="legend-total">{{totalPaymentAmount | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>

        <div class="legend bg-payment-due" >

            <div class="legend-label">
                <small>Proforma Sent</small>
                <span class="legend-color bg-payment-due" ></span>

            </div>

            <h6 class="legend-total">{{totalProformaAmount | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>



        <div class="legend bg-package-completed" >

            <div class="legend-label">
                <small>Package Sent</small>
                <span class="legend-color bg-package-completed" ></span>
            </div>
            <h6 class="legend-total">{{totalPackageAmount | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>


        <div class="legend bg-package-active" >

            <div class="legend-label">
                <small>Package Active</small>
                <span class="legend-color bg-package-active"></span>
            </div>
            <h6 class="legend-total">{{totalPackageActiveAmount | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>


        <div class="legend bg-package-proposed">

            <div class="legend-label">
                <small>Package Proposed</small>
                <span class="legend-color bg-package-proposed" ></span>
            </div>
            <h6 class="legend-total">{{totalPackageProposed | currency:'INR':'symbol':'1.0-0'}}</h6>
        </div>


    </div>

    <div class="project-progress-chart-list">
        <div class="project-progress-chart-item" *ngFor="let cashflowData of dataList"
            (click)="openProgressDialog(cashflowData)">
            <div class="project">
                <div class="project-title">
                    <h6 class="font-focused">{{cashflowData.code}} | {{cashflowData.project}}</h6>

                </div>
                <div class="project-details">
                    <div>
                        <span class="badge {{getStatusColor(cashflowData.statusFlag)}}">{{cashflowData.status}}</span>
                    </div>
                    <div>
                        <small>Fee: </small>
                        <h6>{{cashflowData.companyFee | currency:'INR':'symbol':'1.0-0'}}</h6>

                    </div>
                    <div>
                        <small>Partner: </small>
                        <h6>{{cashflowData.partner}}</h6>
                    </div>

                </div>
            </div>
            <div class="project-progress">
                <app-project-progress-bars [cashflowData]="cashflowData"></app-project-progress-bars>
            </div>
        </div>
    </div>
</div>
<app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <button mat-icon-button (click)="refresh()" matTooltip="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>
        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>



        <button mat-icon-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <mat-icon>file_download</mat-icon>
        </button>
    </div>

    <div class="nav-footer-mobile-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <button mat-icon-button (click)="refresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>
        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>
        <button mat-icon-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <mat-icon>file_download</mat-icon>
        </button>
    </div>

</app-footer>

<app-mcv-filter-sidenav #sidenav (refreshFiltersEvent)="refreshFilters()">

    <mat-form-field appearance="outline">
        <mat-select [formControl]="companyFC" placeholder="Company">
            <mat-option *ngFor="let item of companyOptions" [value]="item">
                {{item.title }}
            </mat-option>
        </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
        <mat-select [formControl]="statusFC" multiple placeholder="Status">
            <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
        </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
        <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
            <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
        </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
        <mat-select [formControl]="sortFC" placeholder="Sort">
            <mat-option *ngFor="let item of sortOptions" [value]="item">
                <!-- <mat-icon matSuffix *ngIf="item.direction=='desc'">arrow_upward</mat-icon>
          <mat-icon matSuffix *ngIf="item.direction=='asc'">arrow_downward</mat-icon> -->
                {{item }}
            </mat-option>
        </mat-select>
    </mat-form-field>

</app-mcv-filter-sidenav>