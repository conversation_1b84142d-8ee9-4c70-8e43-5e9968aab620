<div class="mat-dialog-header mcv-design-script-dialog-header">
  <h5 class="font-focused">{{dialogTitle}}</h5>
  <div class="d-flex align-items-center">
    <button mat-icon-button (click)="onReplaceDataCard()" *ngIf="dataCard && dataCard.category" matTooltip="Replace DataCard">
      <mat-icon>published_with_changes</mat-icon>
    </button>

    <button mat-icon-button color="warn" class="ml-2" matTooltip="Delete" aria-label="Delete"
      (click)="onUnlinkDataCard()">
      <mat-icon>delete</mat-icon>
    </button>
    <button mat-icon-button aria-label="close" (click)="onClose()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  
</div>
<mat-dialog-content >
  <div class="header-controls">
    <button 
      matTooltip="Toggle Images" 
      [ngClass]="{ 'toggle-on': !isAttachmentToggleHidden, 'toggle-off': isAttachmentToggleHidden }"
      color="primary" 
      mat-raised-button 
      (click)="onToggleImagePreview()">
      <span>{{ isAttachmentToggleHidden ? 'Show All Photos' : 'Hide All Photos' }}</span>
    </button>
  
    <button 
      matTooltip="Toggle Attributes" 
      [ngClass]="{ 'toggle-on': !isToggleHidden, 'toggle-off': isToggleHidden }"
      color="primary" 
      mat-raised-button 
      (click)="onToggleDataCard()">
      <span>{{ isToggleHidden ? 'Show All Attributes' : 'Hide All Attributes' }}</span>
    </button>
  </div>
  
  <div class="design-script-attachment-form-wrapper">
    <div class="design-script-attachment-file-slider-wrapper">
      <div class="image-container">
        <ng-container *ngFor="let image of dataCard.attachments">
          <div class="image-wrapper"
            [ngClass]="{'more-then-two-images':dataCard.attachments.length > 1, 'isHidden':image.isHidden}">
            <img (click)="onPreview(image)"
              src="{{image.thumbUrl || image.url || 'assets/images/file-not-found.png'}}" />
            <div class="image-overlay">
              <button mat-icon-button (click)="toggleHideAttachment(image)" matTooltip="Hide"
                *ngIf="!image.isHidden"><mat-icon>visibility</mat-icon></button>
              <button mat-icon-button (click)="toggleHideAttachment(image)" matTooltip="Show"
                *ngIf="image.isHidden"><mat-icon>visibility_off</mat-icon></button>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="design-script-attachment-details" [formGroup]="form" autocomplete="off">
      <div class="design-script-edit-details row">
        <div class="col-6">
          <h6 class="font-focused">Mat Tag<mat-icon>edit</mat-icon></h6>
          <mat-form-field appearance="outline">
            <input  id="gfcTag" placeholder="Mat Tag" aria-label="Mat Tag" matInput
              formControlName="gfcTag" (change)="onCellChange('gfcTag')"/>
            <mat-error>{{ getErrorMessage(f.gfcTag) }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="detail-tag-wrapper col-6">
          <h6 class="font-focused">Tags <mat-icon>edit</mat-icon></h6>
          <app-mcv-base-search-tag-editor [tags]="dataCard.searchTags" [tagOptions]="this.tagOptions"
            [IsPermissionEdit]="IsPermissionEdit" [showTag]="true" (update)="onTagsUpdate($event)">
          </app-mcv-base-search-tag-editor>
        </div>
        <div class="col-12">
          <h6 class="font-focused">Description<mat-icon>edit</mat-icon></h6>
          <mat-form-field appearance="outline">
            <textarea aria-label="description" matInput formControlName="description" cdkTextareaAutosize
              #autosize="cdkTextareaAutosize" (change)="onCellChange('description')"></textarea>
            <mat-error>{{ getErrorMessage(f.description) }}</mat-error>
          </mat-form-field>
        </div>
        <!-- <div class="col-md-12 col-12">
          <div class="row">
            <div class="col-md-6 col-12">
              <h6 class="font-focused">GFC Tag<mat-icon>edit</mat-icon></h6>
              <mat-form-field appearance="outline">
                <input  id="gfcTag" placeholder="gfcTag" aria-label="GFC Tag" matInput
                  formControlName="gfcTag" />
                <mat-error>{{ getErrorMessage(f.gfcTag) }}
                </mat-error>
              </mat-form-field>
            </div>
            <div class="detail-tag-wrapper col-md-6 col-12">
              <h6 class="font-focused">Tags <mat-icon>edit</mat-icon></h6>
              <app-mcv-base-search-tag-editor [tags]="dataCard.searchTags" [tagOptions]="this.tagOptions"
                [IsPermissionEdit]="IsPermissionEdit" [showTag]="true" (update)="onTagsUpdate($event)">
              </app-mcv-base-search-tag-editor>
            </div>
          </div>
        </div> -->

        <!-- <div class="col-md-12 col-12">
          <div class="row">
            <div class="col-12">
              <h6 class="font-focused">Description<mat-icon>edit</mat-icon></h6>
              <mat-form-field appearance="outline">
                <textarea aria-label="description" matInput formControlName="description" cdkTextareaAutosize
                  #autosize="cdkTextareaAutosize"></textarea>
                <mat-error>{{ getErrorMessage(f.description) }}</mat-error>
              </mat-form-field>
            </div>
          </div>
        </div> -->
      </div>
      <div class="detail-attribute-wrapper">
        <div class="attribute-wrapper" *ngIf="dataCard.isReadOnly">
          <div class="attribute-action-container row" style="height:2.7rem">
            <div class="attribute-container  ">
              <small class="attribute-key">
                Title
              </small>
              <div class="attribute-value">
                {{dataCard.title}}
              </div>
            </div>
          </div>
          <div class="attribute-action-container" style="height:2.7rem">
            <div class="attribute-container ">
              <small class="attribute-key">
                Subtitle
              </small>
              <div class="attribute-value">
                {{dataCard.subtitle}}
              </div>
            </div>
          </div>
          <ng-container *ngFor="let att of dataCard.attributes; let i = index">
            <div class="attribute-action-container half-attribute" [ngClass]="{'disable-wrapper':att.isHidden}">
              <div class="attribute-container " *ngIf="att.attributeValue">
                <small class="attribute-key">
                  {{att.attributeKey}}
                </small>
                <div class="attribute-value">
                  {{att.attributeValue}}
                </div>
              </div>
              <button mat-icon-button (click)="toggleHide(att)" matTooltip="Hide"
                *ngIf="!att.isHidden"><mat-icon>visibility</mat-icon></button>
              <button mat-icon-button (click)="toggleHide(att)" matTooltip="Show"
                *ngIf="att.isHidden"><mat-icon>visibility_off</mat-icon></button>
            </div>
          </ng-container>
        </div>
        <table class="design-script-attachment-form-table" *ngIf="!dataCard.isReadOnly">
          <thead>
            <tr class="sticky-row">
              <th colspan="3">
                <h5 class="font-focused">Attributes</h5>
              </th>
            </tr>
            <tr class="sticky-row">
              <th>Key</th>
              <th>Value</th>
              <th></th>
            </tr>
          </thead>
          <tbody [formGroup]="form">
            <ng-container formArrayName="attributes">
              <ng-container *ngFor="let control of attributes.controls; let i = index">
                <ng-container [formGroupName]="i">
                  <tr>
                    <td class="design-script-attachment-form-cell-td" [ngClass]="{'disable-div':dataCard.isReadOnly}">
                      <div class="design-script-attachment-form-cell-content">
                        <mat-icon *ngIf="isInputInvalid(i,'attributeKey')"
                          [matTooltip]="getErrorMessage(getAttributeFormControl(i,'attributeKey'))">
                          report_problem
                        </mat-icon>

                        <div class="design-script-attachment-form-cell-input">
                     
                          <input matInput  placeholder="Enter Attribute Key" formControlName="attributeKey"
                            style="cursor: pointer;" (input)="onAttributeInputChange(i,'attributeKey')"
                            aria-label="Attribute Key">
                        </div>
                      </div>
                    </td>
                    <td [ngClass]="{'disable-div':dataCard.isReadOnly}">
                      <div class="design-script-attachment-form-cell-content">
                        <mat-icon *ngIf="isInputInvalid(i,'attributeValue')"
                          [matTooltip]="getErrorMessage(getAttributeFormControl(i,'attributeValue'))">
                          report_problem
                        </mat-icon>
                        <div class="design-script-attachment-form-cell-input">
                       
                          <textarea matInput  placeholder="Enter Attribute Value" style="cursor: pointer;"
                            formControlName="attributeValue" aria-label="Attribute Value" cdkTextareaAutosize
                            #autosize="cdkTextareaAutosize" [cdkAutosizeMinRows]="1">
                                                      </textarea>
                        </div>
                      </div>
                    </td>
                    <td class="design-script-attachment-form-cell-td" *ngIf="!dataCard.isReadOnly"
                      [ngClass]="{'disable-div':dataCard.isReadOnly}">
                      <mat-icon (click)="removeAttribute(i)">close</mat-icon>
                    </td>
                  </tr>
                </ng-container>
              </ng-container>
            </ng-container>
            <tr *ngIf="!dataCard.isReadOnly" [ngClass]="{'disable-div':dataCard.isReadOnly}">
              <td colspan="3">
                <mat-icon class="w-100" matTooltip="Add Attributes" (click)="addAttribute()">add</mat-icon>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</mat-dialog-content>