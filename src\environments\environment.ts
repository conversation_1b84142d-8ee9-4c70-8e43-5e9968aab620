
export const environment = {
  production: false,
  appVersion: require('../../package.json').version + '-dev',
  origin: window.location.origin,
  angularPath: '',
  logoUrl: "https://blob.mycockpitview.in/assets/logo.png",
  azureBlobStorageRoot: 'blob.core.windows.net',
  vapidPublicKey: 'BEvhofBzUiz0ZcVkzF4IGO69A2L4ltWgdZfNgVO3AZHnAZFH8BT2k2KXoIirJfTdk179t8Bk45vdFuCvtdpaf-4',

  ////  for test with Prod API .NET8
  // apiPath: 'https://mycockpitview.in/api',

  //// for staging .NET8
  // apiPath: 'https://mycockpitview.in/staging-api',

    //// for test with Local API .NET8
    apiPath: 'https://localhost:7024',
};
