import { Component, inject, OnInit } from '@angular/core';
import { AppPermissions } from 'src/app/app.permissions';
import { Contact } from 'src/app/contact/models/contact';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { AuthService } from 'src/app/auth/services/auth.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { PartnerLastBiteBannerComponent } from '../../../cockpit-banners/components/partner-last-bite-banner/partner-last-bite-banner.component';
import { TotalLastBiteBannerComponent } from '../../../cockpit-banners/components/total-last-bite-banner/total-last-bite-banner.component';
import { CopsBannerComponent } from '../../../cockpit-banners/components/cops-banner/cops-banner.component';
import { ExpenseBannerComponent } from '../../../cockpit-banners/components/expense-banner/expense-banner.component';
import { IncomeBannerComponent } from '../../../cockpit-banners/components/income-banner/income-banner.component';
import { PaymentsReceivedYearlyComponent } from '../../../cockpit-banners/components/payments-received-yearly/payments-received-yearly.component';
import { InquiryConvertedYearlyBannerComponent } from '../../../cockpit-banners/components/inquiry-converted-yearly-banner/inquiry-converted-yearly-banner.component';
import { InquiryDiscardedBannerComponent } from '../../../cockpit-banners/components/inquiry-discarded-banner/inquiry-discarded-banner.component';
import { InquiryConvertedBannerComponent } from '../../../cockpit-banners/components/inquiry-converted-banner/inquiry-converted-banner.component';
import { InquiryPreproposalBannerComponent } from '../../../cockpit-banners/components/inquiry-preproposal-banner/inquiry-preproposal-banner.component';
import { InquiryPendingBannerComponent } from '../../../cockpit-banners/components/inquiry-pending-banner/inquiry-pending-banner.component';
import { InquiryChartComponent } from '../../../cockpit-banners/components/inquiry-chart/inquiry-chart.component';
import { PackageCountBannerComponent } from '../../../cockpit-banners/components/package-count-banner/package-count-banner.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { BreakBannerComponent } from '../../../cockpit-banners/components/break-banner/break-banner.component';
import { LeavesEmergencyBannerComponent } from '../../../cockpit-banners/components/leaves-emergency-banner/leaves-emergency-banner.component';
import { LeavesAppliedBannerComponent } from '../../../cockpit-banners/components/leaves-applied-banner/leaves-applied-banner.component';
import { MeetingsMinutesBannerComponent } from '../../../cockpit-banners/components/meetings-minutes-banner/meetings-minutes-banner.component';
import { MeetingsAttendedBannerComponent } from '../../../cockpit-banners/components/meetings-attended-banner/meetings-attended-banner.component';
import { MeetingsScheduledBannerComponent } from '../../../cockpit-banners/components/meetings-scheduled-banner/meetings-scheduled-banner.component';
import { AgendaDelayedBannerComponent } from '../../../cockpit-banners/components/agenda-delayed-banner/agenda-delayed-banner.component';
import { AgendaPendingBannerComponent } from '../../../cockpit-banners/components/agenda-pending-banner/agenda-pending-banner.component';
import { PackageDelayedBannerComponent } from '../../../cockpit-banners/components/package-delayed-banner/package-delayed-banner.component';
import { PackageActiveBannerComponent } from '../../../cockpit-banners/components/package-active-banner/package-active-banner.component';
import { PackageSubmitedBannerComponent } from '../../../cockpit-banners/components/package-submited-banner/package-submited-banner.component';
import { TaskCompletedBannerComponent } from '../../../cockpit-banners/components/task-completed-banner/task-completed-banner.component';
import { TaskDueBannerComponent } from '../../../cockpit-banners/components/task-due-banner/task-due-banner.component';
import { TaskDelayedBannerComponent } from '../../../cockpit-banners/components/task-delayed-banner/task-delayed-banner.component';
import { PartnerPaymentBannerComponent } from '../partner-payment-banner/partner-payment-banner.component';
import { NgIf, NgFor } from '@angular/common';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ProjectLastBite } from 'src/app/mcv-last-bite/models/project-last-bite.model';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-cockpit-metrics',
  templateUrl: './cockpit-metrics.component.html',
  styleUrls: ['./cockpit-metrics.component.scss'],
  standalone: true,
  imports: [NgIf, PartnerPaymentBannerComponent, TaskDelayedBannerComponent, TaskDueBannerComponent, TaskCompletedBannerComponent, PackageSubmitedBannerComponent, PackageActiveBannerComponent, PackageDelayedBannerComponent, AgendaPendingBannerComponent, AgendaDelayedBannerComponent, MeetingsScheduledBannerComponent, MeetingsAttendedBannerComponent, MeetingsMinutesBannerComponent, LeavesAppliedBannerComponent, LeavesEmergencyBannerComponent, BreakBannerComponent, MatExpansionModule, PackageCountBannerComponent, InquiryChartComponent, InquiryPendingBannerComponent, InquiryPreproposalBannerComponent, InquiryConvertedBannerComponent, InquiryDiscardedBannerComponent, InquiryConvertedYearlyBannerComponent, PaymentsReceivedYearlyComponent, IncomeBannerComponent, ExpenseBannerComponent, CopsBannerComponent, TotalLastBiteBannerComponent, NgFor, PartnerLastBiteBannerComponent, FooterComponent]
})
export class CockpitMetricsComponent implements OnInit {

  private readonly projectService = inject(ProjectApiService);
  private readonly utilityService = inject(UtilityService);

  partnerOptions: Contact[] = [];

  get isPermissionLastBiteAnalysis(): boolean {
    return this.authService.isInAnyRole([this.permissions.COCKPIT_LAST_BITE_ANALYSIS]);
  }

  get isPermissionMaster(): boolean {
    return this.authService.isInAnyRole([this.permissions.MASTER]);
  }

  get projectPreProposalFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_PREPROPOSAL.toString() },
    ]
  }

  get projectInprogressFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_INPROGRESS.toString() },
    ]
  }

  get projectInquiryFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_INQUIRY.toString() },
    ]
  }

  get projectDiscardFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_DISCARD.toString() },
    ]
  }

  get projectDueFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_DUE.toString() },
    ]
  }

  get projectOnHoldFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_ONHOLD.toString() },
    ]
  }

  get projectLockedFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_LOCKED.toString() },
    ]
  }

  get projectCompletedFilters() {
    return [
      { key: 'statusFlag', value: this.projectService.PROJECT_STATUS_FLAG_COMPLETED.toString() },
    ]
  }

  constructor(
    private permissions: AppPermissions,
    private authService: AuthService,
    private contactService: ContactApiService
  ) { }

  ngOnInit(): void {
    // this.getLastBite();
    if (this.isPermissionMaster) {
      this.getPartnerOptions();
    }
  }

  private getPartnerOptions() {
    let partnerFilter = [
      { key: 'usersOnly', value: 'true' },
      { key: 'projectPartnersOnly', value: 'true' }
    ];
    this.contactService.get(partnerFilter).subscribe((data) => this.partnerOptions = data);
  }

  lastBiteData: ProjectLastBite[] = [];
  lastBiteTotal = new ProjectLastBite()
  private async getLastBite() {
    let filters = [
      { key: 'rangeStart', value: this.utilityService.getQuarterStart().toISOString() },
      { key: 'rangeEnd', value: this.utilityService.getQuarterEnd().toISOString() },
      { key: 'contactID', value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '0' },
    ];
    this.lastBiteData = await firstValueFrom(this.projectService.getAnalysis('LastBite', filters));

    this.lastBiteTotal.lastReceivedPaymentAmount = this.lastBiteData.reduce((acc, curr) => acc + curr.lastReceivedPaymentAmount, 0);
      this.lastBiteTotal.partnerAmount = this.lastBiteData.reduce((acc, curr) => acc + curr.partnerAmount, 0);
      this.lastBiteTotal.vHrCost = this.lastBiteData.reduce((acc, curr) => acc + curr.vHrCost, 0);
      this.lastBiteTotal.lastBiteAmount = this.lastBiteData.reduce((acc, curr) => acc + curr.lastBiteAmount, 0);
  }
}
