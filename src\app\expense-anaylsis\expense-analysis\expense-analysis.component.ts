import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AppPermissions } from 'src/app/app.permissions';
import { Expense } from 'src/app/expense/models/expense';
import { AuthService } from 'src/app/auth/services/auth.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ExpenseApiService } from 'src/app/expense/services/expense-api.service';
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { ExpenseAnalysisItemComponent } from '../expense-analysis-item/expense-analysis-item.component';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { HeaderComponent } from '../../mcv-header/components/header/header.component';
import { FooterComponent } from "../../shared/components/footer/footer.component";
import { MatMenuModule } from '@angular/material/menu';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { firstValueFrom } from 'rxjs';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
    selector: 'app-expense-analysis',
    templateUrl: './expense-analysis.component.html',
    styleUrls: ['./expense-analysis.component.scss'],
    standalone: true,
    imports: [HeaderComponent,McvFilterSidenavComponent, MatFormFieldModule, MatDatepickerModule,MatMenuModule,MatOptionModule,MatCheckboxModule,FormsModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatButtonModule, MatTooltipModule, ExpenseAnalysisItemComponent, CommonModule, FooterComponent]
})
export class ExpenseAnalysisComponent implements OnInit {
  pagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 30,
    filters: [
      // { key: 'contactID', value: this.authService.currentUserStore.entity.id.toString() },
      // { key: 'statusFlag', value: 1 },
      { key: 'rangeStart', value: this.utility.getMonthStart().toISOString() },
      { key: 'rangeEnd', value: this.utility.getMonthEnd().toISOString() },
    ],
    searchKey: null,
    sort: 'expenseDate desc',
    route: '',
    showAll: false,
    showAssigned: false,
    groupBy: [],
    keyPropertyName: ''
  });

  totalExpenseAmount = 0;
  totalAmountDr = 0;
  totalAmountCr = 0;
  totalAmountBalance = 0;
  dataList: Expense[] = [];
  total!: Expense;
  showAll: boolean = false;
  isPermissionShowAll: boolean = false;
  isPermissionExcel: boolean = false;
  dateFilters!: FormGroup;
  searchFilter!: FormControl;
  currentPage = 3;
  pageSize = 3;
  // dataList: WFTaskAnalysis[] = [];
  isMobileView: boolean = false;
  isLoading: boolean = false;
  totalRecordsCount = 0;
  totalPages = 0;
  selectedParticulars: string[] = [];
  filteredParticulars: string[] = [];
  distinctParticulars: string[] = [];
  particularsKeySearch: string = '';
  filteredPayTo: string[] = [];
  selectedPayTo: string[] = [];
  distinctPayto: string[] = [];
  payToKeySearch: string = '';
  approvedByKeySearch: string = '';
  filteredApprovedBy: string[] = [];
  selectedApprovedBy: string[] = [];
  distinctApprovedBy: string[] = [];
  originalDataList: any[] = [];
  headKeySearch: string = '';
  filteredHead: string[] = [];
  selectedHead: string[] = [];
  distinctHead: string[] = [];
  sortState: { 
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    expenseDate: '',
    transactionDate: '',
    expenseAmount: '',
    amountDr: '',
    amountCr: '',
    amountBalance: ''
  };
  isSorted: boolean = false;

  constructor(
    private authService: AuthService,
    private utility: UtilityService,
    private permissions: AppPermissions,
    private entityService: ExpenseApiService,
  ) { }

  get headerTitle(): string { return this.entityService.EXPENSE_ANALYSIS_HEADER_TITLE; }

  ngOnInit() {
    this.isMobileView = this.utility.isMobileView;
    this.isPermissionShowAll = this.authService.isInAnyRole([this.permissions.TASK_ANALYSIS_SPECIAL_SHOW_ALL]);
    this.isPermissionExcel = this.authService.isInAnyRole([this.permissions.TASK_ANALYSIS_SPECIAL_EXCEL]);
    this.buildForm();
    if (this.entityService.isPermissionPettyCashEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_PETTY_CASH.toString() });
    }
    if (this.entityService.isPermissionRemunerationEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_REMUNERATION.toString() });
    }
    if (this.entityService.isPermissionInvestmentEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_INVESTMENT.toString() });
    }
    if (this.entityService.isPermissionTaxationEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_TAXATION.toString() });
    }
    if (this.entityService.isPermissionTravelEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_TRAVEL.toString() });
    }
    if (this.entityService.isPermissionEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_GENERAL.toString() });
    }

    this.getDataList(this.currentPage, this.pageSize);
  }

  buildForm() {
    this.dateFilters = new FormGroup({
      start: new FormControl(this.utility.getMonthStart()),
      end: new FormControl(this.utility.getMonthEnd())
    });

    this.dateFilters.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.pagedListConfig.filters = this.pagedListConfig.filters.filter(x => x.key !== 'rangeStart' && x.key != 'rangeEnd');
          this.pagedListConfig.filters.push({ key: 'rangeStart', value: value.start.toISOString() });
          this.pagedListConfig.filters.push({ key: 'rangeEnd', value: value.end.toISOString() });

          this.search();
        }
      });

    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value) {
          this.pagedListConfig.searchKey = value;
          this.search();
        }
      })
  }

  async getDataList(currentPage: number, pageSize: number) {
    this.isLoading = true;
    // this.getDataTotal();
    this.entityService.getAnalysis('full', this.pagedListConfig.filters, this.pagedListConfig.searchKey,
      this.pagedListConfig.sort)
      .subscribe(
        (data: Expense[]) => {
       
          // this.totalRecordsCount = data.total;
          // this.totalPages = data.pages;
          // this.listLoad.emit({ totalRecordsCount: this.totalRecordsCount });
          this.dataList = data; //this.utility.updatePagedList<WFTaskAnalysis>(data.list, this.dataList, 'wfTaskID');
          this.isLoading = false;
          data.forEach(x => {
            this.totalExpenseAmount += x.expenseAmount;
            this.totalAmountDr += x.amountDr;
            this.totalAmountCr += x.amountCr;
          })
          const _pettyCashData = data
            .filter(x => x.typeFlag == 1)
            .sort((b, a) => new Date(a.expenseDate).getTime() - new Date(b.expenseDate).getTime())
            .map(x => x.amountBalance);
          console.log('_pettyCashData', _pettyCashData);
          this.totalAmountBalance = _pettyCashData[0];
        },
        error => {
          this.isLoading = false;
        }
      );

      this.dataList = await firstValueFrom(this.entityService.getAnalysis('full', this.pagedListConfig.filters, this.pagedListConfig.searchKey,this.pagedListConfig.sort))
     
      this.getTotal();
      this.extractDistinctValues();
      this.originalDataList = [...this.dataList];
      
  }

  loadMoreRecords() {
    if (this.currentPage * this.pagedListConfig.pageSize < this.totalRecordsCount) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
    }
  }

  search() {
    this.currentPage = 0;
    this.dataList = [];
    this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
  }

  refresh() {
    this.pagedListConfig.searchKey = null;
    this.searchFilter.setValue(null);
    this.dataList = [];
    this.getDataList(0, (this.currentPage + 1) * this.pagedListConfig.pageSize);
  }

  onToggleShowAll() {
    this.showAll = !this.showAll;
    this.pagedListConfig.showAll = this.showAll;
    if (this.showAll) {
      this.pagedListConfig.filters = this.pagedListConfig.filters.filter(x => x.key !== 'contactID')
    } else {
      this.pagedListConfig.filters.push({ key: 'contactID', value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '' });
    }
    this.search();
  }

  onExportExcel() {
    this.entityService.exportAnalysisExcel(
      'full',
      this.pagedListConfig.filters,
      this.pagedListConfig.searchKey,
      this.pagedListConfig.sort
    );
  }

  filterDistinctParticulars() {
    const searchLower = this.particularsKeySearch.toLowerCase();
    this.filteredParticulars = this.distinctParticulars.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }
  filterDistinctPayTo() {
    const searchLower = this.payToKeySearch.toLowerCase();
    this.filteredPayTo = this.distinctPayto.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctApprovedBy() {
    const searchLower = this.approvedByKeySearch.toLowerCase();
    this.filteredApprovedBy = this.distinctApprovedBy.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }
  filterDistinctHead() {
    const searchLower = this.headKeySearch.toLowerCase();
    this.filteredHead = this.distinctHead.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: Event) {
    this.particularsKeySearch = '';
    this.filterDistinctParticulars();
  } 

  toggleSelectAll(filterType: 'particulars'  | 'payTo' | 'approvedBy'  | 'head'): void {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'particulars':
        this.selectedParticulars = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
      case 'payTo':
        this.selectedPayTo = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
      case 'approvedBy':
        this.selectedApprovedBy = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
      case 'head':
        this.selectedHead = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
      default:
        break;
    }
    
    this.applyFilters(); // Ensure filters update when selecting/deselecting all
  }

  getDistinctValues(filterType: 'particulars'  | 'payTo' | 'approvedBy' | 'head' ): string[] {
    switch (filterType) {
      case 'particulars':
        return this.distinctParticulars;
      case 'payTo':
        return this.distinctPayto;
      case 'approvedBy':
        return this.distinctApprovedBy;
      case 'head':
        return this.distinctHead;
      default:
        return [];
    }
  }

  isAllSelected(filterType: 'particulars'  | 'payTo'  | 'approvedBy' | 'head' ): boolean {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'particulars':
        return this.selectedParticulars.length === distinctValues.length && this.selectedParticulars.length > 0;
      case 'payTo':
        return this.selectedPayTo.length === distinctValues.length && this.selectedPayTo.length > 0;
      case 'approvedBy':
        return this.selectedApprovedBy.length === distinctValues.length && this.selectedApprovedBy.length > 0;
      case 'head':
        return this.selectedHead.length === distinctValues.length && this.selectedHead.length > 0;
      default:
        return false;
    }
  }

  toggleSelection(value: string, type: string) {
   
    if (type === 'particulars') {
      this.selectedParticulars.includes(value)
        ? this.selectedParticulars.splice(this.selectedParticulars.indexOf(value), 1)
        : this.selectedParticulars.push(value);
    } 
    else if (type === 'payTo') {
      this.selectedPayTo.includes(value)
        ? this.selectedPayTo.splice(this.selectedPayTo.indexOf(value), 1)
        : this.selectedPayTo.push(value);
    }
    else if (type === 'approvedBy') {
      this.selectedApprovedBy.includes(value)
        ? this.selectedApprovedBy.splice(this.selectedApprovedBy.indexOf(value), 1)
        : this.selectedApprovedBy.push(value);
    }
    else if (type === 'head') {
      this.selectedHead.includes(value)
        ? this.selectedHead.splice(this.selectedHead.indexOf(value), 1)
        : this.selectedHead.push(value);
    }
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if(this.pagedListConfig.searchKey){

      const searchLower = this.pagedListConfig.searchKey.toLowerCase();
  
     filteredList = filteredList.filter(item =>
      
        (item.particulars?.toLowerCase().includes(searchLower) || '') 
        ||
        (item.payTo?.toLowerCase().includes(searchLower) || '') 
        ||
        (item.approvedBy?.toLowerCase().includes(searchLower) || '')
        ||
        (item.expenseHead?.toLowerCase().includes(searchLower) || '')
      );
    }
  
    if (this.selectedParticulars.length > 0) {
      filteredList = filteredList.filter(item => this.selectedParticulars.includes(item.particulars));
    }
    else if (this.selectedPayTo.length > 0) {
      filteredList = filteredList.filter(item => this.selectedPayTo.includes(item.payTo));
    }
    else if (this.selectedApprovedBy.length > 0) {
      filteredList = filteredList.filter(item => this.selectedApprovedBy.includes(item.approvedBy));
    }
    else if (this.selectedHead.length > 0) {
      filteredList = filteredList.filter(item => this.selectedHead.includes(item.expenseHead));
    }

    this.dataList = filteredList;
    this.getTotal();
  }

 // Reset all filters
 resetFilter() {
  this.selectedParticulars = [];
  this.selectedPayTo = [];
 
  this.dataList = [...this.originalDataList]; // Restore full list
  this.getTotal();
}

clearSelection(type: string) {
    if (type === 'particulars') {
        this.selectedParticulars = [];
    }  
    else if (type === 'payTo') {
        this.selectedPayTo = [];
    }
    else if (type === 'approvedBy') {
        this.selectedApprovedBy = [];
    }
    this.applyFilters(); // Apply filters after clearing the selection
  }

  getTotal() {
    this.totalAmountBalance = 0;
    this.totalAmountCr = 0;
    this.totalAmountDr = 0;
    this.totalExpenseAmount = 0;
    this.dataList.forEach(x => {
      this.totalExpenseAmount += x.expenseAmount;
      this.totalAmountDr += x.amountDr;
      this.totalAmountCr += x.amountCr;
    })
  }

  extractDistinctValues(){
    this.distinctParticulars = [...new Set(this.dataList.map(item => item.particulars))];
    this.filteredParticulars = [...this.distinctParticulars];
    this.distinctApprovedBy = [...new Set(this.dataList.map(item => item.approvedBy))];
    this.filteredApprovedBy = [...this.distinctApprovedBy];
    this.distinctPayto = [...new Set(this.dataList.map(item => item.payTo))];
    this.filteredPayTo = [...this.distinctPayto];
    this.distinctHead = [...new Set(this.dataList.map(item => item.expenseHead))];
    this.filteredHead = [...this.distinctHead];
  }

  sortData(column: keyof Expense | '') {
    if (column === '') {
      // Reset to default (original data) but apply the filters again
      const dataToFilter = this.originalDataList;
  
      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item:any) => {
        return (
          (this.selectedApprovedBy.length === 0 || this.selectedApprovedBy.includes(item.approvedBy)) &&
          (this.selectedHead.length === 0 || this.selectedHead.includes(item.expenseHead)) && 
          (this.selectedParticulars.length === 0 || this.selectedParticulars.includes(item.particulars)) &&
          (this.selectedPayTo.length === 0 || this.selectedPayTo.includes(item.payTo))
        
        );
      });
  
      // Reset the sort state to default
      this.sortState = { 
        activeColumn: '',
        expenseDate: '',
        transactionDate: '',
        expenseAmount: '',
        amountDr: '',
        amountCr: '',
        amountBalance: ''
      };
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if no sorting is selected
    }
  
    // If the clicked column is already the active column, cycle through the three states
    const currentSort = this.sortState[column];
  
    if (currentSort === 'newFirst') {
      // If it's 'newFirst', change to 'oldFirst'
      this.sortState[column] = 'oldFirst';
    } else if (currentSort === 'oldFirst') {
      // If it's 'oldFirst', reset to default (no sorting)
      this.sortState[column] = '';
      this.sortState.activeColumn = '';
      const dataToFilter = this.originalDataList;
  
      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item:any) => {
        return (
            (this.selectedApprovedBy.length === 0 || this.selectedApprovedBy.includes(item.approvedBy)) &&
          (this.selectedHead.length === 0 || this.selectedHead.includes(item.expenseHead)) && 
          (this.selectedParticulars.length === 0 || this.selectedParticulars.includes(item.particulars)) &&
          (this.selectedPayTo.length === 0 || this.selectedPayTo.includes(item.payTo))
        );
      });
  
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if reset is selected
    } else {
      // If no sorting is applied, set it to 'newFirst' (ascending order)
      this.sortState[column] = 'newFirst';
    }
  
    // Set the active column
    this.sortState['activeColumn'] = column;
  
    // Reset other columns' sort state to '' (no sort)
    for (let key in this.sortState) {
      if (key !== column && key !== 'activeColumn') {
        this.sortState[key] = ''; // Reset other columns' sort state to no sort
      }
    }
  
    // Sorting logic: Compare dates for the active column
    const sortedData = [...this.dataList].sort((a, b) => {
      const dateA = new Date(a[column] as string);
      const dateB = new Date(b[column] as string);
  
      if (this.sortState[column] === 'newFirst') {
        return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
      } else if (this.sortState[column] === 'oldFirst') {
        return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
      }
      return 0; // If no sorting, return unchanged order
    });
  
    // Update the dataList with the sorted data
    this.dataList = sortedData;
    this.isSorted = true; // Set isSorted to true when sorting is applied
  }
  
  

}
