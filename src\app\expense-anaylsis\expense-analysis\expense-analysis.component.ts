import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AppPermissions } from 'src/app/app.permissions';
import { Expense } from 'src/app/expense/models/expense';
import { AuthService } from 'src/app/auth/services/auth.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ExpenseApiService } from 'src/app/expense/services/expense-api.service';
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { ExpenseAnalysisItemComponent } from '../expense-analysis-item/expense-analysis-item.component';
import { NgClass, NgFor, NgIf, CurrencyPipe } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { HeaderComponent } from '../../mcv-header/components/header/header.component';

@Component({
    selector: 'app-expense-analysis',
    templateUrl: './expense-analysis.component.html',
    styleUrls: ['./expense-analysis.component.scss'],
    standalone: true,
    imports: [HeaderComponent, MatFormFieldModule, MatDatepickerModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatButtonModule, MatTooltipModule, NgClass, NgFor, ExpenseAnalysisItemComponent, NgIf, CurrencyPipe]
})
export class ExpenseAnalysisComponent implements OnInit {
  pagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 30,
    filters: [
      // { key: 'contactID', value: this.authService.currentUserStore.entity.id.toString() },
      // { key: 'statusFlag', value: 1 },
      { key: 'rangeStart', value: this.utility.getMonthStart().toISOString() },
      { key: 'rangeEnd', value: this.utility.getMonthEnd().toISOString() },
    ],
    searchKey: null,
    sort: 'expenseDate desc',
    route: '',
    showAll: false,
    showAssigned: false,
    groupBy: [],
    keyPropertyName: ''
  });

  totalExpenseAmount = 0;
  totalAmountDr = 0;
  totalAmountCr = 0;
  totalAmountBalance = 0;
  dataList: Expense[] = [];
  total!: Expense;
  showAll: boolean = false;
  isPermissionShowAll: boolean = false;
  isPermissionExcel: boolean = false;
  dateFilters!: FormGroup;
  searchFilter!: FormControl;
  currentPage = 3;
  pageSize = 3;
  // dataList: WFTaskAnalysis[] = [];
  isMobileView: boolean = false;
  isLoading: boolean = false;
  totalRecordsCount = 0;
  totalPages = 0;

  constructor(
    private authService: AuthService,
    private utility: UtilityService,
    private permissions: AppPermissions,
    private entityService: ExpenseApiService,
  ) { }

  get headerTitle(): string { return this.entityService.EXPENSE_ANALYSIS_HEADER_TITLE; }

  ngOnInit() {
    this.isMobileView = this.utility.isMobileView;
    this.isPermissionShowAll = this.authService.isInAnyRole([this.permissions.TASK_ANALYSIS_SPECIAL_SHOW_ALL]);
    this.isPermissionExcel = this.authService.isInAnyRole([this.permissions.TASK_ANALYSIS_SPECIAL_EXCEL]);
    this.buildForm();
    if (this.entityService.isPermissionPettyCashEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_PETTY_CASH.toString() });
    }
    if (this.entityService.isPermissionRemunerationEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_REMUNERATION.toString() });
    }
    if (this.entityService.isPermissionInvestmentEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_INVESTMENT.toString() });
    }
    if (this.entityService.isPermissionTaxationEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_TAXATION.toString() });
    }
    if (this.entityService.isPermissionTravelEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_TRAVEL.toString() });
    }
    if (this.entityService.isPermissionEdit) {
      this.pagedListConfig.filters.push({ key: 'typeFlag', value: this.entityService.EXPENSE_TYPEFLAG_GENERAL.toString() });
    }

    this.getDataList(this.currentPage, this.pageSize);
  }

  buildForm() {
    this.dateFilters = new FormGroup({
      start: new FormControl(this.utility.getMonthStart()),
      end: new FormControl(this.utility.getMonthEnd())
    });

    this.dateFilters.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.pagedListConfig.filters = this.pagedListConfig.filters.filter(x => x.key !== 'rangeStart' && x.key != 'rangeEnd');
          this.pagedListConfig.filters.push({ key: 'rangeStart', value: value.start.toISOString() });
          this.pagedListConfig.filters.push({ key: 'rangeEnd', value: value.end.toISOString() });

          this.search();
        }
      });

    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        if (value) {
          this.pagedListConfig.searchKey = value;
          this.search();
        }
      })
  }

  getDataList(currentPage: number, pageSize: number) {
    this.isLoading = true;
    // this.getDataTotal();
    this.entityService.getAnalysis('full', this.pagedListConfig.filters, this.pagedListConfig.searchKey,
      this.pagedListConfig.sort)
      .subscribe(
        (data: Expense[]) => {
          this.totalAmountCr = 0;
          this.totalAmountDr = 0;
          this.totalExpenseAmount = 0;
          // this.totalRecordsCount = data.total;
          // this.totalPages = data.pages;
          // this.listLoad.emit({ totalRecordsCount: this.totalRecordsCount });
          this.dataList = data; //this.utility.updatePagedList<WFTaskAnalysis>(data.list, this.dataList, 'wfTaskID');
          this.isLoading = false;
          data.forEach(x => {
            this.totalExpenseAmount += x.expenseAmount;
            this.totalAmountDr += x.amountDr;
            this.totalAmountCr += x.amountCr;
          })
          const _pettyCashData = data
            .filter(x => x.typeFlag == 1)
            .sort((b, a) => new Date(a.expenseDate).getTime() - new Date(b.expenseDate).getTime())
            .map(x => x.amountBalance);
          console.log('_pettyCashData', _pettyCashData);
          this.totalAmountBalance = _pettyCashData[0];
        },
        error => {
          this.isLoading = false;
        }
      );
  }

  loadMoreRecords() {
    if (this.currentPage * this.pagedListConfig.pageSize < this.totalRecordsCount) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
    }
  }

  search() {
    this.currentPage = 0;
    this.dataList = [];
    this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
  }

  refresh() {
    this.pagedListConfig.searchKey = null;
    this.searchFilter.setValue(null);
    this.dataList = [];
    this.getDataList(0, (this.currentPage + 1) * this.pagedListConfig.pageSize);
  }

  onToggleShowAll() {
    this.showAll = !this.showAll;
    this.pagedListConfig.showAll = this.showAll;
    if (this.showAll) {
      this.pagedListConfig.filters = this.pagedListConfig.filters.filter(x => x.key !== 'contactID')
    } else {
      this.pagedListConfig.filters.push({ key: 'contactID', value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '' });
    }
    this.search();
  }

  onExportExcel() {
    this.entityService.exportAnalysisExcel(
      'full',
      this.pagedListConfig.filters,
      this.pagedListConfig.searchKey,
      this.pagedListConfig.sort
    );
  }


}
