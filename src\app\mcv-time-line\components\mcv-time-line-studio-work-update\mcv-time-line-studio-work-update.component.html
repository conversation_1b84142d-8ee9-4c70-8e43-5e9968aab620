<!-- 
<div  class="mat-dialog-header mcv-design-script-dialog-header" [ngClass]="{ 'edit-mode-header': isEditable }">
    <div class="d-flex align-items-center justify-content-left ">
      <img class="timeline-avatar" src="{{  person.photoUrl || 'assets/images/user.png' }}" />
   
<div>
        <h5 class="font-focused ml-1"> {{person.name }} | Studio Work</h5>
        <h6 class="ml-1">{{data.subtitle}}</h6>
</div>
    </div>
  

    <div class="d-flex align-items-center justify-content-left ">
        
      <button mat-icon-button aria-label="close" *ngIf="!isEditable && (isCurrentUser ||  isSpecialPermissionEdit)" (click)="edit()">
          <mat-icon>edit</mat-icon>
      </button>
      <button mat-icon-button aria-label="Delete" *ngIf="isEditable && (isCurrentUser ||  isSpecialPermissionDelete)" (click)="onDelete()" matTooltip="Delete">
      <mat-icon>delete</mat-icon>
    </button>
      <button mat-icon-button aria-label="close" *ngIf="isEditable" (click)="onSubmit()">
        <mat-icon>check</mat-icon>
    </button>
      <button mat-icon-button aria-label="close" (click)="onClose(null)">
          <mat-icon>close</mat-icon>
      </button>
    </div>
    


   
</div> -->
<mat-dialog-content class="dialog-wrapper">
  <div class="assign-studio-header d-flex align-items-center justify-content-between px-2">
    <div class="d-flex align-items-center justify-content-start ">
      <img class="timeline-avatar" src="{{  person.photoUrl || 'assets/images/user.png' }}" />
      <div>
        <h5 class=" ml-2 studio-work-dialog-title">
          STUDIO WORK</h5>
        <small class="ml-2 studio-work-dialog-subtitle">{{person.name}}</small>
      </div>
    </div>
    <div class="d-flex align-items-center mr-">
      <h1 class="mhr-count mr-1">{{data.mHr }}</h1><small class=" mt-3">mHR</small>
    </div>


  </div>
  <div *ngIf="!isEditable && !isMobileView" class="values-wrapper mt-3 px-2">
    <div class="dialog-form-row">

      <div class="readonly-field">
        <small class="font-focused">Package:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.code }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Project:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.projectCode }}-{{ packageDetails.projectTitle
          }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Assigner:</small>
        <h6 class="font-focused">{{ data.createdBy }}</h6>
      </div>
    </div>
    <div class="dialog-form-row">
      <div class="readonly-field">
        <small class="font-focused">Executive Partner:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.partner[0].name }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Start Date:</small>
        <h6 class="font-focused">{{ data.startDate | date: 'dd MMM yyyy' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Start Time:</small>
        <h6 class="font-focused">{{ data.startDate | date: 'HH:mm' }}</h6>
      </div>

    </div>
    <div class="dialog-form-row">
      <div class="readonly-field">
        <small class="font-focused">Design Lead:</small>
        <h6 class="font-focused">{{ packageDetails.associate[0].name }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">End Date:</small>
        <h6 class="font-focused">{{ data.endDate | date: 'dd MMM yyyy' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">End Time:</small>
        <h6 class="font-focused">{{ data.endDate | date: 'HH:mm' }}</h6>
      </div>

    </div>
  </div>
  <div *ngIf="!isEditable && isMobileView" class="values-wrapper mt-3 px-2">
    <div class="dialog-form-row">

      <div class="readonly-field">
        <small class="font-focused">Package:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.code }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Project:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.projectCode }}-{{ packageDetails.projectTitle
          }}</h6>
      </div>
       
      <div class="readonly-field">
        <small class="font-focused">Start Date:</small>
        <h6 class="font-focused">{{ data.startDate | date: 'dd MMM yyyy' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Start Time:</small>
        <h6 class="font-focused">{{ data.startDate | date: 'HH:mm' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">End Date:</small>
        <h6 class="font-focused">{{ data.endDate | date: 'dd MMM yyyy' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">End Time:</small>
        <h6 class="font-focused">{{ data.endDate | date: 'HH:mm' }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Assigner:</small>
        <h6 class="font-focused">{{ data.createdBy }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Executive Partner:</small>
        <h6 class="font-focused" *ngIf="packageDetails">{{ packageDetails.partner[0].name }}</h6>
      </div>
      <div class="readonly-field">
        <small class="font-focused">Design Lead:</small>
        <h6 class="font-focused">{{ packageDetails.associate[0].name }}</h6>
      </div>
   
    </div>
    
   
    
  </div>
  <div class="input-wrapper row  m-2" *ngIf="isEditable">
    <form [formGroup]="form">

      <mat-form-field class="col-md-12">
        <mat-label>Package</mat-label>
        <input id="package" placeholder="package" aria-label="package" matInput formControlName="package"
          [matAutocomplete]="package" />
        <mat-autocomplete #package="matAutocomplete" (optionSelected)="onPackageSelected($event)"
          [displayWith]="displaySelectedPackageValue">
          <mat-option *ngFor="let item of selectPackages | async" [value]="item">
            {{ item.title }}
          </mat-option>
        </mat-autocomplete>
        <mat-error>{{ getErrorMessage(f.package) }}</mat-error>
      </mat-form-field>

      <div class="col-md-12">
        <div class="row">
          <mat-form-field class="col-sm-12 col-md-6">
            <mat-label>Start Date</mat-label>
            <input matInput formControlName="startDate" [min]="minDate" [matDatepicker]="startDatePicker"
              placeholder="Select Date" readonly />
            <mat-error>{{ getErrorMessage(f.startDate) }}</mat-error>
            <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
            <mat-datepicker [touchUi]="isMobileView" #startDatePicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field class="col-sm-12 col-md-6">
            <mat-label>Start Time</mat-label>
            <input aria-label="Start Time" [ngxTimepicker]="pickerStartTime" formControlName="startTime" matInput
              [format]="24" [min]="minTime" [max]="maxTime" readonly />
            <ngx-material-timepicker #pickerStartTime [minutesGap]="minutesGap"></ngx-material-timepicker>
            <mat-error>{{ getErrorMessage(f.startTime)}}</mat-error>
            <mat-hint align="end" *ngIf="isMobileView">24 hour format. Min:{{minTime}} Max:{{maxTime}}</mat-hint>
          </mat-form-field>

          <mat-form-field class="col-sm-12 col-md-6">
            <mat-label>End Date</mat-label>
            <input matInput formControlName="dueDate" [matDatepicker]="endDatePicker" placeholder="Select Date"
              readonly />
            <mat-error>{{ getErrorMessage(f.dueDate) }}</mat-error>
            <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
            <mat-datepicker [touchUi]="isMobileView" #endDatePicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field class="col-sm-12 col-md-6">
            <mat-label>End Time</mat-label>
            <input aria-label="End Time" [ngxTimepicker]="pickerDueTime" formControlName="endTime" matInput
              [format]="24" [max]="maxTime" readonly />
            <ngx-material-timepicker #pickerDueTime [minutesGap]="minutesGap"></ngx-material-timepicker>
            <mat-error>{{ getErrorMessage(f.endTime)}}</mat-error>
            <mat-hint align="end" *ngIf="isMobileView">24 hour format. Min:{{minTime}} Max:{{maxTime}}</mat-hint>
          </mat-form-field>

          <mat-form-field class="col-sm-12">
            <mat-label>Work Description</mat-label>
            <textarea id="comment" placeholder="Enter some description about the work to be done" aria-label="comment"
              matInput formControlName="comment" cdkTextareaAutosize #autosize="cdkTextareaAutosize"
              cdkAutosizeMinRows="2" cdkAutosizeMaxRows="5"></textarea>
            <mat-error>{{ getErrorMessage(f.comment) }}</mat-error>
          </mat-form-field>
        </div>



        <!-- <div class="deliverables-wrapper mt-2">
          <h6 class="font-focused pb-2">Deliverables Details</h6>
          <ul>
              <li *ngFor="let item of deliverables">
               
                  <div class="d-flex align-items-center justify-content-between">
                      <mat-checkbox *ngIf="this.isEditable" class="mr-1" [checked]="onCheckedDeliverables(item)"
                          (change)="onChangeSelection(item)"  >
                      </mat-checkbox>
                      <div class="pl-1">
                          <div class="d-flex align-items-center">
                              <h6 class="title">{{item.title}}</h6>
                          </div>
                          <div>
                              <div><small>StageService :</small> {{item.stageService}}</div>
                          </div>
                      </div>
                  </div>
                  <div class="d-flex align-items-center">
                      <div [mcvPopover]="myPopover"
                          class="d-flex align-items-center justify-content-center mr-1 ml-1">
                          <mat-icon>info</mat-icon>
                          <mcv-popover-content #myPopover placement="left" [animation]="true"
                              [closeOnClickOutside]="true">
                              <strong>Description</strong>
                              <p>{{item.description}}</p>
                          </mcv-popover-content>
                      </div>
                  </div>
              </li>
          </ul>
      </div> -->
      </div>



    </form>
  </div>

  <div class="dialog-button-footer px-2">
    <button mat-raised-button class="delete-button" *ngIf="isEditable && (isCurrentUser ||  isSpecialPermissionDelete)"
      (click)="onDelete()">
      Delete
    </button>
    <button mat-raised-button class="edit-button" *ngIf="!isEditable && (isCurrentUser ||  isSpecialPermissionEdit)"
      (click)="edit()">
      Edit
    </button>
    <button mat-raised-button class="save-button" *ngIf="isEditable" (click)="onSubmit()">
      Save
    </button>
    <button mat-raised-button class="close-button" (click)="onClose(null)">
      Close
    </button>
  </div>

</mat-dialog-content>