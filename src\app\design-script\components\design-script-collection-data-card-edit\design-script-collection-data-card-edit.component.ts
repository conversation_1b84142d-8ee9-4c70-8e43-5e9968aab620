import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { CommonModule, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { DesignScriptDataCard, DesignScriptDataCardAttachment, DesignScriptDataCardAttribute } from '../../models/design-script-data-card.model';
import { DesignScriptEntity } from '../../models/design-script-entity.model';
import { DesignScriptDataCardEditComponent } from '../design-script-data-card-edit/design-script-data-card-edit.component';
import { MAT_DIALOG_DATA, MatDialogConfig, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { DesignScriptDataCardApiService } from '../../services/design-script-data-card-api.service';
import { DesignScriptApiService } from '../../services/design-script-api.service';
import { DesignScriptDataCardAttributeApiService } from '../../services/design-script-data-card-attribute-api.service';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { firstValueFrom, forkJoin } from 'rxjs';
import { DesignScriptDataCardReplaceDialogComponent } from '../design-script-data-card-replace-dialog/design-script-data-card-replace-dialog.component';
import { DesignScriptDataCardAttachmentApiService } from '../../services/design-script-data-card-attachment-api.service';
import { McvTagUtilityService } from 'src/app/services/mcv-tag-utility.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { LibraryEntityApiService } from 'src/app/library/service/library-entity-api.service';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { McvBaseSearchTagEditorComponent } from 'src/app/mcv-core/components/mcv-base-search-tag-editor/mcv-base-search-tag-editor.component';

@Component({
  selector: 'app-design-script-collection-data-card-edit',
  standalone: true,
  imports: [NgIf, MatButtonModule, MatTooltipModule, MatIconModule, MatDialogModule, NgFor, NgClass, ReactiveFormsModule, MatFormFieldModule, MatInputModule, McvBaseSearchTagEditorComponent, TextFieldModule],
  templateUrl: './design-script-collection-data-card-edit.component.html',
  styleUrls: ['./design-script-collection-data-card-edit.component.scss']
})
export class DesignScriptCollectionDataCardEditComponent implements OnInit {

  dataCard: DesignScriptDataCard;
  isFullScreen: boolean = false;
  dialogTitle: string;
  designScriptEntity: DesignScriptEntity;
  isToggleHidden: boolean = false;

  constructor(@Inject(MAT_DIALOG_DATA) dialogData: any,
    private dialogRef: MatDialogRef<DesignScriptDataCardEditComponent>,
    private entityService: DesignScriptApiService,
    private designScriptDataCardService: DesignScriptDataCardApiService,
    private formBuilder: FormBuilder,
    private utilityService: UtilityService,
    private tagUtility: McvTagUtilityService,
    private libraryEntityService: LibraryEntityApiService,
    private designScriptDataCardAttributeService: DesignScriptDataCardAttributeApiService,
    private designScriptDataCardAttachmentService: DesignScriptDataCardAttachmentApiService
  ) {
    this.dialogTitle = dialogData.config.dialogTitle;
    this.dataCard = dialogData.config.designScriptDataCard;
    this.designScriptEntity = dialogData.config.scriptEntity;
    if (this.dataCard) {
      this.refresh();
    }
  }

  form!: FormGroup;
  searchTagOptions: string[] = [];

  //Attribute FormArray
  get f(): any { return this.form.controls; }
  get categoryOptions() { return this.entityService.categoryOptions; }
  get TYPEFLAG_ZONE() { return this.entityService.TYPEFLAG_ZONE; }
  get attributes(): FormArray { return this.f['attributes'] as FormArray; }
  get tagOptions(): string[] { return this.entityService.tagOptions; }
  get isMobileView(): boolean { return this.utilityService.isMobileView; }
  get designScriptEntityList() { return this.entityService.entityList; }
  get IsPermissionEdit(): boolean { return this.entityService.isPermissionEdit; }

  @Output() cancel = new EventEmitter<any>();
  @Output() update = new EventEmitter<DesignScriptDataCard>();
  @Output() delete = new EventEmitter<DesignScriptDataCard>();
  @Output() restore = new EventEmitter<DesignScriptDataCard>();
  @Output() unlink = new EventEmitter<DesignScriptDataCard>();

  ngOnInit(): void {
    if (!this.form) {
      this.buildForm();
    }
    this.searchTagOptions = this.entityService.tagOptions;
  }

  refresh() {
    if (!this.form) {
      this.buildForm();
    }
    this.bindForm();
  }

  private buildForm() {
    this.form = this.formBuilder.group({
      description: new FormControl<string>(''),
      gfcTag: new FormControl<string>(''),
      attributes: this.formBuilder.array([]),
    });

    // this.changeToUpperCase();

    this.touchForm();
    if (this.dataCard.isVersion) {
      this.f['attributes'].disable();
      console.log('Form Disabled')
    } else {
      this.f['attributes'].reset();
    }
  }

  private bindForm() {
    this.f['description'].setValue(this.dataCard.description);
    this.f['gfcTag'].setValue(this.dataCard.gfcTag);

    //For Attribute Value
    this.attributes.clear();
    if (this.dataCard.attributes) {
      this.dataCard.attributes.map(x => {
        return { attributeKey: x.attributeKey, attributeValue: x.attributeValue }
      }).forEach((x, i) => {
        this.addAttribute();
        this.attributes.controls[i].setValue(x);
      });
    } else {
      this.addAttribute();
    }
  }

  private touchForm() {
    if (this.form) {
      Object.keys(this.form.controls).forEach(field => {
        const control = this.form.get(field);
        if (control)
          control.markAsTouched({ onlySelf: true });
      });
    }
  }


  getErrorMessage(control: AbstractControl) {
    return this.utilityService.getErrorMessage(control);
  }

  onCancel() {
    this.cancel.emit();
  }

  onTagsUpdate(tags: string[]) {
    if (tags) {
      this.dataCard.searchTags = tags;
      this.entityService.addToTagOptions(tags);
      this.dataCard.searchTags = tags;
      this.designScriptDataCardService.update(this.dataCard).subscribe((data) => {
        // this.update.emit(new DesignScriptDataCard(data));
      });
    }
  }

  onToggleTag(tagList: string[], tag: string, multiple: boolean = false) {
    this.tagUtility.onToggleTag(tagList, tag, multiple);
  }

  isTagSelected(tagList: string[], tag: string): boolean {
    return this.tagUtility.isTagSelected(tagList, tag);
  }

  getSelectedCategoryClass(category: any): string {
    if (this.dataCard.category.includes(category.label)) {
      return category.class;
    }
    return '';
  }

  onClickDelete() {
    const _messageText =
      'Delete Attachment !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.dataCard.isDeleted = true;
        this.designScriptDataCardService.update(this.dataCard).subscribe(data => {
          this.delete.emit(data);
        });
      });
  }

  onClickRestore() {
    const _messageText =
      'Restore !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.dataCard.isDeleted = false;
        this.designScriptDataCardService.update(this.dataCard).subscribe(data => {
          this.restore.emit(this.dataCard);
        });
      });
  }

  addAttribute() {
    const attributeForm = this.formBuilder.group(
      {
        attributeKey: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(50)]],
        attributeValue: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(256)]],
      }
    );
    this.attributes.push(attributeForm);
  }

  removeAttribute(index: number) {
    this.attributes.removeAt(index);
  }

  onAttributeInputChange(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control && _control.value) {
      _control.setValue(_control.value.toUpperCase());
    }
  }
  getAttributeFormControl(formArrayIndex: number, controlName: string) {
    if (this.attributes) {
      const _formGroup = <FormGroup>this.attributes.controls[formArrayIndex];
      return <FormControl>_formGroup.controls[controlName];
    } return new FormControl();
  }

  isInputInvalid(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control) {
      return _control.invalid;
    } else {
      return false;
    }
  }

  onUnlinkDataCard() {
    this.utilityService.showConfirmationDialog(`Remove ${this.dataCard.title}?`, async () => {
      this.designScriptDataCardService.unLinkFromEntity(this.designScriptEntity.id, this.dataCard).toPromise();
      this.onUnlinkChildrens(this.designScriptEntity.id, this.dataCard);
      this.entityService.unlinkDataCard.push({ entity: this.designScriptEntity, dataCard: this.dataCard });
    });
  }

  async onUnlinkChildrens(entityID: number, dataCard: DesignScriptDataCard) {
    const childrens = this.entityService.entityList.filter(x => x.parentID == entityID);
    if (childrens.length) {
      var requests: any[] = [];
      childrens.forEach(x => {
        if (x.dataCards.length && x.dataCards.find(z => z.id == dataCard.id)) {
          requests.push(this.designScriptDataCardService.unLinkFromEntity(x.id, dataCard));
        }
        requests.push(this.onUnlinkChildrens(x.id, dataCard));
      });
      await forkJoin(requests).toPromise();
    }
    this.dialogRef.close(dataCard);
  }

  onToggleFullscreen() {
    this.isFullScreen = !this.isFullScreen;
  }

  getFileExtension(filename: string) { return this.utilityService.getFileExtension(filename); }
  getFileMediaType(filename: string) { return this.utilityService.getFileMediaType(filename); }

  getMasterCategory(category: string) {
    return this.categoryOptions.find(x => x.label == category);
  }

  // changeToUpperCase() {
  //   this.f['gfcTag'].valueChanges.pipe(
  //     debounceTime(400),
  //     distinctUntilChanged(),
  //   ).subscribe(val => {
  //     if (val) {
  //       this.f['gfcTag'].setValue(val.toUpperCase(), { emitEvent: false });
  //     }
  //   });
  // }

  onPreview(image: DesignScriptDataCardAttachment) {
    const _data = {
      title: image.filename,
      urls: null,
      filename: image.filename,
      activeUrl: image.thumbUrl || image.url,
      mediaType: 'image',
      contentType: image.contentType,
      // mediaCaption: this.mediaCaption
    };
    this.libraryEntityService.openLibraryLightBox(this.dataCard.attachments, _data);
  }

  async toggleHide(item: DesignScriptDataCardAttribute) {
    item.isHidden = !item.isHidden;
    item = await (this.designScriptDataCardAttributeService.update(item, true).toPromise());
  }

  async toggleHideAttachment(item: DesignScriptDataCardAttachment) {
    item.isHidden = !item.isHidden;
    item = await (this.designScriptDataCardAttachmentService.update(item, true).toPromise());
  }

  onClose() {
    this.dialogRef.close(this.dataCard);
  }

  async onToggleDataCard() {
    this.isToggleHidden = !this.isToggleHidden;
    this.dataCard.attributes.forEach(x => x.isHidden = this.isToggleHidden);
    let _request = this.dataCard.attributes.map(x =>
      this.designScriptDataCardAttributeService.update(x)
    );
    await forkJoin(_request).toPromise();
  }

  async onReplaceDataCard() {
    const _dialogConfig = new MatDialogConfig();
    _dialogConfig.disableClose = true;
    _dialogConfig.autoFocus = true;
    _dialogConfig.data = {
      dataCard: this.dataCard
    }

    const _dialogRef = this.entityService.openDialog(DesignScriptDataCardReplaceDialogComponent, _dialogConfig, true);
    _dialogRef.afterClosed().subscribe(res => {
      if (res) {
        console.log(res);
        this.dataCard = Object.assign(this.dataCard, res);
        this.dialogRef.close(this.dataCard);
      }
    });
  }

  async onCellChange(formControlName: string) {
    const _control = this.form.get(formControlName);
    if (_control) {
      if (formControlName == 'gfcTag') {
        this.f['gfcTag'].setValue(_control.value);
        this.dataCard.gfcTag = _control.value;
      } else if (formControlName == 'description') {
        this.f['description'].setValue(_control.value);
        this.dataCard.description = _control.value;
      }

      const _updated = await firstValueFrom(this.designScriptDataCardService.update(this.dataCard));
      if (_updated) {
        this.utilityService.showSwalToast('DataCard Updated', 'Updated Successfully!', 'success');
        this.dataCard = Object.assign(this.dataCard, _updated);
      }
    }
  }

  async onDeleteDatCard() {
    this.utilityService.showConfirmationDialog(`Do you want to delete ${this.dataCard.title} datacard?, will be deleted from everywhere linked.`, async () => {
      await firstValueFrom(this.designScriptDataCardService.delete(this.dataCard.id));
      this.dialogRef.close({ dataCard: this.dataCard, isDelete: true });
    });
  }
}
