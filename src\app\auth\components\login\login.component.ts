import { AbstractControl, FormBuilder, FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AppConfig } from 'src/app/app.config';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { firstValueFrom } from 'rxjs';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { OtpInputConfig } from '../../mcv-otp-input/models/otp-input-config.model';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgIf } from '@angular/common';
// import { ContactTeamApiService } from 'src/app/contact/services/contact-team-api.service';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [NgIf, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatButtonModule]
})
export class LoginComponent
{
  form!: FormGroup;
  returnUrl: string = '';
  version: string = environment.appVersion;
  logoUrl: string = environment.logoUrl;
  isLoggedIn: boolean = false;
  otpConfig: OtpInputConfig = {
    length: 6, allowNumbersOnly: true,
    inputClass: '',
    isPasswordInput: false,
    disableAutoFocus: false,
    letterCase: 'Upper'
  }
  otpFC = new FormControl<any>(null, [Validators.required, Validators.minLength(this.otpConfig.length)]);
  get isLocked() { return false; } //return environment.isLocked; }
  // convenience getter for easy access to form fields
  get f(): any { return this.form.controls; }

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private config: AppConfig,
    private utilityService: UtilityService,
    private contactService: ContactApiService,
    // private contactTeamService:ContactTeamApiService
  ) { }

  ngOnInit()
  {
    this.buildForm();
  }

  private buildForm()
  {
    this.form = this.formBuilder.group({
      username: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      password: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] })
    });
    // this.touchForm();
  }

  private touchForm()
  {
    Object.keys(this.form.controls).forEach(field =>
    {
      // {1}
      const control = this.form.get(field); // {2}
      if (control != null)
      {
        control.markAsTouched({ onlySelf: true }); // {3}
      }
    });
  }

  getErrorMessage(control: AbstractControl)
  {
    return this.utilityService.getErrorMessage(control);
  }

  async onSubmit()
  {
    if (this.form.invalid)
    {
      this.touchForm();
      this.utilityService.showSweetDialog(
        'Incomplete data!',
        'Please fill all required fields with valid data and try again!',
        'warning');
      return;
    }
    var result = await firstValueFrom(this.authService.login(this.f['username'].value, this.f['password'].value));

    if (result)
    {
      this.isLoggedIn = true;
      if (result.isOTPRequired)
      {
        this.utilityService.showSwalToast("OTP sent to registered email!", "", "success");
      } else
      {

        this.redirectToCockpit();
      }

    }


  }

  async onOtpChange(otp: string)
  {
    const result = await firstValueFrom(this.authService.verifyEmailOTP(otp));
    if (result.status == 'success')
    {
      this.utilityService.showSwalToast("OTP verification successful!", "", "success");
      await this.redirectToCockpit();
    } else
    {
      this.utilityService.showSweetDialog("OTP verification failed!", "Please enter a valid otp received on email.", "error");
    }
  }

  private async redirectToCockpit()
  {
    if (this.authService.currentUserStore)
    {
      this.authService.currentUserStore.roles = await firstValueFrom(this.authService.getRolesByUsername(this.f['username'].value));
      this.authService.currentUserStore.contact = (await firstValueFrom(this.contactService.get([{ key: 'Username', value: this.f['username'].value }])))[0];
      // this.authService.currentUserStore.teams=await firstValueFrom(this.contactTeamService.get([{key:'contactID',value:this.authService.currentUserStore.contact.id.toString()}]));

      this.authService.setUserStore(this.authService.currentUserStore);

      if (this.authService.currentUserStore?.isChangePassword)
      {
        this.router.navigate([this.config.ROUTE_CHANGE_PASSWORD]);
      } else
      {
        const redirect = this.authService.redirectUrl
          && this.authService.redirectUrl != this.config.ROUTE_LOGIN ? this.authService.redirectUrl : this.config.ROUTE_COCKPIT;
        this.router.navigate([redirect]);
      }
    } else
    {
      this.authService.logout();
    }
  }

  async verifyOTP()
  {
    if (this.otpFC.invalid)
    {
      // this.touchForm();
      this.utilityService.showSweetDialog(
        'Incomplete data!',
        'Please enter valid OTP and try again!',
        'warning');
      return;
    }
    // this.authService.verifyOTP(`91${this.mobileFC.value}`, this.otpFC.value)
    const otp = this.otpFC.value;
    if (otp)
    {
      console.log("calling OTP verification API");
      const result = await firstValueFrom(this.authService.verifyEmailOTP(otp));
      console.log(result);
      if (result.status === 'success')
      {
        this.utilityService.showSwalToast("OTP verification successful!", "", "success");
        if (this.authService.currentUserStore && this.authService.currentUserStore.isChangePassword)
        {
          this.router.navigate([this.config.ROUTE_CHANGE_PASSWORD]);
        } else
        {
          const redirect = this.authService.redirectUrl
            && this.authService.redirectUrl != this.config.ROUTE_LOGIN ? this.authService.redirectUrl : this.config.ROUTE_COCKPIT;
          this.router.navigate([redirect]);
        }
      } else
      {
        this.utilityService.showSweetDialog("OTP verification failed!", "Please enter a valid otp received on email.", "error");
      }

    } else
    {
      this.utilityService.showSweetDialog("OTP verification failed!", "Please enter a valid otp received on email.", "error");
    }
  }

}
