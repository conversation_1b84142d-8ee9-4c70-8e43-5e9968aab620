import { FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { MatAutocompleteSelectedEvent, MatAutocompleteModule } from '@angular/material/autocomplete';

import { RequestTicket, RequestTicketAttachment } from '../../models/mcv-request-ticket';
import { McvBaseEntityComponent } from 'src/app/mcv-core/components/mcv-base-entity/mcv-base-entity.component';
import { Contact } from 'src/app/contact/models/contact';

import { Observable, forkJoin } from 'rxjs';
import { AuthService } from 'src/app/auth/services/auth.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { RequestTicketApiService } from '../../services/request-ticket-api.service';
import { RequestTicketAssigneeApiService } from '../../services/request-ticket-assignee-api.service';
import { RequestTicketAttachmentApiService } from 'src/app/request-ticket/services/request-ticket-attachment-api.service';

import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { WFTask } from 'src/app/models/wf-task.model';
import { EmailContact } from 'src/app/models/email-contact';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { RequestTicketAssignee } from '../../models/mcv-request-ticket-assignee';
import { Project } from 'src/app/project/models/project.model';
import { BlobUploadInfo } from 'src/app/mcv-file/models/mcv-blob-upload-info.model';
import { DateFilterFn, MatDatepickerModule } from '@angular/material/datepicker';
import { McvFileListComponent } from '../../../mcv-file/components/mcv-file-list/mcv-file-list.component';
import { McvBaseSearchTagEditorComponent } from '../../../mcv-core/components/mcv-base-search-tag-editor/mcv-base-search-tag-editor.component';
import { McvActivityListComponent } from '../../../mcv-activity/components/mcv-activity-list/mcv-activity-list.component';
import { WftaskActionComponent } from '../../../wf-task/components/wftask-action/wftask-action.component';
import { McvTimeEntryListComponent } from '../../../mcv-time-entry/components/mcv-time-entry-list/mcv-time-entry-list.component';
import { McvFileComponent } from '../../../mcv-file/components/mcv-file/mcv-file.component';
import { McvFileUploadComponent } from '../../../mcv-file/components/mcv-file-upload/mcv-file-upload.component';
import { RequestTicketEmailComponent } from '../request-ticket-email/request-ticket-email.component';
import { MatIconModule } from '@angular/material/icon';
import { CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf } from '@angular/cdk/scrolling';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { WftaskTitleBarComponent } from '../../../wf-task/components/wftask-title-bar/wftask-title-bar.component';
import { NgIf, NgClass, NgFor, NgStyle, AsyncPipe, DatePipe } from '@angular/common';

@Component({
    selector: 'app-request-ticket',
    templateUrl: './request-ticket.component.html',
    styleUrls: ['./request-ticket.component.scss'],
    standalone: true,
    imports: [NgIf, WftaskTitleBarComponent, MatButtonModule, MatTooltipModule, NgClass, MatExpansionModule, ReactiveFormsModule, MatFormFieldModule, MatSelectModule, NgFor, MatOptionModule, MatInputModule, MatAutocompleteModule, MatDatepickerModule, TextFieldModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, NgStyle, CdkVirtualForOf, MatIconModule, RequestTicketEmailComponent, McvFileUploadComponent, McvFileComponent, McvTimeEntryListComponent, WftaskActionComponent, McvActivityListComponent, McvBaseSearchTagEditorComponent, McvFileListComponent, AsyncPipe, DatePipe]
})
export class RequestTicketComponent extends McvBaseEntityComponent implements OnInit {

  //Common component properties-------------------------
  override currentEntity: RequestTicket = new RequestTicket();
  override nameOfEntity = this.config.NAMEOF_ENTITY_REQUEST_TICKET;
  selectedProject!: Project | null;
  //-----------end

  unSupportedMediaTypes = ['video', 'audio'];
  readonly STATUSFLAG_ACTIVE = 0;
  readonly STATUSFLAG_COMPLETED = 1;
  readonly STATUSFLAG_DROPPED = 2;
  readonly DEFAULT_REPEAT_INTERVAL = 7;

  emailContactOptions: EmailContact[] = [];
  contactFilter = [];
  filteredContacts$!: Observable<EmailContact[]>;
  // selectedAssignee: ContactDto;
  completeValidationMessage = 'Please complete checklist to activate Complete button!';
  purposeOptions = ['Information', 'Meeting', 'Approval', 'Sign-Off', 'Payment'];
  now = new Date();
  timePickerMinutesGap = 5;
  titleTypeOptions: string[] = ['Project', 'Custom'
  ];

  historyList?: any[] = [];
  isPermissionEdit: boolean = false;
  isPermissionDelete: boolean = false;
  isPermissionSpecialEdit: boolean = false;
  isPermissionSpecialDelete: boolean = false;

  allowEdit: boolean = false;
  allowDelete: boolean = false;

  tagOptions: string[] = [];

  projectOptions: any[] = [];
  projectFilter = [{ key: 'statusFlag', value: '1' }, { key: 'statusFlag', value: '2' }];
  filteredProjects$!: Observable<Project[]>;

  subjectOptions: any[] = [];
  subjectFilter = [];
  filteredSubjects$!: Observable<string[]>;

  constructor(
    private entityService: RequestTicketApiService,
    private contactService: ContactApiService,
    override authService: AuthService,
    private assigneeService: RequestTicketAssigneeApiService,
    private projectService: ProjectApiService,
    private attachmentService: RequestTicketAttachmentApiService,
  ) {
    super();
  }

  override async ngOnInit() {
    await super.ngOnInit();
    if (!this.form) {
      this.buildForm();
    }
    this.getEmailContactOptions();
  }

  override refresh() {

    this.currentEntity = new RequestTicket();
    if (!this.form) {
      this.buildForm();
    }
    if (this.entityID && this.entityID !== -1) {
      this.getCurrent(this.entityID);
      this.getCurrentHistory(this.nameOfEntity, this.entityID);
    } else {
      this.checkPermissions();
    }
  }

  dateFilter: DateFilterFn<Date | null> = (d: Date | null): boolean => {
    if (!d) {
      return true;
    }
    // Prevent Saturday and Sunday from being selected.
    // if (d.getDay() === 0) {
    //   return false;
    // }

    const date = new Date();
    // date.setDate(date.getDate() - 1);

    if (d < date) {
      return false;
    }

    return true;
  }

  private checkPermissions() {
    this.isPermissionEdit = this.entityService.isPermissionEdit;
    this.isPermissionDelete = this.entityService.isPermissionDelete;
    this.isPermissionSpecialEdit = this.entityService.isPermissionSpecialEdit;
    this.isPermissionSpecialDelete = this.entityService.isPermissionSpecialDelete;

    this.allowEdit = false;
    this.allowDelete = false;

    if (this.isCreateMode) {
      this.allowEdit = true;
    }
    else if (this.currentEntity
      && this.currentEntity.statusFlag === this.STATUSFLAG_ACTIVE) {

      this.allowEdit = !!((this.isPermissionSpecialEdit)
        || (this.isTaskMode
          && this.isPermissionEdit
          && this.currentEntity.assignerContactID
          && this.authService.currentUserStore?.contact
          && this.currentEntity.assignerContactID === this.authService.currentUserStore.contact.id));

      this.allowDelete = !!((this.isPermissionSpecialDelete)
        || (this.isTaskMode
          && this.isPermissionDelete
          && this.currentEntity.assignerContactID && this.authService.currentUserStore?.contact
          && this.currentEntity.assignerContactID === this.authService.currentUserStore.contact.id));

      // if (this.authService.isInRole(this.permissions.MASTER)) {
      //   this.titleTypeOptions.push('CUSTOM');
      // }
    }

    if (!this.isCreateMode) {
      if (!this.allowEdit) {
        this.form.disable();
      } else {
        this.f['titleType'].disable();
        // this.f['assignee'].disable();
      }
    }
  }


  override buildForm() {
    this.form = this.formBuilder.group({
      assignee: new FormControl<any>(null),
      purpose: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required, Validators.minLength(3)] }),
      titleType: new FormControl<any>(null),
      title: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required, Validators.minLength(3)] }),
      subtitle: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required, Validators.minLength(3)] }),
      message: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required, Validators.minLength(3)] }),
      nextReminderDate: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      reminderInterval: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      resolutionMessage: new FormControl<any>(null),
    });


    const _reminderInterval = this.DEFAULT_REPEAT_INTERVAL;
    const _nextDate = new Date();
    _nextDate.setDate(_nextDate.getDate() + _reminderInterval);
    this.f['nextReminderDate'].setValue(_nextDate, { emitEvent: false });
    this.f['reminderInterval'].setValue(_reminderInterval, { emitEvent: false });
    this.f['message'].setValue('Dear Sir/Madam,', { emitEvent: false });
    this.f['purpose'].setValue('Information', { emitEvent: false });

    this.f['titleType'].setValue('Project');
    this.getProjectOptions();

    this.filteredContacts$ = this.f['assignee'].valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),

      map(value => value ? (typeof value === 'string' ? value : (value as Contact).name) : null),
      map(name => name ? this.filterContacts(name as string) : this.emailContactOptions.slice(0,10)),

    );

    this.filteredProjects$ = this.f['title'].valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),

      map(value => typeof value === 'string' ? value : (value != null ? (value as Project).code + ' ' + (value as Project).title : null)),
      map(value => value ? this.filterProjectOptions(value as string) : this.projectOptions.slice()),
    );

    this.filteredSubjects$ = this.f['title'].valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),
      map(value => value ? this.filterTitleOptions(value as string) : this.subjectOptions.slice()),
    );

    this.f['titleType'].valueChanges
      .pipe(debounceTime(400))
      .subscribe(
        (value: any) => {
          if (value && !this.isDuplicated) {
            // this.f['title'].reset();
            if (this.f['titleType'].value == 'Project') {
              this.getProjectOptions();
            } else {
              this.getTitleOptions();
              this.selectedProject = null;
              this.f['title'].setValue('');
            }
          }
        }
      );


    this.f['title'].valueChanges
      .pipe(debounceTime(400))
      .subscribe(
        (value: any) => {
          if (value && this.isCreateMode) {

            if (typeof value === 'string') {
              this.currentEntity.title = value;
              this.currentEntity.entity = null;
              this.currentEntity.entityID = null;
            } else {
              this.currentEntity.title = value.code + '-' + value.title;
              this.currentEntity.entity = this.config.NAMEOF_ENTITY_PROJECT;
              this.currentEntity.entityID = value.id;
              this.currentEntity.projectID = value.id;
            }
          }
        }
      );

    this.touchForm();

  }

  private bindForm(entity: RequestTicket) {

    if (entity) {
      this.f['titleType'].setValue(entity.projectID ? 'Project' : 'Custom');
      this.f['purpose'].setValue(entity.purpose);
      this.f['title'].setValue(entity.title);
      if (entity.projectID) {
        this.f['title'].disable();
      } else {
        this.f['title'].enable();
      }
      this.f['subtitle'].setValue(entity.subtitle);
      this.f['nextReminderDate'].setValue(entity.nextReminderDate);
      this.f['message'].setValue(entity.requestMessage);
      this.f['reminderInterval'].setValue(entity.reminderInterval);
      this.f['resolutionMessage'].setValue(entity.resolutionMessage);
    }

  }

  private getEmailContactOptions() {
    this.contactService.getEmailOptions(this.contactFilter, '', 'fullName').subscribe(data => this.emailContactOptions = data);
  }

  private filterContacts(property: string): any[] {
    return this.emailContactOptions.filter(option => {
      return option ?
        (option.name.toLowerCase().includes(property.toLowerCase())
          || option.email.toLowerCase().includes(property.toLowerCase())
        )
        : false;
    });
  }

  displayFnContact(option?: EmailContact): string {
    return option ? (`${option.name} | ${option.email} | ${option.company}`) : '';
  }

  private getTitleOptions() {
    this.entityService.getSubjectOptions().subscribe(
      data => {
        this.subjectOptions = data;
      });
  }

  private filterTitleOptions(property: string): any[] {
    return this.subjectOptions.filter(option => option ? option.toLowerCase().includes(property.toLowerCase()) : false);
  }

  displayFnTitle(option?: any): string  {
    return option ? option : '';
  }

  private getProjectOptions() {
    // this.f['title'].reset();
    this.projectOptions = [];
    this.projectService.get([{ key: 'statusFlag', value: '1' }, { key: 'statusFlag', value: '2' }]).subscribe((data: any) => this.projectOptions = data);
  }

  private filterProjectOptions(property: string): any[] {

    return this.projectOptions.filter(option => option ? (option.code + '-' + option.title).toLowerCase().includes(property.toLowerCase()) : false);
  }

  displayFnProject(option?: any): string {
    return option ? (option.code + '-' + option.title) : '';
  }


  onTitleTypeChanged() {
    this.f['title'].reset();
    if (this.f['titleType'].value) {
      if (this.f['titleType'].value == 'Project') {
        this.getProjectOptions();
      } else {
        this.getTitleOptions();
      }
    }
  }

  protected getCurrent(id: number) {
    this.entityService.getById(id).subscribe(
      data => {
        this.currentEntity = data;
        this.afterEntityLoaded();
      }

    );
  }

  protected afterEntityLoaded() {
    this.resetForm();
    this.checkPermissions();
    this.bindForm(this.currentEntity);
  }


  override onSubmit(task?: WFTask) {
    // stop here if form is invalid
    if (this.form.invalid) {
      this.touchForm();
      console.log('invalid form', this.form);
      this.utilityService.showSweetDialog('Invalid Form',
        'Please fill all required fields with valid data and try again.', 'error'
      );
      return;
    }

    this.currentEntity.purpose = this.f['purpose'].value;
    if (this.selectedProject) {
      this.currentEntity.title = `${this.selectedProject.code}-${this.selectedProject.title}`;
    } else {
      this.currentEntity.title = this.f['title'].value;
    }
    this.currentEntity.subtitle = this.f['subtitle'].value;
    // this.currentEntity.assigneeContactID = this.f['assignee'].value ? (this.f['assignee'].value as ContactDto).id : null;
    this.currentEntity.nextReminderDate = this.utilityService.getLocalDate(this.f['nextReminderDate'].value);
    this.currentEntity.requestMessage = this.f['message'].value;
    this.currentEntity.resolutionMessage = this.f['resolutionMessage'].value;
    this.currentEntity.reminderInterval = this.f['reminderInterval'].value;
    if (!this.currentEntity.title || this.currentEntity.title.length === 0) {
      this.utilityService.showSweetDialog('title is blank!',
        'Please enter a valid title and try again!', 'error'
      );
      return;
    }


    if (this.getFilteredAssignees(this.currentEntity.assignees, 0).length == 0) {
      this.utilityService.showSweetDialog('Assignee not selected!',
        'Please add atleast 1 assignee contact and try again!', 'error'
      );
      return;
    }

    // const _messageText =
    //   (this.isCreateMode ? `Create New ${this.nameOfEntity}: ` : `Update ${this.nameOfEntity}: `) +
    //   this.currentEntity.title;
    // this.utilityService.showConfirmationDialog(_messageText,
    //   () => {
    if (this.isCreateMode) {

      if (this.uploadQueue.length == 0) {
        //if no attachments
        this.currentEntity.isSendUpdate = true;
      }
      this.entityService.create(this.currentEntity).subscribe(
        data => {
          this.utilityService.showSwalToast(
            'Success!',
            'Save successful.',
          );
          if (this.uploadQueue.length != 0) {
            this.currentEntity = data;
            this.currentEntity.isSendUpdate = true;
            this.uploadFiles();
          }
          this.create.emit(data);
          this.entityService.refreshList();

        }
      );

    } else {
      this.entityService.update(this.currentEntity).subscribe(
        (data) => {
          this.utilityService.showSwalToast(
            'Success!',
            'Save successful.',
          );
          this.update.emit(data);
          this.entityService.refreshList();
          this.activity.refresh();
          this.getCurrentHistory(this.nameOfEntity, this.entityID);
          if (task) {
            console.log('Trigger Task complete');
            this.wfTaskService.completeTask(task);
          }
        }
      );
    }
    // }
    // );

  }

  override onDelete() {
    const _messageText = `Delete ${this.nameOfEntity}: ` + this.currentEntity.title;
    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.entityService.delete(this.currentEntity.id).subscribe(
          () => {
            this.utilityService.showSwalToast(
              'Success!',
              'Delete successful.',
            );

            this.delete.emit(this.currentEntity);
            this.entityService.refreshList();
          }
        );
      }
    );
  }


  onStartFlow() {
    const _messageText = 'You want restart flow for '
      + this.currentEntity.title
      + '. All previously pending tasks will be deleted!.';
    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.entityService.startFlow(this.currentEntity.id).subscribe(
          () => {
            this.utilityService.showSwalToast(
              'Success!',
              'Save successful.',
            );
            this.update.emit(this.currentEntity);
          }
        );
      }
    );
  }

  onSelectContact(typeFlag: number = 0) {
    if (this.f['assignee'].value) {
      // console.log('typeFlag', typeFlag);
      this.createAssignee(typeFlag);
    }
  }

  getFilteredAssignees(assignees: RequestTicketAssignee[], typeFlag: number = 0): RequestTicketAssignee[] {
    if (assignees) {
      return assignees.filter(x => x.typeFlag == typeFlag);
    }
    return [];
  }


  private createAssignee(typeFlag: number = 0) {

    if (!this.f['assignee'].value || !this.f['assignee'].value.id) {
      this.utilityService.showSwalToast('Invalid Contact!', 'Please select a person and try again!', 'error');
      return;
    }
    let _assignee = new RequestTicketAssignee();
    _assignee.contactID = this.f['assignee'].value.id;
    _assignee.typeFlag = typeFlag;
    _assignee.name = (this.f['assignee'].value as EmailContact).name;
    _assignee.email = (this.f['assignee'].value as EmailContact).email;
    _assignee.company = (this.f['assignee'].value as EmailContact).company;

    this.f['assignee'].reset();
    if (this.isCreateMode) {
      // console.log(_assignee);
      this.currentEntity.assignees.push(_assignee);
    } else {
      _assignee.requestTicketID = this.currentEntity.id;
      this.assigneeService.create(_assignee).subscribe(
        data => {
          if (data) {
            this.currentEntity.assignees.push(data);
          }
          this.utilityService.showSwalToast(
            "Success!",
            "Save Successfull.",
          );
          this.f['assignee'].reset();
          this.activity.refresh();
          this.getCurrentHistory(this.nameOfEntity, this.entityID);
        }
      );
    }

  }

  onDeleteAssignee(assignee: RequestTicketAssignee) {
    if (assignee) {
      if (this.isCreateMode) {
        this.currentEntity.assignees = this.currentEntity.assignees.filter(x => x.contactID !== assignee.contactID);
      } else {
        this.assigneeService.delete(assignee.id).subscribe(
          data => {

            this.currentEntity.assignees = this.currentEntity.assignees.filter(x => x.id !== assignee.id);
            this.utilityService.showSwalToast(
              "Success!",
              "Delete Successfull.",
            );
            this.activity.refresh();
            this.getCurrentHistory(this.nameOfEntity, this.entityID);
          }
        );
      }
    }
  }

  private async getCurrentHistory(entity: string, entityID: number) {
    this.historyList = await this.entityService.get([{ key: 'parentID', value: entityID.toString() }, { key: 'IsVersion', value: 'true' }]).toPromise();
  }

  onTagsUpdate(tags: string[]) {
    if (tags) {
      this.currentEntity.searchTags = tags;
      this.entityService.update(this.currentEntity).subscribe(
        (data) => {
        }
      );
    }
  }


  onDeleteAttachment(item: any) {
    this.attachmentService.delete(item.id).subscribe(value => {
      this.delete.emit(item);
    });
    this.currentEntity.attachments = this.currentEntity.attachments.filter(x => x.id !== item.id);
  }

  onDownloadAttachment(item: any) {
    window.open(item.url);
  }

  uploadQueue: BlobUploadInfo[] = [];
  onUpload(uploads: BlobUploadInfo[]) {
    if (!this.isCreateMode) {
      uploads.forEach(x => {
        this.uploadQueue.push(x);
      });
      // console.log(this.uploadQueue);
      this.uploadFiles();
    } else {
      //Creating a dummy object
      uploads.forEach(x => {
        let obj = new RequestTicketAttachment();
        obj.filename = x.file.name;
        obj.size = x.file.size;
        obj.contentType = x.file.type;
        obj.guidname = x.blobPath;
        obj.blobPath = x.blobPath;
        obj.requestTicketID = this.currentEntity.id;
        obj.container = this.blobConfig.container;
        obj.typeFlag = this.currentEntity.typeFlag;
        obj.url = x.url;
        this.currentEntity.attachments.push(obj);
        this.uploadQueue.push(x);
      });
      // console.log(this.currentEntity.attachments,this.uploadQueue);
    }
  }

  private uploadFiles() {
    let _createRequests: any[] = [];
    this.uploadQueue.forEach(x => {
      let obj = new RequestTicketAttachment();
      obj.filename = x.file.name;
      obj.size = x.file.size;
      obj.contentType = x.file.type;
      obj.guidname = x.blobPath;
      obj.blobPath = x.blobPath;
      obj.requestTicketID = this.currentEntity.id;
      obj.container = this.blobConfig.container;
      obj.typeFlag = 0;
      obj.url = x.url;
      obj.originalUrl = x.url;
      _createRequests.push(this.attachmentService.create(obj));
    });
    this.uploadQueue = [];

    forkJoin(_createRequests).subscribe(results => {
      results.forEach(x => {
        this.currentEntity.attachments.push(x as RequestTicketAttachment);
      })
      if (this.isCreateMode) {
        this.entityService.update(this.currentEntity).subscribe(x => {
          this.create.emit(x);
        });

      }
    });
  }

  onProjectSelected(event: MatAutocompleteSelectedEvent) {
    this.selectedProject = event.option.value;
  }

}

