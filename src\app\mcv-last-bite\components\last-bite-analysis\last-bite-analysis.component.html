

<div class="data-filter-row" >
  <div class="filter-header" *ngIf="selectedProject.length > 0 || selectedStatus.length > 0 || selectedPartner.length > 0" >
     <h6 class="font-focused ">Filters:</h6> 
</div>
    <h6 *ngIf="selectedStatus.length > 0">
        <b>Status:</b> {{selectedStatus.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('status')">✖</span>
    </h6>
    <h6 *ngIf="selectedProject.length > 0">
        <b>Project:</b> {{selectedProject.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('project')">✖</span>
    </h6>
    <h6 *ngIf="selectedPartner.length > 0">
        <b>Partner:</b> {{selectedPartner.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('partner')">✖</span>
    </h6>
   
    <h6  (click)="resetFilter()" *ngIf="selectedProject.length > 0 || selectedStatus.length > 0 || selectedPartner.length > 0" >
      <b> Clear All</b>
     
    </h6>
</div>

<div class="last-bite-analysis-wrapper">
  <table>
    <thead>
      <tr>
        <th [ngClass]="{'sort': sortState['activeColumn'] === 'lastReceivedPaymentDate'}">
          <div class="analysis-table-header" (click)="sortData('lastReceivedPaymentDate')">
            <h6>Payment On</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastReceivedPaymentDate' && sortState['lastReceivedPaymentDate'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastReceivedPaymentDate' && sortState['lastReceivedPaymentDate'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'lastReceivedPaymentDate' || !sortState['lastReceivedPaymentDate']">import_export</mat-icon>
          </div>
        </th>
        <th [ngClass]="{'sort': sortState['activeColumn'] === 'previousPaymentDate'}">
          <div class="analysis-table-header" (click)="sortData('previousPaymentDate')">
            <h6>Previous Payment On</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'previousPaymentDate' && sortState['previousPaymentDate'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'previousPaymentDate' && sortState['previousPaymentDate'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'previousPaymentDate' || !sortState['previousPaymentDate']">import_export</mat-icon>
          </div>
        </th>
        <th [ngClass]="{'sort': sortState['activeColumn'] === 'lastBillNo'}">
          <div class="analysis-table-header" (click)="sortData('lastBillNo')">
            <h6> Bill No.</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastBillNo' && sortState['lastBillNo'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastBillNo' && sortState['lastBillNo'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'lastBillNo' || !sortState['lastBillNo']">import_export</mat-icon>
          </div>
        </th>
        <th mat-button [matMenuTriggerFor]="assignedToMenu" [ngClass]="{'filter': selectedProject.length > 0}">

          <div class="analysis-table-header">
            <h6>Project</h6>
            <mat-icon>filter_alt</mat-icon>

            <mat-menu #assignedToMenu="matMenu">
              <div class="search-container p-1">
                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                  <input matInput placeholder="Search" [(ngModel)]="projectKeySearch"
                    (input)="filterDistinctProject()" />
                  <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                </mat-form-field>
              </div>
              <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('project')">
                {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
              </button>
              <mat-option *ngFor="let project of filteredProject"
                (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                <mat-checkbox [checked]="selectedProject.includes(project)">{{ project }}</mat-checkbox>
              </mat-option>
            </mat-menu>
          </div>
        </th>
        <th mat-button [matMenuTriggerFor]="statusMenu" [ngClass]="{'filter': selectedStatus.length > 0}">
          <div class="analysis-table-header">
            <h6>Status</h6>
            <mat-icon>filter_alt</mat-icon>

            <mat-menu #statusMenu="matMenu">
              <div class="search-container p-1">
                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                  <input matInput placeholder="Search" [(ngModel)]="statusSearch"
                    (input)="filterDistinctStatus()" />
                  <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                </mat-form-field>
              </div>
              <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('status')">
                {{ isAllSelected('status') ? 'Deselect All' : 'Select All' }}
              </button>
              <mat-option *ngFor="let status of filteredStatus"
                (click)="$event.stopPropagation(); toggleSelection(status, 'status')">
                <mat-checkbox [checked]="selectedStatus.includes(status)">{{ status }}</mat-checkbox>
              </mat-option>
            </mat-menu>
          </div>
        </th>
        <th mat-button [matMenuTriggerFor]="partnerMenu" [ngClass]="{'filter': selectedPartner.length > 0}">
        

          <div class="analysis-table-header">
            <h6>Partner</h6>
            <mat-icon>filter_alt</mat-icon>

            <mat-menu #partnerMenu="matMenu">
              <div class="search-container p-1">
                <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                  <input matInput placeholder="Search" [(ngModel)]="partnerSearch"
                    (input)="filterDistinctPartner()" />
                  <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                </mat-form-field>
              </div>
              <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('partner')">
                {{ isAllSelected('partner') ? 'Deselect All' : 'Select All' }}
              </button>
              <mat-option *ngFor="let partner of filteredPartner"
                (click)="$event.stopPropagation(); toggleSelection(partner, 'partner')">
                <mat-checkbox [checked]="selectedPartner.includes(partner)">{{ partner }}</mat-checkbox>
              </mat-option>
            </mat-menu>
          </div>

        </th>
        <th class="text-right" [ngClass]="{'sort': sortState['activeColumn'] === 'lastReceivedPaymentAmount'}">

          <div class="analysis-table-header" (click)="sortData('lastReceivedPaymentAmount')">
            <h6>Payment</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastReceivedPaymentAmount' && sortState['lastReceivedPaymentAmount'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastReceivedPaymentAmount' && sortState['lastReceivedPaymentAmount'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'lastReceivedPaymentAmount' || !sortState['lastReceivedPaymentAmount']">import_export</mat-icon>
          </div>
        </th>
        <th class="text-right" [ngClass]="{'sort': sortState['activeColumn'] === 'partnerAmount'}">

          <div class="analysis-table-header" (click)="sortData('partnerAmount')">
            <h6>Partner's Share</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'partnerAmount' && sortState['partnerAmount'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'partnerAmount' && sortState['partnerAmount'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'partnerAmount' || !sortState['partnerAmount']">import_export</mat-icon>
          </div>
        </th>
        <th class="text-right" [ngClass]="{'sort': sortState['activeColumn'] === 'vHrCost'}">

          <div class="analysis-table-header" (click)="sortData('vHrCost')">
            <h6>VHr Consumed</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'vHrCost' && sortState['vHrCost'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'vHrCost' && sortState['vHrCost'] === 'oldFirst'">north</mat-icon>
            <mat-icon 
            *ngIf="sortState['activeColumn'] !== 'vHrCost' || !sortState['vHrCost']">import_export</mat-icon>
          </div>
        </th>
        <th class="text-right" [ngClass]="{'sort': sortState['activeColumn'] === 'lastBiteAmount'}">
          <div class="analysis-table-header" (click)="sortData('lastBiteAmount')">
            <h6>Last Bite</h6>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastBiteAmount' && sortState['lastBiteAmount'] === 'newFirst'">south</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] === 'lastBiteAmount' && sortState['lastBiteAmount'] === 'oldFirst'">north</mat-icon>
            <mat-icon
              *ngIf="sortState['activeColumn'] !== 'lastBiteAmount' || !sortState['lastBiteAmount']">import_export</mat-icon>
          </div>
        </th>
      </tr>
      <tr *ngIf="total" class="total">
        <td colspan="6"><strong>Total</strong></td>
        <td class="text-right">{{ total?.lastReceivedPaymentAmount | currency:'INR':'symbol':'1.2-2' }}</td>
        <td class="text-right">{{ total?.partnerAmount | currency:'INR':'symbol':'1.2-2' }}</td>
        <td class="text-right">{{ total?.vHrCost | currency:'INR':'symbol':'1.2-2' }}</td>
        <td class="text-right">
            {{ total?.lastBiteAmount | currency:'INR':'symbol':'1.2-2' }}
        </td>
      </tr>
    </thead>
    <tbody class="table-body">
      <ng-container *ngFor="let item of dataList; let i=index">
        <tr>
          <td class="text-right">{{item?.lastReceivedPaymentDate | date:'dd MMM y'}}</td>
          <td class="text-right">{{item?.previousPaymentDate | date:'dd MMM y'}}</td>
          <td class="text-right">{{item?.lastBillNo}}</td>
          <td>{{item?.code}} - {{item?.project}}</td>
          <td>{{item?.status}}</td>
          <td>{{item?.partner}}</td>
          <td class="text-right">{{item?.lastReceivedPaymentAmount | currency:'INR':'symbol':'1.2-2'}}</td>
          <td class="text-right">{{item?.partnerAmount | currency:'INR':'symbol':'1.2-2'}}</td>
          <td class="text-right">{{item?.vHrCost | currency:'INR':'symbol':'1.2-2'}}</td>
          <td class="text-right">
            <strong [ngClass]="{'text-success': item.lastBiteAmount > 0, 'text-danger': item.lastBiteAmount < 0}">
              {{ item?.lastBiteAmount | currency:'INR':'symbol':'1.2-2' }}
            </strong>
          </td>
        </tr>


      </ng-container>
      <!-- <tr >
        <td colspan="10">
        <ng-container *ngFor="let item of dataList">
          <mcv-last-bite-analysis-list-item [item]="item" [isMobileView]="isMobileView"></mcv-last-bite-analysis-list-item>
        </ng-container>
        </td>
      </tr> -->


      <tr *ngIf="!isLoading && dataList.length == 0">
        <td colspan="10" class="text-center">No items found</td>
      </tr>

      <tr *ngIf="!isLoading && totalRecordsCount > 0 && currentPage + 1 < totalPages" (click)="loadMoreRecords()"
        style="cursor:pointer;">
        <td colspan="10" class="text-center">
          <h5>Show More</h5>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<app-footer>
  <div class="nav-footer-actions">
    <div>
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>
    <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>

    
    


    <button mat-raised-button [color]="showAll ? 'accent' : ''" (click)="onToggleShowAll()"
      *ngIf="isPermissionSpecialShowAll" matTooltip="Show All" aria-label="Show All">
      Show All
    </button>

    <button mat-raised-button (click)="onExportExcel()" matTooltip="export excel" aria-label="export excel">Export
      Excel</button>
  </div>

  <div class="nav-footer-mobile-actions">
    <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>


    <button mat-raised-button [color]="showAll ? 'accent' : ''" (click)="onToggleShowAll()"
      *ngIf="isPermissionSpecialShowAll" matTooltip="Show All" aria-label="Show All">
      Show All
    </button>
    <button mat-raised-button (click)="onExportExcel()" matTooltip="export excel" aria-label="export excel">Export
      Excel</button>
  </div>

  <div class="nav-filters">
    <div class="inline-list">
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>Select Range</mat-label>
        <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker2">
          <input matStartDate placeholder="Start date" formControlName="start" readonly>
          <input matEndDate placeholder="End date" formControlName="end" readonly>
        </mat-date-range-input>
        <mat-datepicker-toggle matSuffix [for]="rangePicker2"></mat-datepicker-toggle>
        <mat-date-range-picker [touchUi]="isMobileView" #rangePicker2></mat-date-range-picker>
      </mat-form-field>

    


      
    </div>
  </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav >

<div>
  <mat-form-field appearance="outline">
    <mat-label>Select Range</mat-label>
    <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
      <input matStartDate placeholder="Start date" formControlName="start" readonly>
      <input matEndDate placeholder="End date" formControlName="end" readonly>
    </mat-date-range-input>
    <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
    <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
  </mat-form-field>
</div>
</app-mcv-filter-sidenav>