import { ActivatedRoute } from '@angular/router';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';

import { debounceTime, distinctUntilChanged, tap } from 'rxjs/operators';

import { AppService } from 'src/app/services/app.service';
import { TypeMasterService } from 'src/app/services/type-master.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { BillApiService } from 'src/app/project/services/bill-api.service';
import { StatusMasterService } from 'src/app/services/status-master.service';

import { AppConfig } from 'src/app/app.config';
import { ApiFilter } from 'src/app/models/api-filters';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { BillAnalysisDto } from '../../../models/bill-analysis-dto';
import { BillAnalysisDataSource } from '../../datasources/bill-analysis-data-source';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { NgClass, NgFor, NgIf, PercentPipe, CurrencyPipe, DatePipe } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTableModule } from '@angular/material/table';
import { HeaderComponent } from '../../../mcv-header/components/header/header.component';
import { firstValueFrom } from 'rxjs';
import { CompanyAccount } from 'src/app/models/company-account';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';


@Component({
    selector: 'app-bill-analysis',
    templateUrl: './bill-analysis.component.html',
    styleUrls: ['./bill-analysis.component.scss'],
    standalone: true,
    imports: [HeaderComponent, MatTableModule, MatTooltipModule, NgClass, FooterComponent, ReactiveFormsModule, FormsModule, MatButtonModule, MatIconModule, NgFor, NgIf, MatFormFieldModule, MatSelectModule, MatOptionModule, MatInputModule, MatDatepickerModule, FilterToggleDirective, PercentPipe, CurrencyPipe, DatePipe]
})
export class BillAnalysisComponent implements OnInit, AfterViewInit, OnDestroy {

  @Input() showFilters: boolean = false;
  @Output() rowClick = new EventEmitter();
  // @ViewChild(MatPaginator) paginator: MatPaginator;
  // @ViewChild(MatSort) matSort: MatSort;

  filters: ApiFilter[] = [{ key: 'typeFlag', value: '0' }];
  typeOptions = [{ title: 'Proforma', value: 0 }, { title: 'Invoice', value: 1 }];
  dataSource!: BillAnalysisDataSource;

  searchFish: string = '';
  // totalRecordsCount = 0;
  // totalPages = 0;
  // readonly PAGE_SIZE = 50;
  // pageSize = this.PAGE_SIZE;
  // currentPage = 0;
  readonly defaultSort: string = 'billdate desc';
  sort!: string;
  selectedRow!: BillAnalysisDto;
  total!: BillAnalysisDto;
  statusOptions: StatusMaster[] = [];
  statusFC = new FormControl();
  headerTitle: string = '';

  companyOptions: CompanyAccount[] = [];
  companyFC = new FormControl();

  form!: FormGroup;
  get f(): any { return this.form.controls; }

  get isMobileView(): boolean {
    return this.utilityService.isMobileView;
  }
  constructor(
    private utilityService: UtilityService,
    private formBuilder: FormBuilder,
    private changeDetectorRefs: ChangeDetectorRef,
    private billService: BillApiService,
    private route: ActivatedRoute,
    private appService: AppService,
    private bottomSheet: MatBottomSheet,
    private typeMasterService: TypeMasterService,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
    private config: AppConfig
  ) { }

  ngOnDestroy(): void {
    this.appService.resetHeader();
  }

  ngOnInit() {
    this.headerTitle = this.route.snapshot.data['title'];
    // this.appService.setHeaderTitle(this.route.snapshot.data.title);
    if (!this.form) {
      this.buildForm();
    }
    this.refresh();

    this.getTypeOptions();
    this.getStatusOptions();
    this.getCompanyOptions();
    this.statusFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== 'projectstatusFlag');
            value.forEach((element: any) => {
              this.addFilter('projectstatusFlag', element);
            });
            this.search();
          }
        }
      );

      this.companyFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "companyID");
            value.forEach((e: any) => {
              if (e && e.id) {
                this.addFilter('companyID', e.id);
              }
            });
            this.search();
          }
        }
      );
    this.dataSource.total$.subscribe(value => {
      // console.log('total',value);
      this.total = value;
    });
  }

  ngAfterViewInit() {
    // reset the paginator after sorting
    // this.matSort.sortChange
    //   .pipe(
    //     tap(() => {
    //       this.loadPage(this.filters,
    //         this.searchFish, this.sort);
    //     })
    //   ).subscribe((value: Sort) => {
    //     this.sort = (value.active + ' ' + value.direction).trim();
    //   });
  }

  buildForm() {
    this.form = this.formBuilder.group({
      startDateFilter: new FormControl<any>(null),
      endDateFilter: new FormControl<any>(null),
    });

    if (this.filters && this.filters.length > 0) {
      const startDate = this.filters.find(x => x.key.toLowerCase() === 'rangestart');
      const endDate = this.filters.find(x => x.key.toLowerCase() === 'rangeend');
      const typeFlags = this.filters.filter(x => x.key.toLowerCase() === 'typeFlag').map(x => x.value);

      if (startDate) {
        this.f['startDateFilter'].setValue(startDate.value, { emitEvent: false });
      }

      if (endDate) {
        this.f['endDateFilter'].setValue(endDate.value, { emitEvent: false });
      }

      if (typeFlags && typeFlags.length !== 0) {
        this.f['typeFilter'].setValue(typeFlags, { emitEvent: false });
      }
    }

    this.loadFilters();
  }

  refresh() {

    this.searchFish = '';
    this.sort = this.defaultSort;

    this.dataSource = new BillAnalysisDataSource(this.billService);
    this.loadPage(this.filters, this.searchFish, this.sort);
    this.changeDetectorRefs.detectChanges();

  }

  protected async getStatusOptions() {
    this.statusOptions = await firstValueFrom(this.statusMasterService.get([{ key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT}]));
  }

  protected async getCompanyOptions() {
    this.companyOptions = await firstValueFrom(this.companyAccountService.get());
  }

  loadFilters() {
    // this.f['typeFilter'].valueChanges
    // .pipe(
    //   debounceTime(400),
    //   distinctUntilChanged()
    // )
    // .subscribe(
    //   (value) => {
    //     this.filters = this.filters.filter(i => i.key.toLowerCase() !== "typeflag");
    //     value.forEach(element => {
    //       this.addFilter('typeFlag', element);
    //     });
    //   }
    // );
    this.f['startDateFilter'].valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value: any) => {
          this.filters = this.filters.filter(i => i.key.toLowerCase() !== "rangestart");
          this.addFilter('rangestart', value);
          this.search();
        }
      );
    this.f['endDateFilter'].valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value: any) => {
          this.filters = this.filters.filter(i => i.key.toLowerCase() !== "rangeend");
          this.addFilter('rangeend', value);
          this.search();
        }
      );

  }

  toggleType(value: number) {
    this.filters = this.filters.filter(i => i.key.toLowerCase() !== "typeflag");
    this.addFilter('typeFlag', value.toString());
    this.search();
  }

  isFilter(key: string, value: string): boolean {
    const _filter = this.filters.find(obj => {
      return obj.key.toLowerCase() === key.toLowerCase() && obj.value === value;
    });
    if (_filter) return true;
    return false;
  }

  addFilter(key: string, value: string) {
    if (!this.isFilter(key, value)) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  loadPage(filters?: any, searchFish?: string, sort?: string) {
    this.dataSource.loadData(filters, searchFish, sort);

  }

  onRowClick(row: any) {
    this.selectedRow = row;
    this.rowClick.emit(this.selectedRow);
  }

  search() {
    // this.currentPage = 0;
    // if (this.paginator) {
    //   this.paginator.pageIndex = 0;
    // }
    this.loadPage(this.filters, this.searchFish, this.sort);
  }

  toggleFilter(filter: string) {
    this.search();
  }



  exportExcel() {
    this.filters = this.filters.filter(x => x.key != 'typeFlag');
    this.billService.exportExcel(this.filters, this.searchFish, this.sort);
  }
nameOfEntity = this.config.NAMEOF_ENTITY_PROJECT_BILL;
  private async getTypeOptions() {
    const storedValue = localStorage.getItem('BillTypeOptions');

    if (storedValue !== null) {
      this.typeOptions = JSON.parse(storedValue);
    }
    this.typeOptions = await firstValueFrom(this.typeMasterService.get([{ key: 'entity', value: this.nameOfEntity}]));

        localStorage.setItem(
          "BillTypeOptions",
          JSON.stringify(this.typeOptions)
        );
     
  }

  displayedColumns() {
    return [
      'project',
      'projectStatus',
      'partner',
      'client',
      'workCompletion',
      'billDate',
      'billNo',
      'totalAmount',
      'totalReceived',
      'currentAmount',
      'currentRecieved',
      'score',
      'igstAmount',
      'cgstAmount',
      'sgstAmount',
      'paymentDate',
    ];

  }
}
