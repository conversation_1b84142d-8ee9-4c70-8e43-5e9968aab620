import { ActivatedRoute } from '@angular/router';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import {
  Form<PERSON><PERSON>er,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  FormsModule,
  Validators,
} from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';

import { debounceTime, distinctUntilChanged, tap } from 'rxjs/operators';

import { AppService } from 'src/app/services/app.service';
import { TypeMasterService } from 'src/app/services/type-master.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { BillApiService } from 'src/app/project/services/bill-api.service';
import { StatusMasterService } from 'src/app/services/status-master.service';

import { AppConfig } from 'src/app/app.config';
import { ApiFilter } from 'src/app/models/api-filters';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { BillAnalysisDto } from '../../../models/bill-analysis-dto';
import { BillAnalysisDataSource } from '../../datasources/bill-analysis-data-source';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import {
  NgClass,
  NgFor,
  NgIf,
  PercentPipe,
  CurrencyPipe,
  DatePipe,
  CommonModule,
} from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTableModule } from '@angular/material/table';
import { HeaderComponent } from '../../../mcv-header/components/header/header.component';
import { firstValueFrom } from 'rxjs';
import { CompanyAccount } from 'src/app/models/company-account';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
  selector: 'app-bill-analysis',
  templateUrl: './bill-analysis.component.html',
  styleUrls: ['./bill-analysis.component.scss'],
  standalone: true,
  imports: [
    HeaderComponent,
    CommonModule,
    MatTableModule,
    MatTooltipModule,
    MatMenuModule,
    MatCheckboxModule,
    FormsModule,
    CommonModule,
    FooterComponent,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    McvFilterSidenavComponent,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatDatepickerModule,
    FilterToggleDirective
  ],
})
export class BillAnalysisComponent implements OnInit, OnDestroy {
  @Input() showFilters: boolean = false;
  @Output() rowClick = new EventEmitter();
  // @ViewChild(MatPaginator) paginator: MatPaginator;
  // @ViewChild(MatSort) matSort: MatSort;
  dateFilters!: FormGroup;
  filters: ApiFilter[] = [{ key: 'typeFlag', value: '0' }];
  typeOptions = [
    { title: 'Proforma', value: 0 },
    { title: 'Invoice', value: 1 },
  ];
  dataSource!: BillAnalysisDataSource;
  dataList: BillAnalysisDto[] = [];

  // totalRecordsCount = 0;
  // totalPages = 0;
  // readonly PAGE_SIZE = 50;
  // pageSize = this.PAGE_SIZE;
  // currentPage = 0;
  readonly defaultSort: string = 'billdate desc';
  sort!: string;
  selectedRow!: BillAnalysisDto;
  searchFilter!: FormControl;
  total: BillAnalysisDto = new BillAnalysisDto();
  statusOptions: StatusMaster[] = [];
  statusFC = new FormControl();
  headerTitle: string = '';
  searchKey!: string;
  companyOptions: CompanyAccount[] = [];
  companyFC = new FormControl();
  filteredProjects: string[] = [];
  selectedProjects: string[] = [];
  distinctProjects: string[] = [];
  projectKeySearch: string = '';
  filteredProjectStatus: string[] = [];
  selectedProjectStatus: string[] = [];
  distinctProjectStatus: string[] = [];
  projectStatusKeySearch: string = '';
  filteredPartner: string[] = [];
  selectedPartner: string[] = [];
  distinctPartner: string[] = [];
  partnerKeySearch: string = '';
  filteredClient: string[] = [];
  selectedClient: string[] = [];
  distinctClient: string[] = [];
  clientKeySearch: string = '';
  isSorted: boolean = false;
  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '',
    billDate: '',
    totalAmount: '',
    totalReceived: '',
    currentAmount: '',
    currentRecieved: '',
    score: '',
    igstAmount: '',
    cgstAmount: '',
    sgstAmount: '',
    paymentDate: '',
    workCompletion: '',
  };


  form!: FormGroup;
  // get f(): any { return this.form.controls; }
  originalDataList: any[] = [];
  get isMobileView(): boolean {
    return this.utilityService.isMobileView;
  }
  constructor(
    private utilityService: UtilityService,
    private formBuilder: FormBuilder,
    private changeDetectorRefs: ChangeDetectorRef,
    private billService: BillApiService,
    private route: ActivatedRoute,
    private appService: AppService,
    private bottomSheet: MatBottomSheet,
    private typeMasterService: TypeMasterService,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
    private config: AppConfig,
    private utility: UtilityService
  ) {}

  ngOnDestroy(): void {
    this.appService.resetHeader();
  }

  ngOnInit() {
    this.headerTitle = this.route.snapshot.data['title'];
    // this.appService.setHeaderTitle(this.route.snapshot.data.title);
   
    this.buildForm();
   
    this.refresh();

    this.getTypeOptions();
    this.getStatusOptions();
    this.getCompanyOptions();
    this.statusFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter(
            (i) => i.key !== 'projectstatusFlag'
          );
          value.forEach((element: any) => {
            this.addFilter('projectstatusFlag', element);
          });
          this.search();
        }
      });

    this.companyFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter((i) => i.key !== 'companyID');
          value.forEach((e: any) => {
            if (e && e.id) {
              this.addFilter('companyID', e.id);
            }
          });
          this.search();
        }
      });
    // this.dataSource.total$.subscribe(value => {

    //   this.total = value;
    // });
  }

  buildForm() {
    this.dateFilters = this.formBuilder.group({
      start: new FormControl<any>(null, { validators: [Validators.required] }),
      end: new FormControl<any>(null, { validators: [Validators.required] }),
    });

    this.dateFilters.controls['start'].setValue(this.utility.getMonthStart(), {
      emitEvent: false,
    });

    this.dateFilters.controls['end'].setValue(this.utility.getMonthEnd(), {
      emitEvent: false,
    });

    this.dateFilters.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.filters = this.filters.filter(
            (x) => x.key !== 'rangeStart' && x.key != 'rangeEnd'
          );
          this.filters.push({
            key: 'rangeStart',
            value: value.start.toISOString(),
          });
          this.filters.push({
            key: 'rangeEnd',
            value: value.end.toISOString(),
          });

          this.search();
        }
      });

    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.searchKey = value;
          this.search();
        }
      });
  }

  refresh() {
    this.searchKey = '';
    this.sort = this.defaultSort;

    // this.dataSource = new BillAnalysisDataSource(this.billService);

    this.getData(this.filters, this.searchKey, this.sort);
    // this.changeDetectorRefs.detectChanges();
  }

  protected async getStatusOptions() {
    this.statusOptions = await firstValueFrom(
      this.statusMasterService.get([
        { key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT },
      ])
    );
  }

  protected async getCompanyOptions() {
    this.companyOptions = await firstValueFrom(
      this.companyAccountService.get()
    );
  }

  toggleType(value: number) {
    this.filters = this.filters.filter(
      (i) => i.key.toLowerCase() !== 'typeflag'
    );
    this.addFilter('typeFlag', value.toString());
    this.search();
  }

  isFilter(key: string, value: string): boolean {
    const _filter = this.filters.find((obj) => {
      return obj.key.toLowerCase() === key.toLowerCase() && obj.value === value;
    });
    if (_filter) return true;
    return false;
  }

  addFilter(key: string, value: string) {
    if (!this.isFilter(key, value)) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  async getData(filters?: any, searchKey?: string, sort?: string) {
    // this.dataSource.loadData(filters, searchFish, sort);

    this.dataList = await firstValueFrom(
      this.billService.getAnalysis(filters, searchKey, sort)
    );
    console.log('this.dataList',this.dataList);
    this.extractDistinctValues();
    this.originalDataList = [...this.dataList];
    this.calculateTotal();
  }
  calculateTotal() {
    this.total = new BillAnalysisDto();
    this.dataList.forEach((x) => {
      this.total.totalAmount += x.totalAmount;
      this.total.totalReceived += x.totalReceived;
      this.total.currentAmount += x.currentAmount;
      this.total.currentRecieved += x.currentRecieved;
      this.total.score += x.score;
      this.total.igstAmount += x.igstAmount;
      this.total.cgstAmount += x.cgstAmount;
      this.total.sgstAmount += x.sgstAmount;
    });
  }
  onRowClick(row: any) {
    this.selectedRow = row;
    this.rowClick.emit(this.selectedRow);
  }

  search() {
    // this.currentPage = 0;
    // if (this.paginator) {
    //   this.paginator.pageIndex = 0;
    // }
    this.getData(this.filters, this.searchKey, this.sort);
  }

  toggleFilter(filter: string) {
    this.search();
  }

  exportExcel() {
    this.filters = this.filters.filter((x) => x.key != 'typeFlag');
    this.billService.exportExcel(this.filters, this.searchKey, this.sort);
  }
  nameOfEntity = this.config.NAMEOF_ENTITY_PROJECT_BILL;
  private async getTypeOptions() {
    const storedValue = localStorage.getItem('BillTypeOptions');

    if (storedValue !== null) {
      this.typeOptions = JSON.parse(storedValue);
    }
    this.typeOptions = await firstValueFrom(
      this.typeMasterService.get([{ key: 'entity', value: this.nameOfEntity }])
    );

    localStorage.setItem('BillTypeOptions', JSON.stringify(this.typeOptions));
  }

  displayedColumns() {
    return [
      'project',
      'projectStatus',
      'partner',
      'client',
      'workCompletion',
      'billDate',
      'billNo',
      'totalAmount',
      'totalReceived',
      'currentAmount',
      'currentRecieved',
      'score',
      'igstAmount',
      'cgstAmount',
      'sgstAmount',
      'paymentDate',
    ];
  }


  filterDistinctProjects() {
    this.filteredProjects = this.distinctProjects.filter((project) =>
      project.toLowerCase().includes(this.projectKeySearch.toLowerCase())
    );
  }
  filterDistinctProjectStatus() {
    this.filteredProjectStatus = this.distinctProjectStatus.filter((projectStatus) =>
      projectStatus.toLowerCase().includes(this.projectStatusKeySearch.toLowerCase())
    );
  }
  filterDistinctPartner() {
    this.filteredPartner = this.distinctPartner.filter((partner) =>
      partner.toLowerCase().includes(this.partnerKeySearch.toLowerCase())
    );
  }
  filterDistinctClient() {
    this.filteredClient = this.distinctClient.filter((client) =>
      client.toLowerCase().includes(this.clientKeySearch.toLowerCase())
    );
  }

  clearSearch(event: Event) {
    event.stopPropagation();
    this.projectKeySearch = '';
    this.projectStatusKeySearch = '';
    this.partnerKeySearch = '';
    this.clientKeySearch = '';

    this.filterDistinctProjects();
    this.filterDistinctProjectStatus();
    this.filterDistinctPartner();
    this.filterDistinctClient();
  }

  isAllSelected(type: string) {
    if (type === 'project') {
      return this.selectedProjects.length === this.filteredProjects.length;
    }
    if (type === 'projectStatus') {
      return this.selectedProjectStatus.length === this.filteredProjectStatus.length;
    }
    if (type === 'partner') {
      return this.selectedPartner.length === this.filteredPartner.length;
    }
    if (type === 'client') {
      return this.selectedClient.length === this.filteredClient.length;
    }
    return false;
  }

  toggleSelectAll(type: string) {
    if (type === 'project') {
      this.selectedProjects = this.isAllSelected(type) ? [] : [...this.filteredProjects];
    }
    if (type === 'projectStatus') {
      this.selectedProjectStatus = this.isAllSelected(type) ? [] : [...this.filteredProjectStatus];
    }
    if (type === 'partner') {
      this.selectedPartner = this.isAllSelected(type) ? [] : [...this.filteredPartner];
    }
    if (type === 'client') {
      this.selectedClient = this.isAllSelected(type) ? [] : [...this.filteredClient];
    }
    this.applyFilters();
  }

  toggleSelection(value: string, type: string) {
    if (type === 'project') {
      this.selectedProjects.includes(value)
        ? this.selectedProjects.splice(this.selectedProjects.indexOf(value), 1)
        : this.selectedProjects.push(value);
    }
    if (type === 'projectStatus') {
      this.selectedProjectStatus.includes(value)
        ? this.selectedProjectStatus.splice(this.selectedProjectStatus.indexOf(value), 1)
        : this.selectedProjectStatus.push(value);
    }
    if (type === 'partner') {
      this.selectedPartner.includes(value)
        ? this.selectedPartner.splice(this.selectedPartner.indexOf(value), 1)
        : this.selectedPartner.push(value);
    }
    if (type === 'client') {
      this.selectedClient.includes(value)
        ? this.selectedClient.splice(this.selectedClient.indexOf(value), 1)
        : this.selectedClient.push(value);
    }
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if(this.searchKey){

      const searchLower = this.searchKey.toLowerCase();
  
     filteredList = filteredList.filter(item =>
      
        (item.project?.toLowerCase().includes(searchLower) || '') 
        ||
        (item.projectStatus?.toLowerCase().includes(searchLower) || '') ||
        (item.partner?.toLowerCase().includes(searchLower) || '') ||
        (item.client?.toLowerCase().includes(searchLower) || '') 

      );
    }
  
    if (this.selectedProjects.length > 0) {
      filteredList = filteredList.filter(item => this.selectedProjects.includes(item.project));
    }
    if (this.selectedProjectStatus.length > 0) {
      filteredList = filteredList.filter(item => this.selectedProjectStatus.includes(item.projectStatus));
    }
    if (this.selectedPartner.length > 0) {
      filteredList = filteredList.filter(item => this.selectedPartner.includes(item.partner));
    }
    if (this.selectedClient.length > 0) {
      filteredList = filteredList.filter(item => this.selectedClient.includes(item.client));
    }
   

    
    this.dataList = filteredList;
    this.getTotal();
  }
  
  getTotal() {
    this.total = new BillAnalysisDto();
    this.dataList.forEach((x) => {
      this.total.totalAmount += x.totalAmount;
      this.total.totalReceived += x.totalReceived;
      this.total.currentAmount += x.currentAmount;
      this.total.currentRecieved += x.currentRecieved;
      this.total.score += x.score;
      this.total.igstAmount += x.igstAmount;
      this.total.cgstAmount += x.cgstAmount;
      this.total.sgstAmount += x.sgstAmount;
    });
  }

  extractDistinctValues() {
    this.distinctProjects = [...new Set(this.dataList.map((x) => x.project))];
    this.filteredProjects = [...this.distinctProjects];
    this.distinctProjectStatus = [...new Set(this.dataList.map((x) => x.projectStatus))];
    this.filteredProjectStatus = [...this.distinctProjectStatus];
    this.distinctPartner = [...new Set(this.dataList.map((x) => x.partner))];
    this.filteredPartner = [...this.distinctPartner];
    this.distinctClient = [...new Set(this.dataList.map((x) => x.client))];
    this.filteredClient = [...this.distinctClient];
  }
  resetFilter() {
    this.selectedProjects = [];
    this.selectedProjectStatus = [];
    this.selectedPartner = [];
    this.selectedClient = [];
    this.dataList = [...this.originalDataList];

    this.getTotal();
  }
  
  clearSelection(type: string) {
    if (type === 'project') {
      this.selectedProjects = [];
    } else if (type === 'projectStatus') {
      this.selectedProjectStatus = [];
    } else if (type === 'partner') {
      this.selectedPartner = [];
    } else if (type === 'client') {
      this.selectedClient = [];
    }
    this.applyFilters();
  }

  
  sortData(column: keyof BillAnalysisDto | '') {
      if (column === '') {
        // Reset to default (original data) but apply the filters again
        const dataToFilter = this.originalDataList;
    
        // Apply filters to the original data list
        this.dataList = dataToFilter.filter((item:any) => {
          return (
         (this.selectedProjects.length === 0 || this.selectedProjects.includes(item.project)) &&
          (this.selectedProjectStatus.length === 0 || this.selectedProjectStatus.includes(item.projectStatus)) && 
          (this.selectedPartner.length === 0 || this.selectedPartner.includes(item.partner)) &&
          (this.selectedClient.length === 0 || this.selectedClient.includes(item.client))
          
          );
        });
    
        // Reset the sort state to default
        this.sortState = { 
           activeColumn: '',
        totalAmount: '',
        totalReceived: '',
        currentAmount: '',
        currentRecieved: '',
        score: '',
        igstAmount: '',
        cgstAmount: '',
        sgstAmount: '',
        billDate: '',
        paymentDate: '',
        workCompletion: '',
        };
        this.isSorted = false; // Set isSorted to false when sorting is reset
        return; // Exit the function if no sorting is selected
      }
    
      // If the clicked column is already the active column, cycle through the three states
      const currentSort = this.sortState[column];
    
      if (currentSort === 'newFirst') {
        // If it's 'newFirst', change to 'oldFirst'
        this.sortState[column] = 'oldFirst';
      } else if (currentSort === 'oldFirst') {
        // If it's 'oldFirst', reset to default (no sorting)
        this.sortState[column] = '';
        this.sortState.activeColumn = '';
        const dataToFilter = this.originalDataList;
    
        // Apply filters to the original data list
        this.dataList = dataToFilter.filter((item:any) => {
          return (
         (this.selectedProjects.length === 0 || this.selectedProjects.includes(item.project)) &&
          (this.selectedProjectStatus.length === 0 || this.selectedProjectStatus.includes(item.projectStatus)) && 
          (this.selectedPartner.length === 0 || this.selectedPartner.includes(item.partner)) &&
          (this.selectedClient.length === 0 || this.selectedClient.includes(item.client))
          );
        });
    
        this.isSorted = false; // Set isSorted to false when sorting is reset
        return; // Exit the function if reset is selected
      } else {
        // If no sorting is applied, set it to 'newFirst' (ascending order)
        this.sortState[column] = 'newFirst';
      }
    
      // Set the active column
      this.sortState['activeColumn'] = column;
    
      // Reset other columns' sort state to '' (no sort)
      for (let key in this.sortState) {
        if (key !== column && key !== 'activeColumn') {
          this.sortState[key] = ''; // Reset other columns' sort state to no sort
        }
      }
    
      // Sorting logic: Compare dates for the active column
      const sortedData = [...this.dataList].sort((a, b) => {
        const dateA = new Date(a[column] as string);
        const dateB = new Date(b[column] as string);
    
        if (this.sortState[column] === 'newFirst') {
          return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
        } else if (this.sortState[column] === 'oldFirst') {
          return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
        }
        return 0; // If no sorting, return unchanged order
      });
    
      // Update the dataList with the sorted data
      this.dataList = sortedData;
      this.isSorted = true; // Set isSorted to true when sorting is applied
    }
}
