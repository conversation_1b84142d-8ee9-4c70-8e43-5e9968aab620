<div class="vhr-analysis-wrapper">
  <div class="data-filter-row" >
    <div class="filter-header" *ngIf="selectedPerson.length > 0 || selectedCompany.length > 0 || selectedRenumerationType.length > 0" >
       <h6 class="font-focused ">Filters:</h6> 
  </div>
      <h6 *ngIf="selectedPerson.length > 0">
          <b>Person:</b> {{selectedPerson.join(', ')}}
          <span class="clear-icon" (click)="clearSelection('person')">✖</span>
      </h6>
      <h6 *ngIf="selectedCompany.length > 0">
          <b>Company:</b> {{selectedCompany.join(', ')}}
          <span class="clear-icon" (click)="clearSelection('company')">✖</span>
      </h6>
      <h6 *ngIf="selectedRenumerationType.length > 0">
          <b>Renumeration Type:</b> {{selectedRenumerationType.join(', ')}}
          <span class="clear-icon" (click)="clearSelection('renumerationType')">✖</span>
      </h6>

      <h6  (click)="resetFilter()" *ngIf="selectedPerson.length > 0 || selectedCompany.length > 0 || selectedRenumerationType.length > 0" >
        <b> Clear All</b>
       
      </h6>
  </div>
  <div [ngClass]="{'mcv-vhr-analysis-table-mobileview':isMobileView}">
    <table>
      <thead>
        <tr class="table-header">
          <th></th>
          <th mat-button [matMenuTriggerFor]="personMenu" [ngClass]="{'filter': selectedPerson.length > 0}">
            <div class="analysis-table-header">
              <h6>Person</h6>
              <mat-icon>filter_alt</mat-icon>
              <mat-menu #personMenu="matMenu">
                  <div class="search-container p-1">
                      <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                          <input
                          matInput
                          placeholder="Search"
                          [(ngModel)]="personKeySearch"
                          (input)="filterDistinctPerson()"
                        />
                        <mat-icon class="clear-icon"
                        matSuffix
                        (click)="clearSearch($event)">close</mat-icon>
                      </mat-form-field>
                    </div>
                  <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('person')">
                      {{ isAllSelected('person') ? 'Deselect All' : 'Select All' }}
                  </button>
                  <mat-option *ngFor="let person of filteredPerson" (click)="$event.stopPropagation(); toggleSelection(person, 'person')">
                      <mat-checkbox [checked]="selectedPerson.includes(person)">{{ person }}</mat-checkbox>
                  </mat-option>
              </mat-menu>
          </div>
          </th>
          <th mat-button [matMenuTriggerFor]="companyMenu" [ngClass]="{'filter': selectedCompany.length > 0}">
            <div class="analysis-table-header">
              <h6>Company</h6>
              <mat-icon>filter_alt</mat-icon>
              <mat-menu #companyMenu="matMenu">
                  <div class="search-container p-1">
                      <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                          <input
                          matInput
                          placeholder="Search"
                          [(ngModel)]="companyKeySearch"
                          (input)="filterDistinctCompany()"
                        />
                        <mat-icon class="clear-icon"
                        matSuffix
                        (click)="clearSearch($event)">close</mat-icon>
                      </mat-form-field>
                    </div>
                  <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('company')">
                      {{ isAllSelected('company') ? 'Deselect All' : 'Select All' }}
                  </button>
                  <mat-option *ngFor="let company of filteredCompany" (click)="$event.stopPropagation(); toggleSelection(company, 'company')">
                      <mat-checkbox [checked]="selectedCompany.includes(company)">{{ company }}</mat-checkbox>
                  </mat-option>
              </mat-menu>
          </div>
          </th>
          <th mat-button [matMenuTriggerFor]="renumerationMenu" [ngClass]="{'filter': selectedRenumerationType.length > 0}">
            <div class="analysis-table-header">
              <h6>Renumeration Type</h6>
              <mat-icon>filter_alt</mat-icon>
              <mat-menu  #renumerationMenu="matMenu">
                  <div class="search-container p-1">
                      <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                          <input
                          matInput
                          placeholder="Search"
                          [(ngModel)]="renumerationKeySearch"
                          (input)="filterDistinctRenumerationType()"
                        />
                        <mat-icon class="clear-icon"
                        matSuffix
                        (click)="clearSearch($event)">close</mat-icon>
                      </mat-form-field>
                    </div>
                  <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('renumerationType')">
                      {{ isAllSelected('renumerationType') ? 'Deselect All' : 'Select All' }}
                  </button>
                  <mat-option *ngFor="let renumerationType of filteredRenumerationType" (click)="$event.stopPropagation(); toggleSelection(renumerationType, 'renumerationType')">
                      <mat-checkbox [checked]="selectedRenumerationType.includes(renumerationType)">{{ renumerationType }}</mat-checkbox>
                  </mat-option>
              </mat-menu>
          </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'currentManValue'}">
            <div class="analysis-table-header" (click)="sortData('currentManValue')" >
              <h6>ManValue</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'currentManValue' && sortState['currentManValue'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'currentManValue' && sortState['currentManValue'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'currentManValue' || !sortState['currentManValue']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'expectedVHr'}">
            
            <div class="analysis-table-header" (click)="sortData('expectedVHr')" >
              <h6>Expected vHR</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'expectedVHr' && sortState['expectedVHr'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'expectedVHr' && sortState['expectedVHr'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'expectedVHr' || !sortState['expectedVHr']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'expectedRemuneration'}">
            
            <div class="analysis-table-header" (click)="sortData('expectedRemuneration')" >
              <h6>Expected Remuneration</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'expectedRemuneration' && sortState['expectedRemuneration'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'expectedRemuneration' && sortState['expectedRemuneration'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'expectedRemuneration' || !sortState['expectedRemuneration']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'mHr'}">
            
            <div class="analysis-table-header" (click)="sortData('mHr')" >
              <h6>mHR</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'mHr' && sortState['mHr'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'mHr' && sortState['mHr'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'mHr' || !sortState['mHr']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'vHr'}">
            <div class="analysis-table-header" (click)="sortData('vHr')" >
              <h6>vHR</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'vHr' && sortState['vHr'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'vHr' && sortState['vHr'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'vHr' || !sortState['vHr']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'remuneration'}">
            <div class="analysis-table-header" (click)="sortData('remuneration')" >
              <h6>Remuneration</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'remuneration' && sortState['remuneration'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'remuneration' && sortState['remuneration'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'remuneration' || !sortState['remuneration']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-right-align" [ngClass]="{'sort': sortState['activeColumn'] === 'vHrDifferencePercentage'}">
            
            <div class="analysis-table-header" (click)="sortData('vHrDifferencePercentage')" >
              <h6>Difference</h6>
              <mat-icon *ngIf="sortState['activeColumn'] === 'vHrDifferencePercentage' && sortState['vHrDifferencePercentage'] === 'newFirst'">south</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] === 'vHrDifferencePercentage' && sortState['vHrDifferencePercentage'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'vHrDifferencePercentage' || !sortState['vHrDifferencePercentage']">import_export</mat-icon>
            </div>
          </th>
        </tr>
        <tr class="table-header-total">
          <th class="td-total">Total</th>
          <th></th>
          <th></th>
          <th></th>
          <th></th>
          <th class="td-right-align font-focused">{{total?.expectedVHr | number:'2.0-2'}}</th>
          <th class="td-right-align font-focused">{{total?.expectedRemuneration | currency:'INR':'symbol':'1.0'}}</th>
          <th class="td-right-align font-focused">{{total?.mHr | number:'2.0-2'}}</th>
          <th class="td-right-align font-focused">{{total?.vHr | number:'2.0-2'}}</th>
          <th class="td-right-align font-focused">{{total?.remuneration | currency:'INR':'symbol':'1.0'}}</th>
          <th class="td-right-align font-focused">{{total?.vHrDifferencePercentage | number:'2.0-1'}}%</th>
        </tr>
      </thead>
      <tbody class="table-body">
        <ng-container *ngFor="let item of dataList; let i = index">
          <tr>
            <td (click)="toggleExpand(i)" class="text-align-center">
              <mat-icon *ngIf="!isExpanded[i]">expand_more</mat-icon>
              <mat-icon *ngIf="isExpanded[i]">expand_less</mat-icon>
            </td>
            <td>{{item.person}}</td>
            <td>{{item.company}}</td>
            <td>{{item.renumerationType}}</td>
            <td class="td-right-align">{{item?.currentManValue}}</td>
            <td class="td-right-align">{{item?.expectedVHr | number:'2.0-2'}}</td>
            <td class="td-right-align">{{item?.expectedRemuneration | currency:'INR':'symbol':'1.0'}}</td>
            <td class="td-right-align">{{item?.mHr | number:'2.0-2'}}</td>
            <td class="td-right-align">{{item?.vHr | number:'2.0-2'}}</td>
            <td class="td-right-align">{{item?.remuneration | currency:'INR':'symbol':'1.0'}}</td>
            <td class="vhrDifference"
              [ngClass]="{'text-success': getDifferenceColor(item)=='green','text-warning': getDifferenceColor(item)=='yellow','text-danger': getDifferenceColor(item)=='red'}">
              {{item?.vHrDifferencePercentage | number:'2.0-1'}}%</td>
          </tr>
          <tr *ngIf="item && isExpanded[i]">
            <td class="mcv-vhr-analysis-list-item-div" colspan="11"  >
              <mcv-vhr-analysis-list-item [item]="item" [isMobileView]="isMobileView" [isExpanded]="isExpanded" [i]="i">
              </mcv-vhr-analysis-list-item>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>
<app-footer>
  <div class="nav-footer-actions">
    <div>
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>
   
    <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>

    
    <button mat-raised-button (click)="onExportExcel()" matTooltip="export excel" aria-label="export excel">Export
      Excel</button>
  </div>

  <div class="nav-footer-mobile-actions">
    <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>
  
    <button mat-raised-button (click)="onExportExcel()" matTooltip="export excel" aria-label="export excel">Export
      Excel</button>
  </div>

  <div class="nav-filters">
    <div class="inline-list">
      
          <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
       
        <mat-form-field appearance="outline">
          <mat-label>Select Range</mat-label>
          <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
            <input matStartDate placeholder="Start date" formControlName="start" readonly>
            <input matEndDate placeholder="End date" formControlName="end" readonly>
          </mat-date-range-input>
          <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
          <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
        </mat-form-field>
     

    
    </div>
  </div>
</app-footer>


<app-mcv-filter-sidenav #sidenav >
  <div>
    <mat-form-field appearance="outline">
      <mat-label>Select Range</mat-label>
      <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker2">
        <input matStartDate placeholder="Start date" formControlName="start" readonly>
        <input matEndDate placeholder="End date" formControlName="end" readonly>
      </mat-date-range-input>
      <mat-datepicker-toggle matSuffix [for]="rangePicker2"></mat-datepicker-toggle>
      <mat-date-range-picker [touchUi]="isMobileView" #rangePicker2></mat-date-range-picker>
    </mat-form-field>
  </div>

      </app-mcv-filter-sidenav>