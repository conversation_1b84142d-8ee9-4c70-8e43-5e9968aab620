<div class="cockpit-task-analysis-wrapper">
  <div class="row">
    <div class="col-md-3">
      <app-cockpit-banner *ngIf="expectedVhrData" [data]="expectedVhrData"></app-cockpit-banner>
    </div>
    <div class="col-md-3">
      <app-cockpit-banner *ngIf="amhrData" [data]="amhrData"></app-cockpit-banner>
    </div>
    <div class="col-md-3">
      <app-cockpit-banner *ngIf="vhrData" [data]="vhrData"></app-cockpit-banner>
    </div>

    <div class="col-md-3">
      <app-cockpit-banner *ngIf="remunerationData" [data]="remunerationData"></app-cockpit-banner>
    </div>
  </div>



  <div class="data-filter-row">
    <div class="filter-header"
      *ngIf="selectedAssignedTo.length > 0 || selectedTask.length > 0 || selectedTitle.length > 0">
      <h6 class="font-focused ">Filters:</h6>
    </div>
    <h6 *ngIf="selectedAssignedTo.length > 0">
      <b>Assigned To:</b> {{selectedAssignedTo.join(', ')}}
      <span class="clear-icon" (click)="clearSelection('assignedTo')">✖</span>
    </h6>
    <h6 *ngIf="selectedTask.length > 0">
      <b>Task:</b> {{selectedTask.join(', ')}}
      <span class="clear-icon" (click)="clearSelection('task')">✖</span>
    </h6>
    <h6 *ngIf="selectedTitle.length > 0">
      <b>Title:</b> {{selectedTitle.join(', ')}}
      <span class="clear-icon" (click)="clearSelection('title')">✖</span>
    </h6>
    <h6 (click)="resetFilter()"
      *ngIf="selectedAssignedTo.length > 0 || selectedTask.length > 0 || selectedTitle.length > 0">
      <b> Clear All</b>

    </h6>
  </div>

  <div class="mcv-task-analysis-table-container"
    [ngClass]="{'mcv-task-analysis-table-mobileview':isMobileView , 'filter-header-present':selectedAssignedTo.length > 0 || selectedTask.length > 0 || selectedTitle.length > 0}">
    <table>
      <thead>
        <!-- Table Header  -->
        <tr class="table-header">
          <!-- <td></td> -->
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'startDate'}">
            <div class="analysis-table-header" (click)="sortData('startDate')">
              <h6>Start</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'startDate' || !sortState['startDate']">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'dueDate'}">
            <div class="analysis-table-header" (click)="sortData('dueDate')">
              <h6>Due</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'dueDate' && sortState['dueDate'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'dueDate' && sortState['dueDate'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'dueDate' || !sortState['dueDate']">import_export</mat-icon>
            </div>
          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'completedDate'}">
            <div class="analysis-table-header" (click)="sortData('completedDate')">
              <h6>Completed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'completedDate' && sortState['completedDate'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'completedDate' && sortState['completedDate'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'completedDate' || !sortState['completedDate']">import_export</mat-icon>
            </div>
          </th>
          <th *ngIf="showAll" mat-button [matMenuTriggerFor]="assignedToMenu"
            [ngClass]="{'filter': selectedAssignedTo.length > 0}">

            <div class="analysis-table-header">
              <h6>Assigned To</h6>
              <mat-icon>filter_alt</mat-icon>

              <mat-menu #assignedToMenu="matMenu">
                <div class="search-container p-1">
                  <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                    <input matInput placeholder="Search" [(ngModel)]="assignedToSearch"
                      (input)="filterDistinctAssignedTo()" />
                    <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                  </mat-form-field>
                </div>
                <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('assignedTo')">
                  {{ isAllSelected('assignedTo') ? 'Deselect All' : 'Select All' }}
                </button>
                <mat-option *ngFor="let person of filteredAssignedTo"
                  (click)="$event.stopPropagation(); toggleSelection(person, 'assignedTo')">
                  <mat-checkbox [checked]="selectedAssignedTo.includes(person)">{{ person }}</mat-checkbox>
                </mat-option>
              </mat-menu>
            </div>

          </th>
          <th [ngClass]="{'sort': sortState['activeColumn'] === 'delay'}">
            <div class="analysis-table-header" (click)="sortData('delay')">
              <h6>Delay</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'delay' && sortState['delay'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'delay' && sortState['delay'] === 'oldFirst'">north</mat-icon>
              <mat-icon *ngIf="sortState['activeColumn'] !== 'delay' || !sortState['delay']">import_export</mat-icon>
            </div>
          </th>
          <th mat-button [matMenuTriggerFor]="taskMenu" [ngClass]="{'filter': selectedTask.length > 0}">
            <div class="analysis-table-header">
              <h6>Task</h6>
              <mat-icon>filter_alt</mat-icon>
              <mat-menu #taskMenu="matMenu">
                <div class="search-container p-1">
                  <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                    <input matInput placeholder="Search" [(ngModel)]="assignedtaskSearch"
                      (input)="filterDistinctTask()" />
                    <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                  </mat-form-field>
                </div>
                <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('task')">
                  {{ isAllSelected('task') ? 'Deselect All' : 'Select All' }}
                </button>
                <mat-option *ngFor="let task of filteredTask"
                  (click)="$event.stopPropagation(); toggleSelection(task, 'task')">
                  <mat-checkbox [checked]="selectedTask.includes(task)">{{ task }}</mat-checkbox>
                </mat-option>
              </mat-menu>
            </div>
          </th>
          <th mat-button [matMenuTriggerFor]="titleMenu" [ngClass]="{'filter': selectedTitle.length > 0}">

            <div class="analysis-table-header">
              <h6>Title</h6>
              <mat-icon>filter_alt</mat-icon>
              <mat-menu #titleMenu="matMenu">
                <div class="search-container p-1">
                  <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                    <input matInput placeholder="Search" [(ngModel)]="assignedtitleSearch"
                      (input)="filterDistinctTitle()" />
                    <mat-icon class="clear-icon" matSuffix (click)="clearSearch($event)">close</mat-icon>
                  </mat-form-field>
                </div>
                <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('title')">
                  {{ isAllSelected('title') ? 'Deselect All' : 'Select All' }}
                </button>
                <mat-option *ngFor="let person of filteredTitle"
                  (click)="$event.stopPropagation(); toggleSelection(person, 'title')">
                  <mat-checkbox [checked]="selectedTitle.includes(person)">{{ person }}</mat-checkbox>
                </mat-option>
              </mat-menu>
            </div>
          </th>
          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'mHrAssigned'}">

            <div class="analysis-table-header" (click)="sortData('mHrAssigned')">
              <h6>mHR Assigned</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrAssigned' && sortState['mHrAssigned'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrAssigned' && sortState['mHrAssigned'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'mHrAssigned' || !sortState['mHrAssigned']">import_export</mat-icon>
            </div>

          </th>
          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'mHrConsumed'}">
            <div class="analysis-table-header" (click)="sortData('mHrConsumed')">
              <h6>mHR Consumed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrConsumed' && sortState['mHrConsumed'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrConsumed' && sortState['mHrConsumed'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'mHrConsumed' || !sortState['mHrConsumed']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'assessmentPoints'}">

            <div class="analysis-table-header" (click)="sortData('assessmentPoints')">
              <h6>Points</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'assessmentPoints' && sortState['assessmentPoints'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'assessmentPoints' && sortState['assessmentPoints'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'assessmentPoints' || !sortState['assessmentPoints']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'mHrAssessed'}">
            <div class="analysis-table-header" (click)="sortData('mHrAssessed')">
              <h6> mHR Assessed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrAssessed' && sortState['mHrAssessed'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'mHrAssessed' && sortState['mHrAssessed'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'mHrAssessed' || !sortState['mHrAssessed']">import_export</mat-icon>
            </div>
          </th>

          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'vHrConsumed'}">
            <div class="analysis-table-header" (click)="sortData('vHrConsumed')">
              <h6> vHR Consumed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrConsumed' && sortState['vHrConsumed'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrConsumed' && sortState['vHrConsumed'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHrConsumed' || !sortState['vHrConsumed']">import_export</mat-icon>
            </div>
          </th>
          <th class="td-align-right" [ngClass]="{'sort': sortState['activeColumn'] === 'vHrAssessed'}">
            <div class="analysis-table-header" (click)="sortData('vHrAssessed')">
              <h6>vHR Assessed</h6>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrAssessed' && sortState['vHrAssessed'] === 'newFirst'">south</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] === 'vHrAssessed' && sortState['vHrAssessed'] === 'oldFirst'">north</mat-icon>
              <mat-icon
                *ngIf="sortState['activeColumn'] !== 'vHrAssessed' || !sortState['vHrAssessed']">import_export</mat-icon>
            </div>
          </th>
          <th>Assessment</th>
        </tr>
        <!-- Table Header Total  -->
        <tr class="total">
          <!-- <td></td> -->
          <th class="pl-2">Total</th>
          <th></th>
          <th></th>
          <th *ngIf="showAll"></th>
          <th><span [ngClass]="{'text-muted': totalDelay < 0,'text-red': totalDelay > 0}"
              *ngIf="totalDelay > 0">+</span>
            <span [ngClass]="{'text-muted': totalDelay < 0,'text-red': totalDelay > 0}">
              {{ totalDelay | number: "2.0-2" }}</span>
          </th>
          <th></th>
          <th></th>
          <th class="td-align-right font-focused pr-2">{{ totalMHrAssigned | number: "2.0-2" }}</th>
          <th class="td-align-right font-focused pr-2">{{ totalMHrConsumed | number: "2.0-2" }}</th>
          <th></th>
          <th class="td-align-right font-focused pr-2">{{ totalMHrAssessed | number: "2.0-2" }}</th>
          <th class="td-align-right font-focused pr-2">{{ totalVHrCosumed | number: "2.0-2" }}</th>
          <th class="td-align-right font-focused pr-2">{{ totalVHrAssessed | number: "2.0-2" }}</th>
          <th></th>

        </tr>
      </thead>
      <!-- Table Body  -->
      <tbody class="table-body">
        <ng-container *ngFor="let item of dataList; let i=index">
          <tr>
            <!-- <td class="td-1" *ngIf="!item.assessmentRemark && item.assessments.length == 0"></td>
            <td class="td-2" *ngIf="item.assessmentRemark || item.assessments.length > 0" (click)="toggleExpand(i)">
              <div class="d-flex align-items-center">
                <mat-icon *ngIf="!isExpanded[i]">expand_more</mat-icon>
                <mat-icon matTooltip="MHr Requests" class="notification" *ngIf="!isExpanded[i] && item.requests && item.requests.length > 0">notifications_active</mat-icon>
                <mat-icon *ngIf="isExpanded[i]">expand_less</mat-icon>
              </div>
            </td> -->
            <td class="align-right text-nowrap">{{item.startDate | date:'dd MMM y HH:mm'}}
            </td>
            <td class="align-right text-nowrap">{{item.dueDate | date:'dd MMM y HH:mm'}}</td>
            <td class="align-right text-nowrap">{{item.completedDate | date:'dd MMM y HH:mm'}}</td>
            <td *ngIf="showAll" class="align-left">{{item.person}}</td>
            <td class="align-right">{{item.delay | number:'2.0-2'}}</td>
            <td class="align-left">{{item.taskTitle}}</td>
            <td class="align-left ">{{item.entityTitle}}</td>
            <td class="align-right">{{item.mHrAssigned | number:'2.0-2'}}</td>
            <td class="align-right">{{item.mHrConsumed | number:'2.0-2'}}</td>
            <td class="align-center">
              <span *ngIf="item.isAssessmentApplicable">{{item.assessmentPoints}}<small>/10</small></span>
              <span *ngIf="!item.isAssessmentApplicable">N/A</span>
            </td>
            <td class="align-right">{{item.mHrAssessed | number:'2.0-2'}}</td>
            <td class="align-right">{{item.vHrConsumed | number:'2.0-2'}}</td>
            <td class="align-right">{{item.vHrAssessed | number:'2.0-2'}}</td>
            <td>
              <mat-icon *ngIf="item.taskTitle == 'Studio Work' || item.taskTitle == 'Todo Work'"
                (click)="onShowTaskDetails(item)">info</mat-icon>
            </td>
          </tr>
          <tr class="analysis-list-item-tr" *ngIf="item.assessments.length > 0 && isExpanded[i]">
            <td class="app-cockpit-task-analysis-list-item-div" [attr.colspan]="showAll ? '14' : '13'">
              <app-cockpit-task-analysis-list-item [item]="item" [isMobileView]="isMobileView" [showAll]="showAll"
                [isExpanded]="isExpanded" [i]="i">
              </app-cockpit-task-analysis-list-item>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
  <ul *ngIf="!isLoading && dataList.length == 0">
    <li>
      <div class="mcv-data-list-cell mcv-data-list-cell-align-center">
        <span>No items found</span>
      </div>
    </li>
  </ul>
</div>

<app-footer>
  <div class="nav-footer-actions">
    <div>
      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>

    <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>




    <button type="button" mat-raised-button [color]="showAll ? 'accent' : ''" (click)="onToggleShowAll()"
      *ngIf="isPermissionShowAll" matTooltip="Show All" aria-label="Show All">
      Show All
    </button>
    <button mat-raised-button type="button" (click)="onExportExcel()" matTooltip="export excel"
      aria-label="export excel" *ngIf="isPermissionExcel">
      Export Excel
    </button>
  </div>

  <div class="nav-footer-mobile-actions">
    <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
      <mat-icon>filter_list</mat-icon>
    </button>
    <button type="button" mat-raised-button [color]="'accent'" (click)="onToggleShowAll()" *ngIf="isPermissionShowAll"
      matTooltip="Show All" aria-label="Show All">
      Show All
    </button>
  </div>
  <div class="nav-filters">
    <div class="inline-list">

      <mat-form-field appearance="outline">
        <mat-label>Search</mat-label>
        <input placeholder="Enter text to search" aria-label="Search" matInput [formControl]="searchFilter" />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>


      <mat-form-field appearance="outline">
        <mat-label>Select Range</mat-label>
        <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker1">
          <input matStartDate placeholder="Start date" formControlName="start" readonly />
          <input matEndDate placeholder="End date" formControlName="end" readonly />
        </mat-date-range-input>
        <mat-datepicker-toggle matSuffix [for]="rangePicker1"></mat-datepicker-toggle>
        <mat-date-range-picker [touchUi]="isMobileView" #rangePicker1></mat-date-range-picker>
      </mat-form-field>


    </div>
  </div>

</app-footer>

<app-mcv-filter-sidenav #sidenav>
  <div>
    <mat-form-field appearance="outline">
      <mat-label>Select Range</mat-label>
      <mat-date-range-input [formGroup]="dateFilters" [rangePicker]="rangePicker2">
        <input matStartDate placeholder="Start date" formControlName="start" readonly />
        <input matEndDate placeholder="End date" formControlName="end" readonly />
      </mat-date-range-input>
      <mat-datepicker-toggle matSuffix [for]="rangePicker2"></mat-datepicker-toggle>
      <mat-date-range-picker [touchUi]="isMobileView" #rangePicker2></mat-date-range-picker>
    </mat-form-field>
  </div>


</app-mcv-filter-sidenav>