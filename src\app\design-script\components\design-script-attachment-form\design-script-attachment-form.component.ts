import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DesignScriptEntity } from './../../models/design-script-entity.model';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormBuilder, AbstractControl, FormArray, Validators, FormControl, ReactiveFormsModule } from '@angular/forms';
import { McvTagUtilityService } from 'src/app/services/mcv-tag-utility.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { DesignScriptDataCard, DesignScriptDataCardAttachment, DesignScriptDataCardAttribute } from '../../models/design-script-data-card.model';

import { DesignScriptApiService } from '../../services/design-script-api.service';
import { DesignScriptDataCardApiService } from '../../services/design-script-data-card-api.service';
import { LibraryEntityApiService } from 'src/app/library/service/library-entity-api.service';
import { DesignScriptDataCardAttributeApiService } from '../../services/design-script-data-card-attribute-api.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { TextFieldModule } from '@angular/cdk/text-field';
import { McvBaseSearchTagEditorComponent } from '../../../mcv-core/components/mcv-base-search-tag-editor/mcv-base-search-tag-editor.component';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgFor, NgIf, NgClass } from '@angular/common';

@Component({
    selector: 'design-script-attachment-form',
    templateUrl: './design-script-attachment-form.component.html',
    styleUrls: ['./design-script-attachment-form.component.scss'],
    standalone: true,
    imports: [NgFor, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule, McvBaseSearchTagEditorComponent, TextFieldModule, NgIf, NgClass, MatButtonModule, MatTooltipModule]
})
export class DesignScriptAttachmentFormComponent implements OnInit {

  item!: DesignScriptDataCard;
  isFullScreen: boolean = false;
  @Input() isReadOnly: boolean = false;
  @Input() entityItem!: DesignScriptEntity;
  @Input('item') set itemValue(value: DesignScriptDataCard) {
    if (value) {
      this.item = value;
      // console.log(this.item);
      this.refresh();
    }
  }
  @Input() entityID!: number;

  form!: FormGroup;
  searchTagOptions: string[] = [];

  //Attribute FormArray
  get f(): any { return this.form.controls; }
  get categoryOptions() { return this.entityService.categoryOptions; }
  get TYPEFLAG_ZONE() { return this.entityService.TYPEFLAG_ZONE; }
  get attributes(): FormArray { return this.f['attributes'] as FormArray; }
  get tagOptions(): string[] { return this.entityService.tagOptions; }
  get isMobileView(): boolean { return this.utilityService.isMobileView; }
  get designScriptEntityList() { return this.entityService.entityList; }
  get IsPermissionEdit(): boolean { return this.entityService.isPermissionEdit; }

  @Output() cancel = new EventEmitter<any>();
  @Output() update = new EventEmitter<DesignScriptDataCard>();
  @Output() delete = new EventEmitter<DesignScriptDataCard>();
  @Output() restore = new EventEmitter<DesignScriptDataCard>();
  @Output() unlink = new EventEmitter<DesignScriptDataCard>();


  constructor(
    private formBuilder: FormBuilder,
    private utilityService: UtilityService,
    private entityService: DesignScriptApiService,
    private tagUtility: McvTagUtilityService,
    private designScriptDataCardService: DesignScriptDataCardApiService,
    private libraryEntityService: LibraryEntityApiService,
    private designScriptDataCardAttributeService: DesignScriptDataCardAttributeApiService
  ) { }

  ngOnInit(): void {
    if (!this.form) {
      this.buildForm();
    }
    this.searchTagOptions = this.entityService.tagOptions;
  }

  refresh() {
    if (!this.form) {
      this.buildForm();
    }
    this.bindForm();
  }

  private buildForm() {
    this.form = this.formBuilder.group({
      description: new FormControl<any>(null),
      gfcTag: new FormControl<any>(null),
      attributes: this.formBuilder.array([]),
    });

    this.changeToUpperCase();

    this.touchForm();
    if (this.item.isVersion) {
      this.f['attributes'].disable();
      console.log('Form Disabled')
    } else {
      this.f['attributes'].reset();
    }
  }

  private bindForm() {
    this.f['description'].setValue(this.item.description);
    this.f['gfcTag'].setValue(this.item.gfcTag);

    //For Attribute Value
    this.attributes.clear();
    if (this.item.attributes) {
      this.item.attributes.map(x => {
        return { attributeKey: x.attributeKey, attributeValue: x.attributeValue }
      }).forEach((x, i) => {
        this.addAttribute();
        this.attributes.controls[i].setValue(x);
      });
    } else {
      this.addAttribute();
    }
  }

  private touchForm() {
    if (this.form) {
      Object.keys(this.form.controls).forEach(field => {
        const control = this.form.get(field);
        if (control)
          control.markAsTouched({ onlySelf: true });
      });
    }
  }


  getErrorMessage(control: AbstractControl) {
    return this.utilityService.getErrorMessage(control);
  }

  onSubmit() {
    this.item.description = this.f['description'].value;
    this.item.attributes = this.f['attributes'].value;
    this.item.gfcTag = this.f['gfcTag'].value;
    this.designScriptDataCardService.update(this.item).subscribe((data) => {
      if (data) {
        this.item = data;
        this.update.emit(this.item);
      }
    });
  }

  onCancel() {
    this.cancel.emit();
  }

  onTagsUpdate(tags: string[]) {
    if (tags) {
      this.item.searchTags = tags;
      this.entityService.addToTagOptions(tags);
      this.item.searchTags = tags;
      this.designScriptDataCardService.update(this.item).subscribe((data) => {
        // this.update.emit(new DesignScriptDataCard(data));
      });
    }
  }

  onToggleTag(tagList: string[], tag: string, multiple: boolean = false) {
    this.tagUtility.onToggleTag(tagList, tag, multiple);
  }

  isTagSelected(tagList: string[], tag: string): boolean {
    return this.tagUtility.isTagSelected(tagList, tag);
  }

  getSelectedCategoryClass(category: any): string {
    if (this.item.category.includes(category.label)) {
      return category.class;
    }
    return '';
  }

  onClickDelete() {
    const _messageText =
      'Delete Attachment !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.item.isDeleted = true;
        this.designScriptDataCardService.update(this.item).subscribe(data => {
          this.delete.emit(data);
        });
      });
  }

  onClickRestore() {
    const _messageText =
      'Restore !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.item.isDeleted = false;
        this.designScriptDataCardService.update(this.item).subscribe(data => {
          this.restore.emit(this.item);
        });
      });
  }

  addAttribute() {
    const attributeForm = this.formBuilder.group(
      {
        attributeKey: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(50)]],
        attributeValue: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(256)]],
      }
    );
    this.attributes.push(attributeForm);
  }

  removeAttribute(index: number) {
    this.attributes.removeAt(index);
  }

  onAttributeInputChange(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control && _control.value) {
      _control.setValue(_control.value.toUpperCase());
    }
  }
  getAttributeFormControl(formArrayIndex: number, controlName: string) {
    if (this.attributes) {
      const _formGroup = <FormGroup>this.attributes.controls[formArrayIndex];
      return <FormControl>_formGroup.controls[controlName];
    } return new FormControl();
  }

  isInputInvalid(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control) {
      return _control.invalid;
    } else {
      return false;
    }
  }

  onUnlinkDataCard(entityID: number, dataCard: DesignScriptDataCard) {
    // console.log(entityID, dataCard);
    // this.designScriptDataCardService.unLinkFromEntity(entityID, dataCard).subscribe((result) => {
    //   // this.utilityService.showSwalToast(
    //   //   "Success!",
    //   //   "Unlinked Successfull.",
    //   //   );
    //   this.unlinkChildrenDataCard(this.entityID, this.item);
    //   Object.assign(this.item, { unLink: true });
    //   this.update.emit(this.item);
    //   // this.designScriptDataCardService.unlinkDataCard = this.item;
    // });

    // console.log(entityID,dataCard);
    this.designScriptDataCardService.unLinkFromEntity(entityID, dataCard).subscribe((result) => {
      const _children = this.designScriptEntityList.filter(x => x.parentID == entityID);
      if (_children && _children.length > 0) {
        let _deleteFrom: any[] = _children.filter(x => x.dataCards.some(y => y.id == dataCard.id));
        console.log(_deleteFrom);
        if (_deleteFrom.length > 0) {
          _deleteFrom.forEach(z => {
            this.onUnlinkDataCard(z.id, dataCard);
          });
          // if(_deleteFrom.length == 0){
          this.update.emit(dataCard);
          // }
          _deleteFrom = [];
        }
      }
    });
  }


  onToggleFullscreen() {
    this.isFullScreen = !this.isFullScreen;
  }

  getFileExtension(filename: string) { return this.utilityService.getFileExtension(filename); }
  getFileMediaType(filename: string) { return this.utilityService.getFileMediaType(filename); }

  getMasterCategory(category: string) {
    return this.categoryOptions.find(x => x.label == category);
  }

  changeToUpperCase() {
    this.f['gfcTag'].valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),
    ).subscribe((val: any) => {
      if (val) {
        this.f['gfcTag'].setValue(val.toUpperCase(), { emitEvent: false });
      }
    });
  }

  onPreview(image: DesignScriptDataCardAttachment) {
    const _data = {
      title: image.filename,
      urls: null,
      filename: image.filename,
      activeUrl: image.thumbUrl || image.url,
      mediaType: 'image',
      contentType: image.contentType,
      // mediaCaption: this.mediaCaption
    };
    this.libraryEntityService.openLibraryLightBox(this.item.attachments, _data);
  }

  async toggleHide(item: DesignScriptDataCardAttribute) {
    item.isHidden = !item.isHidden;
    item = await (this.designScriptDataCardAttributeService.update(item, true).toPromise());
  }
}
