
export class VHr {
  contactID!: number;
  person!: string;
  currentManValue: number = 1;
  currentVHrRate: number = 1;
  expectedMHr: number = 0;
  expectedVHr: number = 0;
  expectedRemuneration: number = 0;
  vHrDifferencePercentage: number = 0;
  earnedInSelfPackages?: WFTaskVHr;
  recordedInOthersPackages?: WFTaskVHr;
  recordedForMeetings?: WFTaskVHr;
    recordedForInspections?: WFTaskVHr;
  recordedInProjectTodo?: WFTaskVHr;
  recordedInTodo?: WFTaskVHr;
  recordedForInquiries?: WFTaskVHr;
  recordedForOtherTasks?: WFTaskVHr;
  mHr: number = 0;
  vHr: number = 0;
  remuneration: number = 0;
  company!: string;
  renumerationType!: string;
}

export class WFTaskVHr {
  contactID!: number;
  person!: string;
  entity!: string;
  mHrAssessed: number = 0;
  vHrAssessed: number = 0;
  vHrAssessedCost: number = 0;
}
