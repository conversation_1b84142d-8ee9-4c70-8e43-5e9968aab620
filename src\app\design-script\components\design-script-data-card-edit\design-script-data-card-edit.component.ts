import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormArray, FormBuilder, AbstractControl, Validators, FormControl, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { LibraryEntityApiService } from 'src/app/library/service/library-entity-api.service';
import { McvTagUtilityService } from 'src/app/services/mcv-tag-utility.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { DesignScriptDataCard, DesignScriptDataCardAttachment, DesignScriptDataCardAttribute } from '../../models/design-script-data-card.model';
import { DesignScriptEntity } from '../../models/design-script-entity.model';
import { DesignScriptApiService } from '../../services/design-script-api.service';
import { DesignScriptDataCardApiService } from '../../services/design-script-data-card-api.service';
import { DesignScriptDataCardAttributeApiService } from '../../services/design-script-data-card-attribute-api.service';
import { MAT_DIALOG_DATA, MatDialogConfig, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { firstValueFrom, forkJoin } from 'rxjs';
import { DesignScriptDataCardAttachmentApiService } from '../../services/design-script-data-card-attachment-api.service';
import { DesignScriptDataCardReplaceDialogComponent } from '../design-script-data-card-replace-dialog/design-script-data-card-replace-dialog.component';
import { TextFieldModule } from '@angular/cdk/text-field';
import { McvBaseSearchTagEditorComponent } from '../../../mcv-core/components/mcv-base-search-tag-editor/mcv-base-search-tag-editor.component';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { NgIf, NgFor, NgClass } from '@angular/common';

@Component({
  selector: 'app-design-script-data-card-edit',
  templateUrl: './design-script-data-card-edit.component.html',
  styleUrls: ['./design-script-data-card-edit.component.scss'],
  standalone: true,
  imports: [NgIf, MatButtonModule, MatTooltipModule, MatIconModule, MatDialogModule, NgFor, NgClass, ReactiveFormsModule, MatFormFieldModule, MatInputModule, McvBaseSearchTagEditorComponent, TextFieldModule]
})
export class DesignScriptDataCardEditComponent implements OnInit {

  dataCard: DesignScriptDataCard;
  isFullScreen: boolean = false;
  dialogTitle: string;
  designScriptEntity: DesignScriptEntity;
  isToggleHidden: boolean = false;
  isAttachmentToggleHidden: boolean = false;
  constructor(@Inject(MAT_DIALOG_DATA) dialogData: any,
    private dialogRef: MatDialogRef<DesignScriptDataCardEditComponent>,
    private entityService: DesignScriptApiService,
    private designScriptDataCardService: DesignScriptDataCardApiService,
    private formBuilder: FormBuilder,
    private utilityService: UtilityService,
    private tagUtility: McvTagUtilityService,
    private libraryEntityService: LibraryEntityApiService,
    private designScriptDataCardAttributeService: DesignScriptDataCardAttributeApiService,
    private designScriptDataCardAttachmentService: DesignScriptDataCardAttachmentApiService
  ) {
    this.dialogTitle = dialogData.dialogTitle;
    this.dataCard = dialogData.config.designScriptDataCard;
    this.designScriptEntity = dialogData.config.scriptEntity;
    if (this.dataCard) {
      this.refresh();
    }
  }

  form!: FormGroup;
  searchTagOptions: string[] = [];

  //Attribute FormArray
  get f(): any { return this.form.controls; }
  get categoryOptions() { return this.entityService.categoryOptions; }
  get TYPEFLAG_ZONE() { return this.entityService.TYPEFLAG_ZONE; }
  get attributes(): FormArray { return this.f['attributes'] as FormArray; }
  get tagOptions(): string[] { return this.entityService.tagOptions; }
  get isMobileView(): boolean { return this.utilityService.isMobileView; }
  get designScriptEntityList() { return this.entityService.entityList; }
  get IsPermissionEdit(): boolean { return this.entityService.isPermissionEdit; }

  @Output() cancel = new EventEmitter<any>();
  @Output() update = new EventEmitter<DesignScriptDataCard>();
  @Output() delete = new EventEmitter<DesignScriptDataCard>();
  @Output() restore = new EventEmitter<DesignScriptDataCard>();
  @Output() unlink = new EventEmitter<DesignScriptDataCard>();

  ngOnInit(): void {
    if (!this.form) {
      this.buildForm();
    }
    this.searchTagOptions = this.entityService.tagOptions;
    this.setToggleStatus()
  }

  refresh() {
    if (!this.form) {
      this.buildForm();
    }
    this.bindForm();
  }

  private buildForm() {
    this.form = this.formBuilder.group({
      description: new FormControl<string>(''),
      gfcTag: new FormControl<string>(''),
      attributes: this.formBuilder.array([]),
    });

    // this.changeToUpperCase();

    this.touchForm();
    if (this.dataCard.isVersion) {
      this.f['attributes'].disable();
      console.log('Form Disabled')
    } else {
      this.f['attributes'].reset();
    }

    // this.f['description'].valueChanges.pipe(
    //   debounceTime(400),
    //   distinctUntilChanged()
    // ).subscribe((res: any) => {
    //   this.dataCard.description = res;
    //   this.designScriptDataCardService.update(this.dataCard, true).subscribe((data) => {
    //     if (data) {
    //       this.dataCard = data;
    //     }
    //   });
    // });

    // this.f['gfcTag'].valueChanges.pipe(
    //   debounceTime(400),
    //   distinctUntilChanged()
    // ).subscribe((res: any) => {
    //   this.dataCard.gfcTag = res;
    //   this.designScriptDataCardService.update(this.dataCard, true).subscribe((data) => {
    //     if (data) {
    //       this.dataCard = data;
    //     }
    //   });
    // });
  }

  private bindForm() {
    this.f['description'].setValue(this.dataCard.description);
    this.f['gfcTag'].setValue(this.dataCard.gfcTag);

    //For Attribute Value
    this.attributes.clear();
    if (this.dataCard.attributes) {
      this.dataCard.attributes.map(x => {
        return { attributeKey: x.attributeKey, attributeValue: x.attributeValue }
      }).forEach((x, i) => {
        this.addAttribute();
        this.attributes.controls[i].setValue(x);
      });
    } else {
      this.addAttribute();
    }
  }

  private touchForm() {
    if (this.form) {
      Object.keys(this.form.controls).forEach(field => {
        const control = this.form.get(field);
        if (control)
          control.markAsTouched({ onlySelf: true });
      });
    }
  }


  getErrorMessage(control: AbstractControl) {
    return this.utilityService.getErrorMessage(control);
  }

  onSubmit() {
    this.dataCard.description = this.f['description'].value;
    this.dataCard.attributes = this.f['attributes'].value;
    this.dataCard.gfcTag = this.f['gfcTag'].value;
    this.designScriptDataCardService.update(this.dataCard).subscribe((data) => {
      if (data) {
        this.dataCard = data;
        this.update.emit(this.dataCard);
      }
    });
  }

  onCancel() {
    this.cancel.emit();
  }

  onTagsUpdate(tags: string[]) {
    if (tags) {
      this.dataCard.searchTags = tags;
      this.entityService.addToTagOptions(tags);
      this.dataCard.searchTags = tags;
      this.designScriptDataCardService.update(this.dataCard).subscribe((data) => {
        // this.update.emit(new DesignScriptDataCard(data));
      });
    }
  }

  onToggleTag(tagList: string[], tag: string, multiple: boolean = false) {
    this.tagUtility.onToggleTag(tagList, tag, multiple);
  }

  isTagSelected(tagList: string[], tag: string): boolean {
    return this.tagUtility.isTagSelected(tagList, tag);
  }

  getSelectedCategoryClass(category: any): string {
    if (this.dataCard.category.includes(category.label)) {
      return category.class;
    }
    return '';
  }

  onClickDelete() {
    const _messageText =
      'Delete Attachment !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.dataCard.isDeleted = true;
        this.designScriptDataCardService.update(this.dataCard).subscribe(data => {
          this.delete.emit(data);
        });
      });
  }

  onClickRestore() {
    const _messageText =
      'Restore !';

    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.dataCard.isDeleted = false;
        this.designScriptDataCardService.update(this.dataCard).subscribe(data => {
          this.restore.emit(this.dataCard);
        });
      });
  }

  addAttribute() {
    const attributeForm = this.formBuilder.group(
      {
        attributeKey: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(50)]],
        attributeValue: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(256)]],
      }
    );
    this.attributes.push(attributeForm);
  }

  removeAttribute(index: number) {
    this.attributes.removeAt(index);
  }

  onAttributeInputChange(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control && _control.value) {
      _control.setValue(_control.value.toUpperCase());
    }
  }
  getAttributeFormControl(formArrayIndex: number, controlName: string) {
    if (this.attributes) {
      const _formGroup = <FormGroup>this.attributes.controls[formArrayIndex];
      return <FormControl>_formGroup.controls[controlName];
    } return new FormControl();
  }

  isInputInvalid(formArrayIndex: number, controlName: string) {
    var _control = this.getAttributeFormControl(formArrayIndex, controlName);
    if (_control) {
      return _control.invalid;
    } else {
      return false;
    }
  }

  onUnlinkDataCard() {
    this.utilityService.showConfirmationDialog(`Remove ${this.dataCard.title}?`, async () => {
      this.designScriptDataCardService.unLinkFromEntity(this.designScriptEntity.id, this.dataCard).toPromise();
      this.onUnlinkChildrens(this.designScriptEntity.id, this.dataCard);
      this.entityService.unlinkDataCard.push({ entity: this.designScriptEntity, dataCard: this.dataCard });
    });
  }

  async onUnlinkChildrens(entityID: number, dataCard: DesignScriptDataCard) {
    const childrens = this.entityService.entityList.filter(x => x.parentID == entityID);
    if (childrens.length) {
      var requests: any[] = [];
      childrens.forEach(x => {
        if (x.dataCards.length && x.dataCards.find(z => z.id == dataCard.id)) {
          requests.push(this.designScriptDataCardService.unLinkFromEntity(x.id, dataCard));
        }
        requests.push(this.onUnlinkChildrens(x.id, dataCard));
      });
      await forkJoin(requests).toPromise();
    }
    this.dialogRef.close(dataCard);
  }

  onToggleFullscreen() {
    this.isFullScreen = !this.isFullScreen;
  }

  getFileExtension(filename: string) { return this.utilityService.getFileExtension(filename); }
  getFileMediaType(filename: string) { return this.utilityService.getFileMediaType(filename); }

  getMasterCategory(category: string) {
    return this.categoryOptions.find(x => x.label == category);
  }

  // changeToUpperCase() {
  //   this.f['gfcTag'].valueChanges.pipe(
  //     debounceTime(400),
  //     distinctUntilChanged(),
  //   ).subscribe(val => {
  //     if (val) {
  //       this.f['gfcTag'].setValue(val.toUpperCase(), { emitEvent: false });
  //     }
  //   });
  // }

  onPreview(image: DesignScriptDataCardAttachment) {
    const _data = {
      title: image.filename,
      urls: null,
      filename: image.filename,
      activeUrl: image.thumbUrl || image.url,
      mediaType: 'image',
      contentType: image.contentType,
      // mediaCaption: this.mediaCaption
    };
    this.libraryEntityService.openLibraryLightBox(this.dataCard.attachments, _data);
  }

  async toggleHide(item: DesignScriptDataCardAttribute) {
    item.isHidden = !item.isHidden;
    item = await (this.designScriptDataCardAttributeService.update(item, true).toPromise());
  }

  async toggleHideAttachment(item: DesignScriptDataCardAttachment) {
    item.isHidden = !item.isHidden;
    item = await (this.designScriptDataCardAttachmentService.update(item, true).toPromise());
  }

  onClose() {
    this.dialogRef.close(this.dataCard);
  }

  async onToggleDataCard() {
    this.isToggleHidden = !this.isToggleHidden;
    this.dataCard.attributes.forEach(x => x.isHidden = this.isToggleHidden);
    let _request = this.dataCard.attributes.map(x =>
      this.designScriptDataCardAttributeService.update(x)
    );
    await forkJoin(_request).toPromise();
  }

  async onReplaceDataCard() {
    const _dialogConfig = new MatDialogConfig();
    _dialogConfig.disableClose = true;
    _dialogConfig.autoFocus = true;
    _dialogConfig.data = {
      dataCard: this.dataCard
    }

    const _dialogRef = this.entityService.openDialog(DesignScriptDataCardReplaceDialogComponent, _dialogConfig, true);
    _dialogRef.afterClosed().subscribe(res => {
      if (res) {
        console.log(res);
        this.dataCard = Object.assign(this.dataCard, res);
        this.dialogRef.close(this.dataCard);
      }
    });
  }

  async onCellChange(formControlName: string) {
    const _control = this.form.get(formControlName);
    if (_control) {
      if (formControlName == 'gfcTag') {
        this.f['gfcTag'].setValue(_control.value);
        this.dataCard.gfcTag = _control.value;
      } else if (formControlName == 'description') {
        this.f['description'].setValue(_control.value);
        this.dataCard.description = _control.value;
      }

      const _updated = await firstValueFrom(this.designScriptDataCardService.update(this.dataCard));
      if (_updated) {
        this.utilityService.showSwalToast('DataCard Updated', 'Updated Successfully!', 'success');
        this.dataCard = Object.assign(this.dataCard, _updated);
      }
      console.log(_control.value);
    }
  }

  async onToggleImagePreview() {
    this.isAttachmentToggleHidden = !this.isAttachmentToggleHidden;
  
    const updateRequests = this.dataCard.attachments.map((element: any) => {
      element.isHidden = this.isAttachmentToggleHidden;
      return this.designScriptDataCardAttachmentService.update(element, true);
    });
  
    const updatedAttachments = await firstValueFrom(forkJoin(updateRequests));
    this.dataCard.attachments = updatedAttachments;
  }

setToggleStatus(){
  this.isAttachmentToggleHidden = this.dataCard.attachments.every(element => element.isHidden);
  this.isToggleHidden = this.dataCard.attributes.every(element => element.isHidden);
}
}
