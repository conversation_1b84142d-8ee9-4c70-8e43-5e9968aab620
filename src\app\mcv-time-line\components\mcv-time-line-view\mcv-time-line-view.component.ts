import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  effect,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import {
  CdkFixedSizeVirtualScroll,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
} from '@angular/cdk/scrolling';
import { McvTimeLineViewDialogComponent } from '../mcv-time-line-view-dialog/mcv-time-line-view-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { first, firstValueFrom } from 'rxjs';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { Contact } from 'src/app/contact/models/contact';
import { AuthService } from 'src/app/auth/services/auth.service';
import { McvTimeLineDateUtilityService } from '../../services/mcv-time-line-date-utility.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { WFTaskApiService } from 'src/app/wf-task/services/wf-task-api.service';
import { AppConfig } from 'src/app/app.config';
import { LeaveApiService } from 'src/app/leave-application/services/leave-api.service';
import { InspectionApiService } from 'src/app/inspection/services/inspection-api.service';
import { ApiFilter } from 'src/app/models/api-filters';
import { MeetingApiService } from 'src/app/meeting/services/meeting-api.service';
import { TimeEntryDto } from 'src/app/models/time-entry-dto';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { PackageApiService } from 'src/app/package/services/package-api.service';
import { Package } from 'src/app/package/models/package.model';
import { McvTimeLineService } from '../../services/mcv-time-line.service';
import { McvTimeLineStudioWorkUpdateComponent } from '../mcv-time-line-studio-work-update/mcv-time-line-studio-work-update.component';
import { McvTimeLineViewLeaveComponent } from '../mcv-time-line-view-leave/mcv-time-line-view-leave.component';
import { McvTimeLineViewInspectionComponent } from '../mcv-time-line-view-inspection/mcv-time-line-view-inspection.component';
import { McvTimeLineViewMeetingComponent } from '../mcv-time-line-view-meeting/mcv-time-line-view-meeting.component';
import { McvTimeLineEvent } from '../../models/mcv-time-line-event';
import { McvTimeEvent } from '../../models/mcv-time-event';
import { WFTask } from 'src/app/models/wf-task.model';

@Component({
  selector: 'app-mcv-time-line-view',
  templateUrl: './mcv-time-line-view.component.html',
  styleUrls: ['./mcv-time-line-view.component.scss'],
  standalone: true,

  imports: [
    MatIconModule,
    MatMenuModule,
    MatTooltipModule,
    CommonModule,
    MatButtonModule,
    McvTimeLineViewDialogComponent,
    CdkVirtualForOf,
    CdkVirtualScrollViewport,
    CdkFixedSizeVirtualScroll,
    MatMenuModule,
  ],
})
export class McvTimeLineViewComponent implements OnInit, AfterViewChecked {
  @ViewChild('scrollOne', { static: false }) scrollOne!: ElementRef;
  @ViewChild('scrollThree', { static: false }) scrollThree!: ElementRef;
  @ViewChild('scrollTwo', { static: false }) scrollTwo!: ElementRef;
  @Output() contactsEmitter = new EventEmitter<any[]>();
  @Output() partnerListEmitter = new EventEmitter<any[]>();
  @Output() associateListEmitter = new EventEmitter<any[]>();
  @ViewChild('timeblock', { static: false }) timeBlockWidth!: ElementRef;
  @ViewChild('taskBlock', { static: false }) taskBlockWidth!: ElementRef;
  contactList: any;
  isMainTimeline: boolean = true;
  isFullWeek: boolean = true;
  isDetailedView: boolean = false;
  filteredContacts: any[] = [];
  selectedGroupContacts: any[] = [];
  rangeStartDate = new Date();
  rangeEndDate = new Date();
  currentDate = new Date();
  visibleDays: Date[] = [];
  dayWidth: number = 0;
  hourWidth: number = 0;
  taskList: any[] = [];
  leavesList: any[] = [];
  inspectionList: any[] = [];
  meetingList: any[] = [];
  travelTimeList: any[] = [];
  prepareReportMinutes: any[] = [];
  eventList: any[] = [];
  leftPosition: number = 0;
  numOfSegmentsArray = Array.from({ length: 9 });
  activePackages: Package[] = [];
  isLegendOpenFlag: boolean = false;
  packageData: any;
  isPackageTimeline: boolean = false;
  isWeekChanged: boolean = false;
  searchedTerm: string = '';
  mcvTimelineEvents: McvTimeEvent[] = [];
  viewModeOptions = ['Full Week', 'Custom Week'];
  weekDaysOption = [1, 2, 3, 4, 5, 6, 7];
  selectedWeekMode: string = 'Full Week';
  selectedDayMode: number | undefined;
  scrollTimeout: any;
  legendItems = [
    { label: 'Meeting', color: '#ff9a9e' },
    { label: 'Leave / Break / Half Day', color: '#AF9BFF' },
    { label: 'Inspection', color: '#4e4376' },
    { label: 'Studio Work Assigned', color: '#528BFF' },
    { label: 'Studio Work Inprogress', color: '#3ac487' },
    { label: 'Studio Work Paused', color: '#dfa579' },
    { label: 'Studio Work Delayed', color: '#C54B44' },
    { label: 'Studio Work Completed', color: '#5d5d5d' },
  ];
  taskOverlap: any;
  associatesList: any[]=[];
  partnerList:  any[]=[];
  get currentUser(): Contact {
    return this.authService.currentUserStore
      ? this.authService.currentUserStore.contact
      : new Contact();
  }

  get isMobileView() {
    return this.utilityService.isMobileView;
  }

  trackById(index: number, item: any): number {
    return item.id; // Use unique ID to track items
  }

  trackByLeaveId(index: number, leave: any): number {
    return leave.id; // Use unique ID to track items
  }

  @Input() set package(data: any) {
    this.packageData = data;
  }

  @Input()
  set componentFlag(component: any) {
    if (component == 'mytimeline') {
      this.isMainTimeline = false;
      this.filterCurrentUser();
    } else if (component == 'packagetimeline') {
      this.isMainTimeline = true;
      this.isPackageTimeline = true;
    } else {
      this.isMainTimeline = true;
    }
  }

  @Input()
  set contactsList(contacts: any[]) {
    if (contacts && contacts.length != 0) {
      this.selectedGroupContacts = contacts;
      this.filteredContacts = contacts
      // this.sortContactsAlphabetically()
      console.log('this.filteredContacts>>>>', this.filteredContacts);
    } else {
      this.filteredContacts = this.contactList;
    }
  }

  @Input()
  set searchTerm(searchname: string) {
    if (searchname && searchname != '') {
      this.searchedTerm = searchname;
      this.filterContacts();
    } else if (searchname == '') {
      this.filteredContacts = this.selectedGroupContacts ? this.selectedGroupContacts : this.contactList;
    }
  }
  constructor(
    private utilityService: UtilityService,
    private wfTaskService: WFTaskApiService,
    private inspectionService: InspectionApiService,
    private leaveService: LeaveApiService,
    public config: AppConfig,
    private contactservice: ContactApiService,
    private authService: AuthService,
    private dateUtility: McvTimeLineDateUtilityService,
    private cdr: ChangeDetectorRef,
    private meetingApiService: MeetingApiService,
    private dialog: MatDialog,
    private packageService: PackageApiService,
    private timelineService: McvTimeLineService
  ) {
    this.initializeTimeline();
    effect(() => {
      const task = this.timelineService.taskCreatedSignal();
      const list = this.timelineService.selectedAssociatesSignal();
      if (task) {
        this.syncEventData();
      }
      if(list){
        console.log('list',list);
        this.filterAndUpdateTimelineEvents(list)
      }
    });
  }

  // async refreshTask() {
  //   await this.getTaskList();
  //   this.addTaskMetrics();
  //   //  this.updateCurrentTime();
  // }

  async syncEventData() {0                   
    this.mcvTimelineEvents = [];
   await this.getTaskList();
   await this.getInspectionList();
   await this.getLeaveList();
   await this.getMeetingList();
   await this.getTravelTime();
    this.getActivePackage();
    this.addTaskMetrics();
    this.calculateTaskTopPositions();
  }

  createNewMappedTask(task: WFTask) {
    // Map the task properties manually
    const newTask: McvTimeEvent = {
      id: task.id,
      title: task.title || '',
      subtitle: task.subtitle || '',
      startDate: task.startDate,
      endDate: task.dueDate,
      serviceType: 'Studio Work',
      typeFlag: task.typeFlag ?? false, // Ensure boolean default
      createdBy: task.assigner.name,
      createdOn: new Date(task.created),
      contactID: task.contactID || 0,
      statusFlag: task.statusFlag,
      taskWidth: 0,
      taskLeftPosition: '',
      isExtended: false,
      mHr: task.mHrAssigned.toString(),
      // package: task.package,
    };
  
    // Add to the timeline events list
    this.mcvTimelineEvents.push(newTask);
  
    // Update task metrics and recalculate positions
    this.addTaskMetrics();
    this.calculateTaskTopPositions();
  }
  
  
  async ngOnInit() {
    // this.isMainTimeline = true;

    this.getContactList();
    await this.getTaskList();
    await this.getInspectionList();
    await this.getLeaveList();
    await this.getMeetingList();
    await  this.getTravelTime();
    this.getAssociatePartnerList();
     this.getActivePackage();
    this.calculateTaskTopPositions();
    this.eventList = this.mcvTimelineEvents
  }

  filterCurrentUser() {
    if (this.contactList && this.contactList.length != 0) {
      this.filteredContacts = this.contactList.filter(
        (contact: any) => contact.id == this.currentUser.id
      );
    }
  }

  ngAfterViewChecked(): void {
    if (!this.dayWidth && this.timeBlockWidth?.nativeElement?.offsetWidth) {
      this.dayWidth = this.timeBlockWidth.nativeElement.offsetWidth;
      this.hourWidth = this.dayWidth / 9;
    }
    this.addTaskMetrics();
    this.calculateLineLeftPosition();
  }

  filterContacts() {
    if (this.contactList && this.contactList.length != 0 ) {
      this.filteredContacts = this.filteredContacts.filter((contact: any) =>
        contact.name.toLowerCase().includes(this.searchedTerm.toLowerCase())
      );
    }
  }
  splitLeaveIntoDays(leave: any) {
    const startDate = new Date(leave.start);
    const endDate = new Date(leave.end);
    const leaveDays = [];
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      if (currentDate.getDay() !== 0) {
        const dayStart = new Date(currentDate);
        const dayEnd = new Date(currentDate);
        dayStart.setHours(0, 0, 0, 0);
        dayEnd.setHours(23, 59, 59, 999);
        if (currentDate.toDateString() === startDate.toDateString()) {
          dayStart.setHours(
            startDate.getHours(),
            startDate.getMinutes(),
            startDate.getSeconds(),
            startDate.getMilliseconds()
          );
        }
        if (currentDate.toDateString() === endDate.toDateString()) {
          dayEnd.setHours(
            endDate.getHours(),
            endDate.getMinutes(),
            endDate.getSeconds(),
            endDate.getMilliseconds()
          );
        }
        leaveDays.push({
          ...leave,
          start: dayStart.toISOString(),
          end: dayEnd.toISOString(),
        });
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return leaveDays;
  }

  calculateTaskTopPositions() {
    let groupedTasks: { [key: string]: any[] } = {};
  
    // Group tasks by contactID and start date
    this.mcvTimelineEvents.forEach((task) => {
      const taskStartDate = new Date(task.startDate).toDateString(); // Normalize date
      const key = `${task.contactID}_${taskStartDate}`;
  
      if (!groupedTasks[key]) {
        groupedTasks[key] = [];
      }
  
      groupedTasks[key].push(task);
    });
  
    // Process each group separately
    Object.entries(groupedTasks).forEach(([day, tasks]) => {
      // Sort tasks by start time (ignoring seconds and milliseconds)
      tasks.sort(
        (a, b) =>
          this.getTimeWithoutSeconds(a.startDate) - this.getTimeWithoutSeconds(b.startDate)
      );
  
      let activeRows: any[][] = []; // Array of rows to track task stacking
  
      tasks.forEach((task) => {
        const taskStart = this.getTimeWithoutSeconds(task.startDate);
        const taskEnd = this.getTimeWithoutSeconds(task.endDate);
  
        let placed = false;
  
        // Try placing in an existing row without overlap
        for (let rowIndex = 0; rowIndex < activeRows.length; rowIndex++) {
          let row = activeRows[rowIndex];
  
          // Check if task overlaps with any task in this row
          let isOverlapping = row.some((t) => {
            const tStart = this.getTimeWithoutSeconds(t.startDate);
            const tEnd = this.getTimeWithoutSeconds(t.endDate);
  
            return tStart < taskEnd && tEnd > taskStart; // Overlap check ignoring seconds
          });
  
          if (!isOverlapping) {
            row.push(task);
            task.taskTopPosition = `${rowIndex * 52}px`;
            placed = true;
            break;
          }
        }
  
        // If overlapping, push to a new row
        if (!placed) {
          activeRows.push([task]);
          task.taskTopPosition = `${(activeRows.length - 1) * 52}px`;
        }
      });
    });
  }
  
  getTaskListHeight(personID: number): string {
    let groupedTasks: { [key: string]: any[] } = {};
  
    // Convert range start and end dates to Date objects for comparison
    const rangeStart = new Date(this.rangeStartDate).setHours(0, 0, 0, 0);
    const rangeEnd = new Date(this.rangeEndDate).setHours(23, 59, 59, 999);
  
    // Group tasks by contactID and start date, but only within the date range
    this.mcvTimelineEvents.forEach((task) => {
      const taskStartDate = new Date(task.startDate).setHours(0, 0, 0, 0);
  
      if (
        task.contactID === personID &&
        taskStartDate >= rangeStart &&
        taskStartDate <= rangeEnd &&
        this.isTaskValid(task.startDate, task.endDate)
      ) {
        const key = `${task.contactID}_${new Date(task.startDate).toDateString()}`;
  
        if (!groupedTasks[key]) {
          groupedTasks[key] = [];
        }
  
        groupedTasks[key].push(task);
      }
    });
  
    let maxOverlap = 0;
  
    // Process each group separately
    Object.entries(groupedTasks).forEach(([day, tasks]) => {
      // Sort tasks by start time (ignoring seconds and milliseconds)
      tasks.sort(
        (a, b) =>
          this.getTimeWithoutSeconds(a.startDate) - this.getTimeWithoutSeconds(b.startDate)
      );
  
      let activeRows: any[][] = []; // Array of rows to track task stacking
  
      tasks.forEach((task) => {
        const taskStart = this.getTimeWithoutSeconds(task.startDate);
        const taskEnd = this.getTimeWithoutSeconds(task.endDate);
  
        let placed = false;
  
        // Try placing in an existing row without overlap
        for (let rowIndex = 0; rowIndex < activeRows.length; rowIndex++) {
          let row = activeRows[rowIndex];
  
          // Check if task overlaps with any task in this row
          let isOverlapping = row.some((t) => {
            const tStart = this.getTimeWithoutSeconds(t.startDate);
            const tEnd = this.getTimeWithoutSeconds(t.endDate);
  
            return tStart < taskEnd && tEnd > taskStart; // Overlap check ignoring seconds
          });
  
          if (!isOverlapping) {
            row.push(task);
            placed = true;
            break;
          }
        }
  
        // If overlapping, push to a new row
        if (!placed) {
          activeRows.push([task]);
        }
      });
  
      // Track the max number of overlapping rows for this person
      maxOverlap = Math.max(maxOverlap, activeRows.length);
    });
  
    // Calculate the height based on max overlaps
    let height = `calc(${maxOverlap * 52}px + ${(maxOverlap - 1) * 0.3}rem)`;
    return height;
  }
  
  

  getTimeWithoutSeconds(dateString: string): number {
    const date = new Date(dateString);
    return date.getHours() * 60 + date.getMinutes(); // Convert to minutes since midnight
  }
  
  async getTravelTime() {
    let eventsFilter = [
      {
        key: 'timelineRangeStart',
        value: this.utilityService
          .convertToUTCDate(this.rangeStartDate)
          .toISOString(),
      },
      {
        key: 'timelineRangeEnd',
        value: this.utilityService
          .convertToUTCDate(this.rangeEndDate)
          .toISOString(),
      },
    ];
    let filters = eventsFilter.map((x) => Object.assign({}, x));
    filters = filters.concat([
      { key: 'entity', value: this.config.NAMEOF_ENTITY_MEETING },
      { key: 'entity', value: this.config.NAMEOF_ENTITY_INSPECTION },
      { key: 'statusFlag', value: '1' },
    ]);
    const _data = await firstValueFrom(this.wfTaskService.get(filters));
    const _closedMeetings = _data.filter(
      (task) =>
        task.entity == this.config.NAMEOF_ENTITY_MEETING &&
        task.wfStageCode == this.config.TASK_STAGE_MEETING_CLOSE
    );
    // this.meetingList.push(_closedMeetings);

    _closedMeetings.forEach((meeting: any) => {
      this.mcvTimelineEvents.push({
        id: meeting.id,
        title: meeting.title || '',
        subtitle: meeting.subtitle || '',
        startDate: meeting.startDate,
        endDate: meeting.dueDate,
        serviceType: 'Meeting',
        typeFlag: meeting.typeFlag,
        createdBy: meeting.createdBy,
        createdOn: new Date(meeting.created),
        contactID: meeting.contactID,
        statusFlag: meeting.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        description: meeting.location,
        mHr:meeting.mHrAssigned,
      });
    });
    const _closedInspection = _data.filter(
      (task) =>
        task.entity == this.config.NAMEOF_ENTITY_INSPECTION &&
        task.wfStageCode == this.config.TASK_STAGE_INSPECTION_CLOSE
    );
    // this.meetingList.push(_closedMeetings);

    _closedInspection.forEach((inspection: any) => {
      this.mcvTimelineEvents.push({
        id: inspection.id,
        title: inspection.title || '',
        subtitle: inspection.subtitle || '',
        startDate: inspection.startDate,
        endDate: inspection.dueDate,
        serviceType: 'Inspection',
        typeFlag: inspection.typeFlag,
        createdBy: inspection.createdBy,
        createdOn: new Date(inspection.created),
        contactID: inspection.contactID,
        statusFlag: inspection.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        description: inspection.location,
        mHr: inspection.mHrAssigned,
      });
    });
    this.travelTimeList = await _data
      .filter(
        (task) =>
          task.timeEntries.length != 0 &&
          (task.wfStageCode == this.config.TASK_STAGE_MEETING_TRAVEL_TIME ||
            task.wfStageCode == this.config.TASK_STAGE_INSPECTION_TRAVEL_TIME)
      )
      .flatMap((task) =>
        task.timeEntries.map((timeEntry: TimeEntryDto) => ({
          ...timeEntry,
          contact: task.contact,
          wfStageCode: task.wfStageCode,

          code: task.subtitle,
        }))
      );

    this.travelTimeList.forEach((element: any) => {
      this.mcvTimelineEvents.push({
        id: element.id,
        title: element.title ,
        subtitle: element.entityTitle ,
        startDate: element.startDate,
        endDate: element.endDate,
        serviceType: 'Travel Time',
        typeFlag: element.typeFlag,
        createdBy: element.createdBy,
        createdOn: new Date(element.created),
        contactID: element.contactID,
        statusFlag: element.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        wfStageCode: element.wfStageCode,
        mHr: element.mHrAssigned,
      });
    });
    this.prepareReportMinutes = await _data
      .filter(
        (task) =>
          task.timeEntries.length !== 0 &&
          (task.wfStageCode ===
            this.config.TASK_STAGE_INSPECTION_PREPARE_REPORT ||
            task.wfStageCode === this.config.TASK_STAGE_MEETING_PREPARE_MINUTES)
      )
      .flatMap((task) =>
        task.timeEntries.map((timeEntry: TimeEntryDto) => ({
          ...timeEntry,
          contact: task.contact,
          wfStageCode: task.wfStageCode,

          code: task.subtitle,
        }))
      );
    this.prepareReportMinutes.forEach((element: any) => {
      this.mcvTimelineEvents.push({
        id: element.id,
        title: element.title ,
        subtitle: element.entityTitle,
        startDate: element.startDate,
        endDate: element.endDate,
        serviceType: 'Prepare Minutes',
        typeFlag: element.typeFlag,
        createdBy: element.createdBy,
        createdOn: new Date(element.created),
        contactID: element.contactID,
        statusFlag: element.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        wfStageCode: element.wfStageCode,
        mHr: element.mHrAssigned,
      });
    });
    // this.calculateTaskTopPositions()
    console.log(' this.travelTimeList ', this.travelTimeList);
    console.log(' this.travelTimeList ', this.prepareReportMinutes);
  }

  async getMeetingList() {
    let eventsFilter: ApiFilter[] = [
      {
        key: 'rangestart',
        value: this.utilityService
          .convertToUTCDate(this.rangeStartDate)
          .toISOString(),
      },
      {
        key: 'rangeend',
        value: this.utilityService
          .convertToUTCDate(this.rangeEndDate)
          .toISOString(),
      },
      { key: 'IsVersion', value: 'false' },
      { key: 'typeFlag', value: this.config.MEETING_TYPEFLAG_MEETING.toString() },
      { key: 'statusFlag', value: this.config.MEETING_STATUSFLAG_SCHEDULED.toString() }, //pending meetings
    ];
    this.meetingList = await firstValueFrom(
      this.meetingApiService.get(eventsFilter)
    );

    this.meetingList.forEach((meeting: any) => {
      this.mcvTimelineEvents.push({
        id: meeting.id,
        title: meeting.title || '',
        subtitle: meeting.subtitle || '',
        startDate: meeting.startDate,
        endDate: meeting.endDate,
        serviceType: 'Meeting',
        typeFlag: meeting.typeFlag,
        createdBy: meeting.createdBy,
        createdOn: new Date(meeting.created),
        contactID: meeting.contactID,
        statusFlag: meeting.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        description: meeting.location,
      });
    });
    // this.calculateTaskTopPositions()
  }

  async getLeaveList() {
    let eventsFilter:ApiFilter[] = [
      {
        key: 'timelineRangeStart',
        value: this.utilityService
          .convertToUTCDate(this.rangeStartDate)
          .toISOString(),
      },
      {
        key: 'timelineRangeEnd',
        value: this.utilityService
          .convertToUTCDate(this.rangeEndDate)
          .toISOString(),
      },
      { key: 'statusFlag', value: this.config.LEAVE_STATUSFLAG_APPROVED.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_APPROVED.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_EMERGENCY.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_APPROVED_BREAK.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_EMERGENCY_BREAK.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_APPROVED_HALFDAY.toString() },
      { key: 'typeFlag', value: this.config.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY.toString() },
    ];

    // Clear previous list before fetching new data
    this.leavesList = [];
    let leaves = await firstValueFrom(this.leaveService.get(eventsFilter));

    for (const leave of leaves) {
      const splitLeaves = this.splitLeaveIntoDays(leave);

      this.leavesList.push(...splitLeaves); // Push all at once for performance
    }

    this.leavesList.forEach((task: any) => {
      // Check if typeFlag matches approved or emergency leave types
      const isSpecialLeave =
        task.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED ||
        task.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY;

      // Set start and end times based on condition
      const startDate = new Date(task.start);
      const endDate = new Date(task.end);

      if (isSpecialLeave) {
        // Override time with TIMELINE_START_TIME and TIMELINE_END_TIME
        const [startHour, startMinute] =
          this.config.TIMELINE_START_TIME.split(':').map(Number);
        const [endHour, endMinute] =
          this.config.TIMELINE_END_TIME.split(':').map(Number);

        startDate.setHours(startHour, startMinute, 0, 0);
        endDate.setHours(endHour, endMinute, 0, 0);
      }

      this.mcvTimelineEvents.push({
        id: task.id,
        title: task.title || '',
        subtitle: task.subtitle || '',
        startDate: startDate, // Ensure proper format
        endDate: endDate,
        serviceType: 'Leaves',
        typeFlag: task.typeFlag, // Ensure boolean default
        createdBy: task.createdBy,
        createdOn: new Date(task.created),
        contactID: task.contactID,
        statusFlag: task.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        description: task.reason,
      });
    });

    // this.calculateTaskTopPositions()
    console.log('this.mcvTimelineEvents', this.mcvTimelineEvents);
  }

  async getInspectionList() {
    const inspectionFilter: ApiFilter[] = [
      { key: 'IsVersion', value: 'false' },
      {
        key: 'rangestart',
        value: this.utilityService
          .convertToUTCDate(this.rangeStartDate)
          .toISOString(),
      },
      {
        key: 'rangeend',
        value: this.utilityService
          .convertToUTCDate(this.rangeEndDate)
          .toISOString(),
      },
      { key: 'statusFlag', value: this.config.INSPECTION_STATUS_PENDING.toString() },
    ];
    // this.inspectionList = await firstValueFrom(
    //   this.inspectionService.get(inspectionFilter)
    // );
    // this.inspectionList.forEach((inspection: any) => {
    //   this.mcvTimelineEvents.push({
    //     id: inspection.id,
    //     title: inspection.title || '',
    //     subtitle: inspection.subtitle || '',
    //     startDate: inspection.startDate,
    //     endDate: inspection.endDate,
    //     serviceType: 'Inspection',
    //     typeFlag: inspection.typeFlag, // Ensure boolean default
    //     createdBy: inspection.createdBy,
    //     createdOn: new Date(inspection.created),
    //     contactID: inspection.contactID,
    //     statusFlag: inspection.statusFlag,
    //     taskWidth: 0,
    //     taskLeftPosition: '',
    //     description:inspection.location
    //   });
    // });
    // this.calculateTaskTopPositions()
    console.log('this.inspectionList', this.inspectionList);
  }

  async getTaskList() {
    let eventsFilter = [
      {
        key: 'timelineRangeStart',
        value: this.utilityService
          .convertToUTCDate(this.rangeStartDate)
          .toISOString(),
      },
      {
        key: 'timelineRangeEnd',
        value: this.utilityService
          .convertToUTCDate(this.rangeEndDate)
          .toISOString(),
      },
    ];
    let filters = eventsFilter.map((x) => Object.assign({}, x));
    filters = filters.concat([
      { key: 'isPreAssignedTimeTask', value: 'true' },
      { key: 'entity', value: 'Package' },
      { key: 'statusFlag', value: '0' },
      { key: 'statusFlag', value: '1' },
      { key: 'statusFlag', value: '2' },
      { key: 'statusFlag', value: '3' },
    ]);

    this.taskList = await firstValueFrom(
      this.wfTaskService.getTimeline(filters)
    );
 

    if (this.taskList.some(task => 'outcomeFlag' in task)) {
      this.taskList = this.taskList.filter(task => task.outcomeFlag !== -1);
    }

    this.taskList.forEach((task: any) => {
      this.mcvTimelineEvents.push({
        id: task.id,
        title: task.title || '',
        subtitle: task.subtitle || '',
        startDate: task.startDate,
        endDate: task.dueDate,
        serviceType: 'Studio Work',
        typeFlag: task.typeFlag, // Ensure boolean default
        createdBy: task.createdBy,
        createdOn: new Date(task.created),
        contactID: task.assigneeContactID || 0,
        statusFlag: task.statusFlag,
        taskWidth: 0,
        taskLeftPosition: '',
        isExtended: false,
        mHr: task.mHrAssigned,
        package : task.package

      });
    });
   
   
    console.log('this.sortedTaskList ', this.mcvTimelineEvents);
    // this.calculateTaskTopPositions()
  }

  async getContactList() {
    let contactFilter = [
      { key: 'usersOnly', value: 'true' },
      { key: 'appointmentStatusFlag', value: '0' },
    ];

    this.contactList = await firstValueFrom(
      this.contactservice.get(contactFilter)
    );
    this.contactList.reverse();
    if (this.isMainTimeline) {
      this.filteredContacts = this.contactList;
      this.contactsEmitter.emit(this.contactList);
    } else {
      this.filteredContacts = this.contactList.filter(
        (contact: any) => contact.id === this.currentUser.id
      );
    }
  }

  getActivePackage() {
    const packageFilter = [
      { key: 'statusFlag', value: this.config.PACKAGE_STATUSFLAG_ACTIVE.toString() },
      { key: 'typeFlag', value: this.config.PACKAGE_TYPEFLAG_ACTIVE.toString() },
    ];
    this.packageService.get(packageFilter).subscribe((data) => {
      if (data) {
        this.activePackages = data;
        // this.activePackages = this.activePackages.filter(x => x.activeStage == this.config.NAMEOF_ENTITY_PACKAGE);
      }
    });
  }

  async initializeTimeline() {
    if (this.currentDate.getDay() === 0) {
      this.isFullWeek = true;
    }
    if(this.isFullWeek){
      this.rangeStartDate = this.dateUtility.getWeekStart(this.currentDate);
      // this.rangeEndDate =  new Date ('Sun Aug 04 2024 23:59:59 GMT+0530')

      this.rangeEndDate = this.dateUtility.getWeekEnd(
        this.currentDate,
        this.isFullWeek
      );
   
    }

    const start = new Date(this.rangeStartDate);
    const end = new Date(this.rangeEndDate);
    const daysArray = [];

    while (start <= end) {
      daysArray.push(new Date(start));
      start.setDate(start.getDate() + 1);
    }

    this.visibleDays = await daysArray;
    console.log('visibleDays', this.visibleDays);
  }

  async setWeekMode(mode: any) {
    if (mode == 'Full Week') {
      this.isFullWeek = true;
      this.rangeStartDate = this.dateUtility.getWeekStart(this.currentDate);
      this.rangeEndDate = this.dateUtility.getWeekEnd(
        this.currentDate,
        this.isFullWeek
      );
    } else {
      this.isFullWeek = false;
      this.setDayPerWeek(1)

    }
    this.selectedWeekMode = mode;
   
    await this.initializeTimeline(); // Wait for timeline initialization
    this.getAssociatePartnerList()
    this.cdr.detectChanges(); // Ensure Angular updates the view before accessing the DOM

    this.updateDayWidth(); // Now call a separate function to update dimensions
    this.addTaskMetrics();
  }

  async setDayPerWeek(mode: number) {
    this.selectedDayMode = mode;
    const selectedDays = mode; // Convert to number

      this.rangeStartDate = new Date(this.currentDate);
      this.rangeEndDate = new Date(this.currentDate);
      this.rangeEndDate.setDate(
        this.rangeStartDate.getDate() + (selectedDays - 1)
      );
    // }

    await this.initializeTimeline(); 
    this.getAssociatePartnerList()
    this.cdr.detectChanges();
    this.updateDayWidth();
    this.addTaskMetrics();
  }

  updateDayWidth() {
    this.dayWidth = this.timeBlockWidth.nativeElement.offsetWidth + 0.5;
    console.log('this.dayWidth', this.dayWidth);
    this.hourWidth = this.dayWidth / 9;
  }

  infoModeChange() {
    this.isDetailedView = !this.isDetailedView;
  }

  getPersonEvent(id: any, day: any) {
    return this.mcvTimelineEvents.filter(
      (event) =>
        event.contactID === id &&
        new Date(event.startDate).setHours(0, 0, 0, 0) ===
          new Date(day).setHours(0, 0, 0, 0)
    );
  }


  getUtcDate(date: Date): string {
    return new Date(date).toISOString(); // Converts to UTC format
  }

  // calculateLineLeftPosition(): number {
  //   // Get current date & time (assumed to be in IST)
  //   const now = new Date();
  //   const dayOfWeek = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
  //   const hours = now.getHours(); // e.g., 15 (3 PM)
  //   const minutes = now.getMinutes(); // e.g., 48

  //   // Timeline constraints (9:00 AM to 6:00 PM)
  //   const timelineStartHour = 9;
  //   const timelineEndHour = 18;
  //   const totalTimelineHours = timelineEndHour - timelineStartHour; // 9 hours

  //   const totalDays = this.isFullWeek ? 7 : 6;

  //   // Adjust day index:
  //   let adjustedDayIndex = this.isFullWeek
  //     ? dayOfWeek === 0
  //       ? 6
  //       : dayOfWeek - 1 // Move Sunday to last if full week
  //     : dayOfWeek === 0
  //     ? -1
  //     : dayOfWeek - 1; // Ignore Sunday if not full week

  //   // If not full week and today is Sunday, return -1 (no timeline on Sunday)
  //   if (!this.isFullWeek && dayOfWeek === 0) {
  //     return -1;
  //   }

  //   // Get timeline width (avoid using full `window.outerWidth`)
  //   const totalWidth = window.innerWidth; // More reliable

  //   // Single day width
  //   const singleDayWidth = totalWidth / totalDays;

  //   // Calculate the total time from Monday 9 AM to now
  //   let totalHoursFromMonday =
  //     adjustedDayIndex * totalTimelineHours +
  //     (hours - timelineStartHour) +
  //     minutes / 60;

  //   // Ensure time is within working hours
  //   if (hours < timelineStartHour)
  //     totalHoursFromMonday = adjustedDayIndex * totalTimelineHours;
  //   if (hours >= timelineEndHour)
  //     totalHoursFromMonday = (adjustedDayIndex + 1) * totalTimelineHours;

  //   // **Use `hourWidth` to ensure accurate positioning**
  //   const leftPosition = totalHoursFromMonday * this.hourWidth;

  //   return leftPosition + 48;
  // }
  calculateLineLeftPosition(): number {
    // Get current date & time (assumed to be in IST)
    const now = new Date();
    const dayOfWeek = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
    const hours = now.getHours(); // e.g., 15 (3 PM)
    const minutes = now.getMinutes(); // e.g., 48

    // Timeline constraints (9:00 AM to 6:00 PM)
    const timelineStartHour = 9;
    const timelineEndHour = 18;
    const totalTimelineHours = timelineEndHour - timelineStartHour; // 9 hours

    let startDayIndex: number;
    let totalDays: number;

    if (this.isFullWeek) {
        startDayIndex = 1; // Monday
        totalDays = 7; // Monday to Sunday
    } else {
        startDayIndex = dayOfWeek; // Start from today
        totalDays = this.selectedDayMode ?? 1; // Selected range (current day + N days)
    }

    // Get timeline width
    const totalWidth = window.innerWidth; 
    const singleDayWidth = totalWidth / totalDays;

    // Adjusted day index (relative to start day)
    const adjustedDayIndex = dayOfWeek - startDayIndex;

    // If out of range, return -1 (meaning no line should be drawn)
    if (adjustedDayIndex < 0 || adjustedDayIndex >= totalDays) {
        return -1;
    }

    // Calculate the total time from start day at 9 AM to now
    let totalHoursFromStart =
        adjustedDayIndex * totalTimelineHours +
        (hours - timelineStartHour) +
        minutes / 60;

    // Ensure time is within working hours
    if (hours < timelineStartHour) {
        totalHoursFromStart = adjustedDayIndex * totalTimelineHours;
    }
    if (hours >= timelineEndHour) {
        totalHoursFromStart = (adjustedDayIndex + 1) * totalTimelineHours;
    }

    // Use `hourWidth` for accurate positioning
    let leftPosition = totalHoursFromStart * this.hourWidth;

    // **Leave 6rem on the left** (already 3rem considered)
    leftPosition += 96; // (6rem = 96px)

    return leftPosition;
}

  getTaskStatus(statusFlag: number, task: any): string {
    const currentDate = new Date();
    const taskDueDate = new Date(task.dueDate);
    switch (statusFlag) {
      case 0:
        return 'assigned-task';
      case 1:
        return 'completed-task';
      case 2:
        if (taskDueDate < currentDate) {
          return 'due-task';
        } else {
          return 'inprogress-task';
        }

      case 3:
        return 'paused-task';
      case 4:
        return 'rejected-task';

      default:
        return '';
    }
  }

  addTaskMetrics() {
    this.mcvTimelineEvents.forEach((event: any) => {
      const metrics = this.calculateTaskMetrics(event);

      event.taskWidth = metrics.taskWidth;
      event.taskLeftPosition = metrics.taskLeftPosition;
      event.isExtended = metrics.isExtended;
    });
  }

  calculateTaskMetrics(event: any) {
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);

    // Define timeline boundaries
    const timelineStart = new Date(start);
    timelineStart.setHours(9, 0, 0, 0); // 09:00

    const timelineEnd = new Date(end);
    timelineEnd.setHours(18, 0, 0, 0); // 18:00

    // Adjusted end time limit (12:30 UTC)
    const endLimit = new Date(end);
    endLimit.setUTCHours(12, 30, 0, 0);
    const adjustedEnd = end.getTime() > endLimit.getTime() ? endLimit : end;

    // Check if event.endDate exceeds 18:00
    const isExtended = end.getTime() > timelineEnd.getTime();

    // Task width calculation
    const diffInMs = adjustedEnd.getTime() - start.getTime();
    const taskWidth = (diffInMs / (1000 * 60 * 60)) * this.hourWidth;

    // Left position calculation
    const diffInMsLeft = start.getTime() - timelineStart.getTime();
    const leftPosition = Math.max(
      0,
      (diffInMsLeft / (1000 * 60 * 60)) * this.hourWidth
    );

    return {
      taskWidth,
      taskLeftPosition: `${leftPosition}px`,
      isExtended,
    };
  }

  isTaskValid(startDate: Date, endDate: Date): boolean {
    const start = new Date(startDate);
    const end = new Date(endDate);
  
    // Convert TIMELINE_START_TIME and TIMELINE_END_TIME to hours
    const [startHour] = this.config.TIMELINE_START_TIME.split(":").map(Number);
    const [endHour] = this.config.TIMELINE_END_TIME.split(":").map(Number);
  
    const taskStartHour = start.getHours();
    const taskEndHour = end.getHours();
  
    // Check if the task overlaps with the timeline range
    return taskStartHour < endHour && taskEndHour > startHour;
  }
  
  
  

  toggleLegend() {
    this.isLegendOpenFlag = !this.isLegendOpenFlag;
  }

  // openCreateDialog(person: any, day: any) {
  //   console.log('day',day);
  //   const hasLeave = this.mcvTimelineEvents.some(event => 
  //     event.contactID === person.id && 
  //     event.serviceType === 'Leaves' && 
  //     (event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED || 
  //      event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY) &&
  //     new Date(event.startDate).getFullYear() === day.getFullYear() &&
  //     new Date(event.startDate).getMonth() === day.getMonth() &&
  //     new Date(event.startDate).getDate() === day.getDate() 
  //   );
  //   if (hasLeave) return;

  //   // const containsLeave = this.mcvTimelineEvents.find(event => 
  //   //   event.contactID === person.id && 
  //   //   event.serviceType === 'Leaves' &&
  //   //   new Date(event.startDate).getFullYear() === day.getFullYear() &&
  //   //   new Date(event.startDate).getMonth() === day.getMonth() &&
  //   //   new Date(event.startDate).getDate() === day.getDate()
  //   // );
  //   // const tasksForDay = this.mcvTimelineEvents.filter(event => 
  //   //   event.contactID === person.id && 
  //   //   event.serviceType === 'Studio Work' &&
  //   //   new Date(event.startDate).getFullYear() === day.getFullYear() &&
  //   //   new Date(event.startDate).getMonth() === day.getMonth() &&
  //   //   new Date(event.startDate).getDate() === day.getDate()
  //   // );
  //   const containsEvent = this.mcvTimelineEvents.filter(event => 
  //     event.contactID === person.id &&
  //     new Date(event.startDate).getFullYear() === day.getFullYear() &&
  //     new Date(event.startDate).getMonth() === day.getMonth() &&
  //     new Date(event.startDate).getDate() === day.getDate()
  //   );
  //   // Log or use containsLeave if needed
    

  //   const strippedDay = new Date(
  //     day.getFullYear(),
  //     day.getMonth(),
  //     day.getDate()
  //   );
  //   const strippedCurrentTime = new Date(
  //     this.currentDate.getFullYear(),
  //     this.currentDate.getMonth(),
  //     this.currentDate.getDate()
  //   );

  //   if (strippedDay >= strippedCurrentTime) {
  //     const dialogConfig = new MatDialogConfig();
  //     // dialogConfig.disableClose = true;
  //     dialogConfig.autoFocus = false;
  //     dialogConfig.data = {
  //       isCreateMode: true,
  //       dialogTitle: 'New Event',
  //       //  hour: hour,
   
  //       containsEvent: containsEvent,
  //       person: person,
  //       day: day,
  //       activePackages: this.activePackages,
  //       ispackageTimeline: this.isPackageTimeline,
  //       entityData: this.packageData,
  //     };
  //     const dialogRef = this.dialog.open(
  //       McvTimeLineViewDialogComponent,
  //       dialogConfig
  //     );
  //     dialogRef.afterClosed().subscribe((result) => {
  //     });

  //     return dialogRef;
  //   } else {
  //     this.utilityService.showSweetDialog(
  //       'ERROR',
  //       'Cannot create task before current time',
  //       'error'
  //     );
  //     return;
  //   }
  // }
  openCreateDialog(person: any, day: any) {

    const hasLeave = this.mcvTimelineEvents.some(event =>
      event.contactID === person.id &&
      event.serviceType === 'Leaves' &&
      (event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED ||
        event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY) &&
      new Date(event.startDate).getFullYear() === day.getFullYear() &&
      new Date(event.startDate).getMonth() === day.getMonth() &&
      new Date(event.startDate).getDate() === day.getDate()
    );
    if (hasLeave) return;
  
    const containsEvent = this.mcvTimelineEvents.filter(event =>
      event.contactID === person.id &&
      new Date(event.startDate).getFullYear() === day.getFullYear() &&
      new Date(event.startDate).getMonth() === day.getMonth() &&
      new Date(event.startDate).getDate() === day.getDate()
    );
  
    // Stripping time off the day and current date to compare only date
    const strippedDay = new Date(day.getFullYear(), day.getMonth(), day.getDate());
    const strippedCurrentTime = new Date(); // Use current date and time for comparison
    strippedCurrentTime.setHours(0, 0, 0, 0); // Remove the time part from current date

    if (strippedDay >= strippedCurrentTime) {
      const dialogConfig = new MatDialogConfig();
      dialogConfig.autoFocus = false;
      dialogConfig.data = {
        isCreateMode: true,
        dialogTitle: 'New Event',
        containsEvent: containsEvent,
        person: person,
        day: day,
        activePackages: this.activePackages,
        ispackageTimeline: this.isPackageTimeline,
        entityData: this.packageData,
      };
      const dialogRef = this.dialog.open(
        McvTimeLineViewDialogComponent,
        dialogConfig
      );
      dialogRef.afterClosed().subscribe((result) => {});
  
      return dialogRef;
    } else {
      this.utilityService.showSweetDialog(
        'ERROR',
        'Cannot create task before current time',
        'error'
      );
      return;
    }
  }
  

  openTaskDialog(task: any, person: any, event: Event) {
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    // if (isFullScreen) {
    //   dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    // }
  
    dialogConfig.autoFocus = true;

   
    dialogConfig.data = {
      dialogTitle: 'View Studio Task',
      task: task,
      contact: person,
      activePackages: this.activePackages,
    };
    const dialogRef = this.dialog.open(
      McvTimeLineStudioWorkUpdateComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
     
    //   console.log('result', result);
      if (result) {
        this.syncEventData()
    //     // Map updateValue to McvTimeEvent model

    //     const mappedValue: McvTimeEvent = {
    //       id: result.updateValue.id,
    //       title: result.updateValue.title || '',
    //       subtitle: result.updateValue.subtitle || '',
    //       startDate: new Date(result.updateValue.startDate),
    //       endDate: new Date(result.updateValue.dueDate),
    //       serviceType: 'Studio Work', // Defaulting to 'Meeting'
    //       typeFlag: result.updateValue.typeFlag || 0,
    //       statusFlag: result.updateValue.statusFlag || 0,
    //       createdBy: result.updateValue.createdBy || '',
    //       createdOn: new Date(result.updateValue.created),
    //       contactID: result.updateValue.contactID,
    //       taskWidth: 0, // Default value, can be updated later
    //       taskLeftPosition: '', // Default value, can be updated later
    //       isExtended: result.updateValue.isPreAssignedTimeTask || false,
    //       description: result.updateValue.description || '',
    //       wfStageCode: result.updateValue.wfStageCode || '',
    //       mHr: result.updateValue.mHrAssigned?.toString() || '0', // Converting to string if available
    //       package : result.updateValue.package
    //     };

    //     console.log('Mapped Value before updating task:', mappedValue); // Debugging log

    //  // Find task in existing mcvTimelineEvents
    //     const taskIndex = this.mcvTimelineEvents.findIndex(
    //       (task: any) => task.id === mappedValue.id && task.title === mappedValue.title
    //     );

    //     if (taskIndex !== -1) {
    //       // First, update the existing task object
    //       const updatedTask = { ...this.mcvTimelineEvents[taskIndex], ...mappedValue };

    //       // Remove the existing task from the list
    //       this.mcvTimelineEvents.splice(taskIndex, 1);

    //       // Add the updated task back to the list
    //       this.mcvTimelineEvents.push(updatedTask);
    //     }

    //     this.addTaskMetrics()
      }

    //   if (result && result.createdTasks && result.createdTasks.length > 0) {
    //     const mappedTasks: McvTimeEvent[] = result.createdTasks.map(
    //       (task: any) => ({
    //         id: task.id,
    //         title: task.title || '',
    //         subtitle: task.subtitle || '',
    //         startDate: new Date(task.startDate),
    //         endDate: new Date(task.dueDate),
    //         serviceType: 'Studio Work', // Defaulting to 'Studio Work'
    //         typeFlag: task.typeFlag || 0,
    //         statusFlag: task.statusFlag || 0,
    //         createdBy: task.createdBy || '',
    //         createdOn: new Date(task.created),
    //         contactID: task.contactID,

    //         isExtended: task.isPreAssignedTimeTask || false,
    //         description: task.description || '',
    //         wfStageCode: task.wfStageCode || '',
    //         mHr: task.mHrAssigned?.toString() || '0', // Converting to string if available
    //       })
    //     );

    //     this.mcvTimelineEvents = this.mcvTimelineEvents.concat(mappedTasks);
    //   }
    //   if (result && result.deletedID) {
    //     this.mcvTimelineEvents = this.mcvTimelineEvents.filter(
    //       (task: any) =>
    //         task.id !== result.deletedID && task.title != result.title
    //     );
    //   }
    //   console.log('this.mcvTimelineEvents', this.mcvTimelineEvents);
    //   this.cdr.detectChanges();

    });

    return dialogRef;
  }

  openLeaveDialog(leave: any, person: any, event: Event) {
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    // dialogConfig.disableClose = false;
 

    // if (this.isMobileView) {
    //   dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    // } else {
    //   dialogConfig.width = '800px';
    // }
    dialogConfig.data = {
      dialogTitle: 'Leave Detail',
      leave: leave,
      contact: person,
    };
    const dialogRef = this.dialog.open(
      McvTimeLineViewLeaveComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
      console.log('Dialog was closed');
    });
    return dialogRef;
  }

  openInspectionDialog(inspection: any, person: any, event: Event) {
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    
   

    
    dialogConfig.data = {
      dialogTitle: 'Inspection Detail',
      data: inspection,
      person: person,
      isInspection: true,
    };
    const dialogRef = this.dialog.open(
      McvTimeLineViewInspectionComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
      console.log('Dialog was closed');
    });
    return dialogRef;
  }

  openMeetingDialog(meeting: any, person: any, event: Event) {
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;

   
    dialogConfig.data = {
      dialogTitle: 'Meeting Detail',
      data: meeting,
      person: person,
      isMeeting: true,
    };
    const dialogRef = this.dialog.open(
      McvTimeLineViewMeetingComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
      console.log('Dialog was closed');
    });
    return dialogRef;
  }

  openTravelTimeDialog(task: any, person: any, event: Event) {
    event.stopPropagation(); // Prevent event bubbling

    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;

   

    // Determine if it's an inspection
    const isInspection =
      task.wfStageCode === this.config.TASK_STAGE_INSPECTION_TRAVEL_TIME;

    // Set dialog data
    dialogConfig.data = {
      dialogTitle: 'Travel Time Detail',
      data: task,
      person,

      isTravelTime: true,
    };
    if (isInspection) {
      const dialogRef = this.dialog.open(
        McvTimeLineViewInspectionComponent,
        dialogConfig
      );
      dialogRef.afterClosed().subscribe(() => {
        console.log('Dialog was closed');
      });

      return dialogRef;
    } else {
      const dialogRef = this.dialog.open(
        McvTimeLineViewMeetingComponent,
        dialogConfig
      );
      dialogRef.afterClosed().subscribe(() => {
        console.log('Dialog was closed');
      });

      return dialogRef;
    }
    // Open the appropriate dialog component
  }
  openPrepareReportMinutesDialog(task: any, person: any, event: Event) {
    event.stopPropagation(); // Prevent event bubbling

    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;

  

    // Determine if it's an inspection
    const isInspection =
      task.wfStageCode === this.config.TASK_STAGE_INSPECTION_PREPARE_REPORT;

    // Set dialog data
    dialogConfig.data = {
      dialogTitle: 'Meeting Prepare Report Detail',
      data: task,
      person,

      isPrepareReport: true,
    };
    if (isInspection) {
      const dialogRef = this.dialog.open(
        McvTimeLineViewInspectionComponent,
        dialogConfig
      );
      dialogRef.afterClosed().subscribe(() => {
        console.log('Dialog was closed');
      });

      return dialogRef;
    } else {
      const dialogRef = this.dialog.open(
        McvTimeLineViewMeetingComponent,
        dialogConfig
      );
      dialogRef.afterClosed().subscribe(() => {
        console.log('Dialog was closed');
      });

      return dialogRef;
    }
    // Open the appropriate dialog component
  }

  // getPrepareReportTooltip(prepareReportMinutes: any): string {
  //   if (
  //     prepareReportMinutes.wfStageCode ===
  //     this.config.TASK_STAGE_INSPECTION_PREPARE_REPORT
  //   ) {
  //     return 'Inspection Prepare Report';
  //   } else if (
  //     prepareReportMinutes.wfStageCode ===
  //     this.config.TASK_STAGE_MEETING_PREPARE_MINUTES
  //   ) {
  //     return 'Meeting Prepare Minutes';
  //   }
  //   return 'Unknown Stage';
  // }

  // getTravelTimeTooltip(wfStageCode: string): string {
  //   if (wfStageCode === this.config.TASK_STAGE_MEETING_CLOSE) {
  //     return 'Meeting Travel Time';
  //   } else if (wfStageCode === this.config.TASK_STAGE_INSPECTION_CLOSE) {
  //     return 'Inspection Travel Time';
  //   }
  //   return 'Unknown Travel Time';
  // }

  updateScroll(scrollElement: string) {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  
    this.scrollTimeout = setTimeout(() => {
      if (scrollElement.toLowerCase() === 'scrollone') {
        this.scrollTwo.nativeElement.scrollLeft = this.scrollOne.nativeElement.scrollLeft;
      } else {
        this.scrollOne.nativeElement.scrollLeft = this.scrollTwo.nativeElement.scrollLeft;
      }
    }, 10); // Adjust delay if needed (10ms is usually optimal)
  }
  

  async toggleWeek(index: number) {
    this.mcvTimelineEvents = [];
    // Update current date by adding/subtracting weeks
    this.currentDate = new Date(this.currentDate);
    this.currentDate.setDate(this.currentDate.getDate() + index * 7);

    // Get updated week start and end dates
    if (this.selectedDayMode && this.selectedDayMode >= 6) {
      this.rangeStartDate = this.dateUtility.getWeekStart(this.currentDate);
      this.rangeEndDate = this.dateUtility.getWeekEnd(this.currentDate);
    }



    // Determine if the current week is changed
    const now = new Date();
    const currentWeekStart = this.dateUtility.getWeekStart(now);
    const currentWeekEnd = this.dateUtility.getWeekEnd(now);

    this.isWeekChanged = !(
      this.rangeStartDate.getTime() === currentWeekStart.getTime() &&
      this.rangeEndDate.getTime() === currentWeekEnd.getTime()
    );
    // Reinitialize the timeline
    this.initializeTimeline();
    // Trigger necessary UI updates
    // this.updateView();
    // await this.getContactList();
    await this.getTaskList();
    await this.getInspectionList();
    await this.getLeaveList();
    await this.getMeetingList();
    await this.getTravelTime();
     this.getActivePackage();
     this.getAssociatePartnerList()
    this.addTaskMetrics();
    this.calculateTaskTopPositions();
    this.cdr.detectChanges();
 
  }

  isCurrentDay(day: Date): boolean {
    const dayCopy = new Date(day);
    const today = new Date();

    return dayCopy.setHours(0, 0, 0, 0) === today.setHours(0, 0, 0, 0);
  }
  calculateTotalAssignedMhr(person: any): string {
    const rangeStart = new Date(this.rangeStartDate);
    rangeStart.setHours(0, 0, 0, 0);

    const rangeEnd = new Date(this.rangeEndDate);
    rangeEnd.setHours(23, 59, 59, 999);

    // Filter the list based on the normalized date range and matching person ID
    const filteredList = this.mcvTimelineEvents.filter((task) => {
      const taskStartDate = new Date(task.startDate);
      taskStartDate.setHours(0, 0, 0, 0);

      const taskEndDate = new Date(task.endDate);
      taskEndDate.setHours(23, 59, 59, 999);

      return (
        task.contactID === person.id &&
        taskStartDate.getTime() >= rangeStart.getTime() &&
        taskEndDate.getTime() <= rangeEnd.getTime()
      );
    });

    // Sum up the AssignedMhr from the filtered tasks, ensuring it's treated as a number
    const totalAssignedMhr = filteredList.reduce(
      (sum: number, task) => sum + (Number(task.mHr) || 0),
      0
    );

    // Return as a string with 2 decimal places
    return totalAssignedMhr.toFixed(2);
  }


  getAssociatePartnerList(){
    let associateSet = new Set<number>();
    let partnerSet = new Set<number>();
    this.mcvTimelineEvents.forEach((item) => {
      if (item.package) {
        // Store unique associates
        item.package.associate?.forEach((assoc: { id: number; name: string }) => {
          if (!associateSet.has(assoc.id)) {
            associateSet.add(assoc.id);
            this.associatesList.push(assoc); // Store unique associate
          }
        });

        // Add unique partners
        item.package.partner?.forEach((part: { id: number; name: string }) => {
          if (!partnerSet.has(part.id)) {
            partnerSet.add(part.id);
            this.partnerList.push(part); // Store unique partner
          }
        });
      }
    });
    this.partnerListEmitter.emit(this.partnerList)
    this.associateListEmitter.emit(this.associatesList)
    console.log('this.associatesList',this.associatesList)
    console.log('this.partnerList',this.partnerList)
  }

  filterAndUpdateTimelineEvents(filterValues: { partners: any[] | null; associates: any[] | null }) {
    // If both partners and associates are null, restore the original event list
    if (!filterValues.partners && !filterValues.associates) {
      this.mcvTimelineEvents = [...this.eventList]; // Restore from eventList
      return;
    }
  
    this.mcvTimelineEvents = this.eventList.filter(event => {
      // Apply filtering only for events with serviceType 'Studio Work'
      if (event.serviceType !== 'Studio Work') return true;
  
      if (!event.package) return false; // Skip events without a package
  
      const eventPartners = event.package.partner || [];
      const eventAssociates = event.package.associate || [];
  
      const hasMatchingPartner = filterValues.partners
        ? eventPartners.some((partner: any) => filterValues.partners!.some(fp => fp.id === partner.id))
        : false;
  
      const hasMatchingAssociate = filterValues.associates
        ? eventAssociates.some((associate: any) => filterValues.associates!.some(fa => fa.id === associate.id))
        : false;
  
      // If both partners and associates are provided, apply AND condition
      if (filterValues.partners && filterValues.associates) {
        return hasMatchingPartner && hasMatchingAssociate;
      }
  
      // If only partners or associates are provided, use OR condition
      return hasMatchingPartner || hasMatchingAssociate;
    });
  }
  
  
}
