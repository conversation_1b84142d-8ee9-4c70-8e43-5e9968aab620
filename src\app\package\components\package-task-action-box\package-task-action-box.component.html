<div class="panel" *ngIf="!isReadOnly">
  <div class="panel-header">
    <div class="panel-header-title">Task Action</div>
  </div>
  <div class="panel-body">

    <div class="row mb-1" *ngIf="task.stageIndex === taskConstants.STAGEINDEX.FINALREVIEW && task.assessments">
      <!-- <div class="col-sm-3 col-6">
          <small class="text-muted">Assigned MHr</small>
          <h4>{{ previousTask?.mHrAssigned | number:'2.0-2' }}</h4>
        </div>

        <div class="col-sm-3 col-6">
          <small class="text-muted">Consumed MHr</small>
          <h4>{{ previousTask?.mHrConsumed | number:'2.0-2' }}</h4>
        </div>

        <div class="col-sm-3 col-6">
          <small class="text-muted">Assessment Points</small>
          <h4>{{ previousTask?.assessmentPoints }}/<small>10</small></h4>
        </div>

        <div class="col-sm-3 col-6">
          <small class="text-muted">Assessed Mhr</small>
          <h4>{{ previousTask?.mHrAssessed | number:'2.0-2' }}</h4>
        </div> -->
      <div class="col-sm-12">
        <small class="text-muted">Assessment Points</small>
        <h4>{{ task?.assessmentPoints }}/<small>10</small></h4>
      </div>
    </div>

    <form [formGroup]="form" autocomplete="off" novalidate *ngIf="task.outcomeFlag == 0">
      <mat-form-field appearance="outline" class="col-md-12">
        <mat-label>Comment</mat-label>
        <textarea aria-label="comment" matInput formControlName="comment" cdkTextareaAutosize
          #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="2" cdkAutosizeMaxRows="5"></textarea>
        <mat-error>{{ getErrorMessage(f.comment) }}</mat-error>
      </mat-form-field>
    </form>

    <app-mcv-file-upload [uploaderConfig]="blobConfig" [showPaste]="true" (upload)="onUpload($event)"
      *ngIf="blobConfig && !isReadOnly">
    </app-mcv-file-upload>

    <ul class="mcv-file-list">

      <li *ngFor="let file of task?.attachments">
        <mcv-file [size]="file.size" [filename]="file.filename" [url]="file.thumbUrl || file.url"
          [contentType]="file.contentType" [showPreview]="true" [showRemove]="true" [downloadUrl]="file.url"
          [singlePreview]="true" [isVerticalThumb]="true" (delete)="onDeleteAttachment(file)">
        </mcv-file>
      </li>

    </ul>

    <small class="text-danger" *ngIf=" canSubmit && task.stageIndex === taskConstants.STAGEINDEX.PACKAGE">
      All Studio Tasks should be completed & approved before submitting!
    </small>
    <div class="inline-list inline-list-justify-right">

      <ng-container *ngIf="task.stageIndex === taskConstants.STAGEINDEX.FINALREVIEW">
        <button mat-raised-button matTooltip="Approve" aria-label="Submit"
          [disabled]="isReadOnly || task.statusFlag == 1" (click)="onAssessmentClick(task)">
          Assessment
        </button>
        <button mat-raised-button color="primary" matTooltip="Approve" aria-label="Submit"
          [disabled]="isReadOnly || task.statusFlag == 1" (click)="onApproveTask()">
          Approve
        </button>

        <button mat-raised-button matTooltip="Reject" aria-label="Submit" color="warn"
          [disabled]="isReadOnly || task.statusFlag == 1" (click)="onRejectTask()">
          Reject
        </button>
      </ng-container>

      <button mat-raised-button matTooltip="Assign Studio Task" aria-label="Assign Studio Task"
        (click)="onSecondaryAction(0)"
        *ngIf="!isReadOnly && task.statusFlag != 1 && task.stageIndex === taskConstants.STAGEINDEX.PACKAGE && showAssignTaskButton">
        Assign Studio Task
      </button>
      <small *ngIf="!showAssignTaskButton">You have exceeded your package Vhr.</small>
      <button mat-raised-button color="primary" matTooltip="Submit" aria-label="Submit" (click)="onSubmitTask(false)"
        [attr.disabled]="canSubmit"
        *ngIf="!isReadOnly && task.statusFlag != 1 && task.stageIndex === taskConstants.STAGEINDEX.PACKAGE">
        Submit
      </button>

      <button mat-raised-button color="primary" matTooltip="Send" aria-label="Send" (click)="onSubmitTask(true)"
        *ngIf="!isReadOnly && task.statusFlag != 1 && task.stageIndex === taskConstants.STAGEINDEX.SUBMISSION">
        Send
      </button>

      <button mat-raised-button color="primary" matTooltip="Start" aria-label="Start" (click)="onStartTask()"
        *ngIf="!isReadOnly && task.stageIndex === taskConstants.STAGEINDEX.BRAINSTORMING && (task.statusFlag === 0 || task.statusFlag===3)">
        Start
      </button>
      <button mat-raised-button matTooltip="Pause" aria-label="Submit" (click)="onPauseTask()"
        *ngIf="!isReadOnly && task.stageIndex === taskConstants.STAGEINDEX.BRAINSTORMING && task.statusFlag === 2">
        Pause
      </button>
      <button mat-raised-button color="primary" matTooltip="Complete" aria-label="Submit" (click)="onCompleteTask()"
        *ngIf="!isReadOnly && task.stageIndex === taskConstants.STAGEINDEX.BRAINSTORMING && (task.statusFlag === 0 || task.statusFlag===2)">
        Complete & Submit
      </button>
    </div>
  </div>
</div>