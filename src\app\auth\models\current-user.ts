
import { Contact } from "../../contact/models/contact";

export class CurrentUserStore
{
  username!: string;
  isChangePassword: boolean = false;
  agreementFlag: number = 0;
  token!: string;
  roles: string[] = [];
  refreshToken!: string;
  isAuth: boolean = false;
  contact!: Contact;
  sessionID!: string;
  isOTPRequired: boolean = false;

  // Token expiration tracking
  tokenExpiresAt?: Date;
  refreshTokenExpiresAt?: Date;
}
