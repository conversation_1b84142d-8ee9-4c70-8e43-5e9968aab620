import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MeetingAgendaApiService } from 'src/app/meeting-agenda/services/meeting-agenda-api.service';
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { AuthService } from 'src/app/auth/services/auth.service';
import { AppConfig } from 'src/app/app.config';
import { MeetingListItem } from '../../models/meeting-list-item-dto';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-cockpit-my-agenda-preview-table',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatOptionModule,
    MatCheckboxModule,
    MatDialogModule,
    MatTooltipModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
  ],
  templateUrl: './cockpit-my-agenda-preview-table.component.html',
  styleUrls: ['./cockpit-my-agenda-preview-table.component.scss'],
})
export class CockpitMyAgendaPreviewTableComponent implements OnInit {
  isLoading: boolean = false;
  dataList: any;
  originalDataList: any;
  totalRecordsCount = 0;

  pageSize = 20;
  currentPage = 0;
  meetingAgendaService = inject(MeetingAgendaApiService);
  authService = inject(AuthService);
  config = inject(AppConfig);
  isSorted: boolean = false;
  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    dueDate: '', // Initial state (no sorting applied)
  };
  selectedMeetingTitle: string[] = [];
  meetingTitleSearch: string = '';
  filteredMeetingTitle: string[] = [];
  distinctMeetingTitle: any[] = [];

  selectedTitle: string[] = [];
  titleSearch: string = '';
  filteredTitle: string[] = [];
  distinctTitle: any[] = [];
  selectedActionBy: string[] = [];
  actionBySearch: string = '';
  filteredActionBy: string[] = [];
  distinctActionBy: any[] = [];

  constructor(
    private dialog: MatDialogRef<CockpitMyAgendaPreviewTableComponent>,
    private utility: UtilityService
  ) {}
  get MEETING_TYPEFLAG_MEETING() {
    return this.meetingAgendaService.MEETING_TYPEFLAG_MEETING;
  }
  get MEETING_TYPEFLAG_CNOTE() {
    return this.meetingAgendaService.MEETING_TYPEFLAG_CNOTE;
  }
  get MEETING_STATUSFLAG_SENT() {
    return this.meetingAgendaService.MEETING_STATUSFLAG_SENT;
  }
  get MEETING_AGENDA_STATUSFLAG_PENDING() {
    return this.meetingAgendaService.MEETING_AGENDA_STATUSFLAG_PENDING;
  }
  myAgendaPagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 10,
    filters: [
      {
        key: 'statusFlag',
        value: this.MEETING_AGENDA_STATUSFLAG_PENDING.toString(),
      },
      {
        key: 'meetingStatusFlag',
        value: this.MEETING_STATUSFLAG_SENT.toString(),
      },
      { key: 'isForwarded', value: 'false' },
      { key: 'IsVersion', value: 'false' },
      {
        key: 'partnerorassociateContactID',
        value: this.authService.currentUserStore
          ? this.authService.currentUserStore.contact.id.toString()
          : '0',
      },
      { key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT },
      // { key: 'entity', value: 'No-ENTITY' },
      { key: 'typeFlag', value: this.MEETING_TYPEFLAG_MEETING.toString() },
      { key: 'typeFlag', value: this.MEETING_TYPEFLAG_CNOTE.toString() },
      // { key: 'usersOnly', value: 'true' },
    ],
    searchKey: null,
    sort: 'dueDate',
    route: '',
    showAll: false,
    showAssigned: false,
    groupBy: [],
    keyPropertyName: '',
  });

  async ngOnInit() {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    await this.getDataList(this.currentPage, this.pageSize);
    console.log('this.dataList', this.dataList);
  }
  onClose(e: any) {
    this.dialog.close(e);
  }

  async getDataList(currentPage: number, pageSize: number) {
    this.isLoading = true;
  
    const data = await firstValueFrom(
      this.meetingAgendaService.getCockpitAgendas(
        currentPage,
        pageSize,
        this.myAgendaPagedListConfig.filters,
        this.myAgendaPagedListConfig.searchKey,
        this.myAgendaPagedListConfig.sort,
        true
      )
    );
    this.dataList = [];
    this.dataList = data.list;
    this.totalRecordsCount = data.total;
    this.extractDistinctValues()
    this.originalDataList = [...this.dataList];
  }

  get totalPages() {
    return Math.ceil(this.totalRecordsCount / this.pageSize);
  }

  goToPreviousPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.getDataList(this.currentPage, this.pageSize);
    }
  }

  goToNextPage() {
    if (this.currentPage + 1 < this.totalPages) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pageSize);
    }
  }

  toggleSelectAll(filterType: 'meetingTitle' | 'title'  | 'actionBy'): void {
    const distinctValues = this.getDistinctValues(filterType);

    switch (filterType) {
      case 'meetingTitle':
        this.selectedMeetingTitle = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'title':
        this.selectedTitle = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'actionBy':
        this.selectedActionBy = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
    
    }

    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data

    // Apply the filter for Assigned To if selected
    if (this.selectedMeetingTitle.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedMeetingTitle.includes(item.meetingTitle)
      );
    }

    // Apply the filter for Assigned By if selected
    if (this.selectedTitle.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedTitle.includes(item.title)
      );
    }
    if (this.selectedActionBy.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedActionBy.includes(item.actionBy)
      );
    }

    this.dataList = filteredList;
  }

  isAllSelected(filterType: 'meetingTitle' | 'title' | 'actionBy'): boolean {
    const distinctValues = this.getDistinctValues(filterType);

    switch (filterType) {
      case 'meetingTitle':
        return (
          this.selectedMeetingTitle.length === distinctValues.length &&
          this.selectedMeetingTitle.length > 0
        );
      case 'title':
        return (
          this.selectedTitle.length === distinctValues.length &&
          this.selectedTitle.length > 0
        );
      case 'actionBy':
        return (
          this.selectedActionBy.length === distinctValues.length &&
          this.selectedActionBy.length > 0
        );
     
      default:
        return false;
    }
  }

  getDistinctValues(filterType: 'meetingTitle' | 'title'  | 'actionBy'): string[] {
    switch (filterType) {
      case 'meetingTitle':
        return this.distinctMeetingTitle;
      case 'title':
        return this.distinctTitle;
      case 'actionBy':
        return this.distinctActionBy;

      default:
        return [];
    }
  }

  extractDistinctValues() {
    this.distinctMeetingTitle = [
      ...new Set(this.dataList.map((item: any) => item.meetingTitle)),
    ];

    this.filteredMeetingTitle = [...this.distinctMeetingTitle];
    this.distinctTitle = [
      ...new Set(this.dataList.map((item: any) => item.title)),
    ];
   
    this.filteredTitle = [...this.distinctTitle];
    this.distinctActionBy = [
      ...new Set(this.dataList.map((item: any) => item.actionBy)),
    ];
    this.filteredActionBy = [...this.distinctActionBy];
    console.log('this.distinctMeetingTitle', this.distinctMeetingTitle);
    console.log(' this.filteredMeetingTitle', this.filteredMeetingTitle);
  }

  toggleSelection(value: string, type: string) {
    if (type === 'meetingTitle') {
      this.selectedMeetingTitle.includes(value)
        ? this.selectedMeetingTitle.splice(
            this.selectedMeetingTitle.indexOf(value),
            1
          )
        : this.selectedMeetingTitle.push(value);
    } else if (type === 'title') {
      this.selectedTitle.includes(value)
        ? this.selectedTitle.splice(this.selectedTitle.indexOf(value), 1)
        : this.selectedTitle.push(value);
    }
    else if (type === 'actionBy') {
      this.selectedActionBy.includes(value)
        ? this.selectedActionBy.splice(this.selectedActionBy.indexOf(value), 1)
        : this.selectedActionBy.push(value);
    }
    this.applyFilters();
  }

  clearSearch(event: Event) {
    this.meetingTitleSearch = '';
    this.titleSearch = '';
    // this.assignedBySearch = '';
    this.actionBySearch = '';
    this.filterDistinctMeetingTitle(); // Reset options
    this.filterDistinctTitle(); // Reset options
    this.filterDistinctActionBy(); // Reset options
  }
  filterDistinctMeetingTitle() {
    const searchLower = this.meetingTitleSearch.toLowerCase();
    this.filteredMeetingTitle = this.distinctMeetingTitle.filter((meetingTitle) =>
      meetingTitle.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctTitle() {
    const searchLower = this.titleSearch.toLowerCase();
    this.filteredTitle = this.distinctTitle.filter((title) =>
      title.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctActionBy() {
    const searchLower = this.actionBySearch.toLowerCase();
    this.filteredActionBy = this.distinctActionBy.filter((actionBy) =>
      actionBy.toLowerCase().includes(searchLower)
    );
  }

  sortData(column: any | '') {
    if (column === '') {
      // Reset to default (original data) but apply the filters again
      const dataToFilter = this.originalDataList;
  
      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item:any) => {
        return (
          (this.selectedMeetingTitle.length === 0 || this.selectedMeetingTitle.includes(item.meetingTitle)) &&
          (this.selectedTitle.length === 0 || this.selectedTitle.includes(item.title)) &&
          (this.selectedActionBy.length === 0 || this.selectedActionBy.includes(item.actionBy))  
        
        );
      });
  
      // Reset the sort state to default
      this.sortState = { 
        activeColumn: '',
        startDate: '',
        dueDate: '',
        completedDate: ''
      };
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if no sorting is selected
    }
  
    // If the clicked column is already the active column, cycle through the three states
    const currentSort = this.sortState[column];
  
    if (currentSort === 'newFirst') {
      // If it's 'newFirst', change to 'oldFirst'
      this.sortState[column] = 'oldFirst';
    } else if (currentSort === 'oldFirst') {
      // If it's 'oldFirst', reset to default (no sorting)
      this.sortState[column] = '';
      this.sortState.activeColumn = '';
      const dataToFilter = this.originalDataList;
  
      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item:any) => {
        return (
           (this.selectedMeetingTitle.length === 0 || this.selectedMeetingTitle.includes(item.meetingTitle)) &&
          (this.selectedTitle.length === 0 || this.selectedTitle.includes(item.title)) &&
          (this.selectedActionBy.length === 0 || this.selectedActionBy.includes(item.actionBy))  
       
        );
      });
  
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if reset is selected
    } else {
      // If no sorting is applied, set it to 'newFirst' (ascending order)
      this.sortState[column] = 'newFirst';
    }
  
    // Set the active column
    this.sortState['activeColumn'] = column;
  
    // Reset other columns' sort state to '' (no sort)
    for (let key in this.sortState) {
      if (key !== column && key !== 'activeColumn') {
        this.sortState[key] = ''; // Reset other columns' sort state to no sort
      }
    }
  
    // Sorting logic: Compare dates for the active column
    const sortedData = [...this.dataList].sort((a, b) => {
      const dateA = new Date(a[column] as string);
      const dateB = new Date(b[column] as string);
  
      if (this.sortState[column] === 'newFirst') {
        return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
      } else if (this.sortState[column] === 'oldFirst') {
        return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
      }
      return 0; // If no sorting, return unchanged order
    });
  
    // Update the dataList with the sorted data
    this.dataList = sortedData;
    this.isSorted = true; // Set isSorted to true when sorting is applied
  }

  clearSelection(type: string) {
    if (type === 'meetingTitle') {
        this.selectedMeetingTitle = [];
    } else if (type === 'title') {
        this.selectedTitle = [];
    } 
    else if (type === 'actionBy') {
        this.selectedActionBy = [];
    }
    this.applyFilters(); // Apply filters after clearing the selection
}

resetFilter() {
  this.selectedMeetingTitle = [];
  this.selectedTitle = [];
  this.selectedActionBy = [];
  this.dataList = [...this.originalDataList]; // Restore full list


}


}
