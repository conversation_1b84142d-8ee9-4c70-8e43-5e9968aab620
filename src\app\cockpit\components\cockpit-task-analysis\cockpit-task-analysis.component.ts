import { DecimalPipe, <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormGroup, FormControl, ReactiveFormsModule, FormsModule } from "@angular/forms";
import { debounceTime, distinctUntilChanged } from "rxjs/operators";
import { AppPermissions } from "src/app/app.permissions";
import { BannerData } from "src/app/models/banner-data";
import { PagedListConfig } from "src/app/mcv-core/models/paged-list-config.model";
import { WFTaskAnalysis } from "src/app/models/wf-task-analysis";
import { AuthService } from "src/app/auth/services/auth.service";
import { UtilityService } from "src/app/shared/services/utility.service";
import { WFTaskApiService } from "src/app/wf-task/services/wf-task-api.service";
import { MatTableDataSource } from "@angular/material/table";
import { FilterToggleDirective } from "../../../shared/directives/filter-toggle.directive";
import { MatButtonModule } from "@angular/material/button";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { FooterComponent } from "../../../shared/components/footer/footer.component";
import { CockpitTaskAnalysisListItemComponent } from "../cockpit-task-analysis-list-item/cockpit-task-analysis-list-item.component";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatIconModule } from "@angular/material/icon";
import { CockpitBannerComponent } from "../cockpit-banner/cockpit-banner.component";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { CockpitTaskAnalysisDialogComponent } from "../cockpit-task-analysis-dialog/cockpit-task-analysis-dialog.component";
import { MatMenuModule } from "@angular/material/menu";
import { MatOptionModule } from "@angular/material/core";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { firstValueFrom } from "rxjs";
import{McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
    selector: "app-cockpit-task-analysis",
    templateUrl: "./cockpit-task-analysis.component.html",
    styleUrls: ["./cockpit-task-analysis.component.scss"],
    standalone: true,
    imports: [
    NgIf,
    NgClass,
    NgFor,
    MatIconModule,
    MatTooltipModule,
    FooterComponent,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatButtonModule,
    FilterToggleDirective,
    DecimalPipe,
    DatePipe,
    //Components
    CockpitBannerComponent,
    CockpitTaskAnalysisListItemComponent,
    MatMenuModule,
    FormsModule,
    MatOptionModule,
    MatCheckboxModule,
    McvFilterSidenavComponent,
],
})
export class CockpitTaskAnalysisComponent implements OnInit {
  pagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 30,
    filters: [
      {
        key: "contactID",
        value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '0',
      },
      { key: "statusFlag", value: '1' },
      { key: "rangeStart", value: this.utility.getMonthStart().toISOString() },
      { key: "rangeEnd", value: this.utility.getMonthEnd().toISOString() },
    ],
    searchKey: null,
    sort: "completedDate desc",
    route: "",
    showAll: false,
    showAssigned: false,
    groupBy: [],
    keyPropertyName: "",
  });
  
  //Total 
  totalDelay: number = 0;
  totalMHrAssigned: number = 0;
  totalMHrConsumed: number = 0;
  totalMHrAssessed: number = 0;
  totalVHrCosumed: number = 0;
  totalVHrAssessed: number = 0;

  assignedToSearch: string = '';
  assignedtaskSearch: string = '';
  assignedtitleSearch: string = '';
  filteredAssignedTo: string[] = [];
  filteredTask: string[] = [];
  filteredTitle: string[] = [];
  distinctAssignedTo: string[] = [];
  distinctTask: string[] = [];
  distinctTitle: string[] = [];
  selectedAssignedTo: string[] = [];
  selectedTask: string[] = [];
  selectedTitle: string[] = [];
  originalDataList: any[] = [];
  searchKey?: string;
  isSorted: boolean = false;
  sortState: { 
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
  
    startDate: '',
    dueDate: '',
    completedDate: '',
    assessmentPoints: '',
    mHrAssigned: '',
    mHrConsumed: '',
    mHrAssessed: '',
    vHrConsumed: '',
    vHrAssessed: '',
    delay: ''
  };

  @Input("config") set configValue(value: PagedListConfig) {
    if (value) {
      this.pagedListConfig = value;
      console.log(this.pagedListConfig);
    }
  }

  myTaskAnalysisColumns: string[] = [
    "start",
    "due",
    "completed",
    "delay",
    "task",
    "title",
    "mHrAssigned",
    "mHrConsumed",
    "points",
    "mHrAssessed",
  ];
  dataList: WFTaskAnalysis[] = [];
  total!: WFTaskAnalysis;
  isLoading: boolean = false;
  totalRecordsCount = 0;
  totalPages = 0;
  // pageSize = 50;
  currentPage = 0;
  isMobileView: boolean = false;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>(); //For TableData
  isExpanded: boolean[] = [];

  @Output() listLoad = new EventEmitter<any>();
  dateFilters!: FormGroup;
  searchFilter = new FormControl<any>(null);
  showAll: boolean = false;
  isPermissionShowAll: boolean = false;
  isPermissionExcel: boolean = false;

  remunerationData!: BannerData;
  amhrData!: BannerData;
  vhrData!: BannerData;
  expectedVhrData!: BannerData;
  item: any;

  constructor(
    private authService: AuthService,
    private service: WFTaskApiService,
    private utility: UtilityService,
    private permissions: AppPermissions,
    private decimalPipe: DecimalPipe,
    private currencyPipe: CurrencyPipe,
    private datePipe: DatePipe,
    private dialog: MatDialog
  ) { }

  ngOnInit() {
    this.isMobileView = this.utility.isMobileView;
    this.isPermissionShowAll = this.authService.isInAnyRole([
      this.permissions.TASK_ANALYSIS_SPECIAL_SHOW_ALL,
    ]);
    this.isPermissionExcel = this.authService.isInAnyRole([
      this.permissions.TASK_ANALYSIS_SPECIAL_EXCEL,
    ]);
    this.buildForm();

    this.getVHrData(
      this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0,
      this.utility.getMonthStart(),
      this.utility.getMonthEnd()
    );

    this.refresh();
  }

  buildForm() {
    this.dateFilters = new FormGroup({
      start: new FormControl(this.utility.getMonthStart()),
      end: new FormControl(this.utility.getMonthEnd()),
    });

    this.dateFilters.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.pagedListConfig.filters = this.pagedListConfig.filters.filter(
            (x) => x.key !== "rangeStart" && x.key != "rangeEnd"
          );
          this.pagedListConfig.filters.push({
            key: "rangeStart",
            value: value.start.toISOString(),
          });
          this.pagedListConfig.filters.push({
            key: "rangeEnd",
            value: value.end.toISOString(),
          });

          this.search();
          this.getVHrData(this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0, new Date(value.start), new Date(value.end));
        }
      });

    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.pagedListConfig.searchKey = value;
          this.searchKey = value;
          this.search();
        }
      });
  }

  getVHrData(contactID: number, start: Date, end: Date) {
    const _filters = [
      { key: "contactID", value: contactID.toString() },
      { key: "rangeStart", value: start.toISOString() },
      { key: "rangeEnd", value: end.toISOString() },
    ];

    this.service.getVHrAnalysis(_filters).subscribe((data) => {
      if (data && data.length != 0) {
        this.remunerationData = new BannerData();
        this.remunerationData.label1 = "My Remuneration";
        const _label2 = this.datePipe.transform(
          this.utility.getMonthStart(),
          "MMM y"
        );
        if (_label2) {
          this.remunerationData.label2 = _label2
        }
        const _currency = this.currencyPipe.transform(
          data[0].remuneration,
          "INR",
          "symbol",
          "1.2"
        );
        if (_currency) {
          this.remunerationData.value = _currency
        }
        this.remunerationData.bannerClass = "primary";

        this.amhrData = new BannerData();
        this.amhrData.label1 = "My Assessed MHr";
        this.amhrData.label2 = this.datePipe.transform(
          this.utility.getMonthStart(),
          "MMM y"
        ) ?? '';
        this.amhrData.value = this.decimalPipe.transform(data[0].mHr, "2.2") ?? '';

        this.vhrData = new BannerData();
        this.vhrData.label1 = "My VHr";
        this.vhrData.label2 = this.datePipe.transform(
          this.utility.getMonthStart(),
          "MMM y"
        ) ?? '';
        this.vhrData.value = this.decimalPipe.transform(data[0].vHr, "2.2") ?? '';

        this.expectedVhrData = new BannerData();
        this.expectedVhrData.label1 = "Expected VHr";
        const _label = this.datePipe.transform(
          this.utility.getMonthStart(),
          "MMM y"
        );
        if (_label) {
          this.expectedVhrData.label2 = _label;
        }
        const _decimal = this.decimalPipe.transform(
          data[0].expectedVHr,
          "2.2"
        );
        if (_decimal) {
          this.expectedVhrData.value = _decimal;
        }
        this.expectedVhrData.bannerClass = "secondary";
      }
    });
  }

  async getDataList(currentPage: number, pageSize: number) {
    this.isLoading = true;
    // this.getDataTotal();

    this.dataList = await firstValueFrom(this.service
      .getAnalysis(
        this.pagedListConfig.filters,
        this.pagedListConfig.searchKey,
        this.pagedListConfig.sort,
        this.pagedListConfig.showAll
      ))
      this.listLoad.emit({ totalRecordsCount: this.totalRecordsCount });
      this.dataSource = new MatTableDataSource<any>(this.dataList);
      this.isLoading = false;
      this.getDataTotal();
      this.getTotal()
      this.extractDistinctValues();
      this.originalDataList = [...this.dataList];
  }

  extractDistinctValues() {
    this.distinctAssignedTo = [...new Set(this.dataList.map(item => item.person))];
    this.distinctTask = [...new Set(this.dataList.map(item => item.taskTitle))];
    this.distinctTitle = [...new Set(this.dataList.map(item => item.entityTitle))];
    this.filteredAssignedTo = [...this.distinctAssignedTo]; 
    this.filteredTask = [...this.distinctTask]; 
    this.filteredTitle = [...this.distinctTitle]; 
  }

  getDataTotal() {
    this.service
      .getAnalysisTotal(
        this.pagedListConfig.filters,
        this.pagedListConfig.searchKey,
        this.pagedListConfig.sort
      )
      .subscribe((data) => (this.total = data));
  }

  getTotal() {
    this.totalDelay = 0;
    this.totalMHrAssigned = 0;
    this.totalMHrAssessed = 0;
    this.totalMHrConsumed = 0;
    this.totalVHrCosumed = 0;
    this.totalVHrAssessed = 0;
    this.dataList.forEach(x => {
      this.totalDelay += x.delay;
      this.totalMHrAssigned += x.mHrAssigned;
      this.totalMHrAssessed += x.mHrAssessed;
      this.totalMHrConsumed += x.mHrConsumed;
      this.totalVHrCosumed += x.vHrConsumed;
      this.totalVHrAssessed += x.vHrAssessed;
    });
  }

  loadMoreRecords() {
    if (
      this.currentPage * this.pagedListConfig.pageSize <
      this.totalRecordsCount
    ) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
    }
  }

  search() {
    this.currentPage = 0;
    this.dataList = [];
    this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
  }
  refresh() {
    this.pagedListConfig.searchKey = null;
    this.searchFilter.setValue(null);
    this.dataList = [];
    this.getDataList(0, (this.currentPage + 1) * this.pagedListConfig.pageSize);
  }

  onToggleShowAll() {
    this.showAll = !this.showAll;
    this.pagedListConfig.showAll = this.showAll;
    if (this.showAll) {
      this.pagedListConfig.filters = this.pagedListConfig.filters.filter(
        (x) => x.key !== "contactID"
      );
    } else {
      this.pagedListConfig.filters.push({
        key: "contactID",
        value: this.authService.currentUserStore ? this.authService.currentUserStore.contact.id.toString() : '0',
      });
    }
    this.search();
  }

  onExportExcel() {
    this.service.exportAnalysisExcel(
      this.pagedListConfig.filters,
      this.pagedListConfig.searchKey,
      this.pagedListConfig.sort
    );
  }

  toggleExpand(i: number) {
    this.isExpanded[i] = !this.isExpanded[i];
  }

  onShowTaskDetails(task:WFTaskAnalysis){
    const _dialogConfig = new MatDialogConfig();
    _dialogConfig.autoFocus = true;
    _dialogConfig.disableClose = true;
    _dialogConfig.data = {
      task: task
    }

    const _dialogRef = this.dialog.open(CockpitTaskAnalysisDialogComponent,_dialogConfig);
    _dialogRef.afterClosed().subscribe(res =>{

    });
  }
  
  filterDistinctAssignedTo() {
    const searchLower = this.assignedToSearch.toLowerCase();
    this.filteredAssignedTo = this.distinctAssignedTo.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctTask() {
    const searchLower = this.assignedtaskSearch.toLowerCase();
    this.filteredTask = this.distinctTask.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctTitle() {
    const searchLower = this.assignedtitleSearch.toLowerCase();
    this.filteredTitle = this.distinctTitle.filter(person =>
      person.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: Event) {
    this.assignedToSearch = '';
    this.assignedtaskSearch = '';
    this.assignedtitleSearch = '';
    this.filterDistinctAssignedTo(); // Reset options

    this.filterDistinctTask(); // Reset options
    this.filterDistinctTitle(); // Reset options
  }

  toggleSelectAll(filterType: 'assignedTo'  | 'task' | 'title'): void {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'assignedTo':
        this.selectedAssignedTo = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;

      case 'task':
        this.selectedTask = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
      case 'title':
        this.selectedTitle = this.isAllSelected(filterType) ? [] : [...distinctValues];
        break;
    }
    
    this.applyFilters(); // Ensure filters update when selecting/deselecting all
  }

  getDistinctValues(filterType: 'assignedTo'  | 'task' | 'title'): string[] {
    switch (filterType) {
      case 'assignedTo':
        return this.distinctAssignedTo;
     
      case 'task':
        return this.distinctTask;
      case 'title':
        return this.distinctTitle;
      default:
        return [];
    }
  }

  isAllSelected(filterType: 'assignedTo'  | 'task' | 'title'): boolean {
    const distinctValues = this.getDistinctValues(filterType);
    
    switch (filterType) {
      case 'assignedTo':
        return this.selectedAssignedTo.length === distinctValues.length && this.selectedAssignedTo.length > 0;
    
      case 'task':
        return this.selectedTask.length === distinctValues.length && this.selectedTask.length > 0;
      case 'title':
        return this.selectedTitle.length === distinctValues.length && this.selectedTitle.length > 0;
      default:
        return false;
    }
  }

  toggleSelection(value: string, type: string) {
   
    if (type === 'assignedTo') {
      this.selectedAssignedTo.includes(value)
        ? this.selectedAssignedTo.splice(this.selectedAssignedTo.indexOf(value), 1)
        : this.selectedAssignedTo.push(value);
    } 
 
    else if (type === 'task') {
      this.selectedTask.includes(value)
        ? this.selectedTask.splice(this.selectedTask.indexOf(value), 1)
        : this.selectedTask.push(value);
    } else if (type === 'title') {
      this.selectedTitle.includes(value)
        ? this.selectedTitle.splice(this.selectedTitle.indexOf(value), 1)
        : this.selectedTitle.push(value);
    }
  
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if(this.searchKey){

      const searchLower = this.searchKey.toLowerCase();
  
     filteredList = filteredList.filter(item =>
      
        (item.person?.toLowerCase().includes(searchLower) || '') ||
        (item.taskTitle?.toLowerCase().includes(searchLower) || '') ||
        (item.entityTitle?.toLowerCase().includes(searchLower) || '')
      );
    }
  
    if (this.selectedAssignedTo.length > 0) {
      filteredList = filteredList.filter(item => this.selectedAssignedTo.includes(item.person));
    }
  
   
   

    if (this.selectedTask.length > 0) {
      filteredList = filteredList.filter(item => this.selectedTask.includes(item.taskTitle));
    }
  

    if (this.selectedTitle.length > 0) {
      filteredList = filteredList.filter(item => this.selectedTitle.includes(item.entityTitle));
    }

    
    this.dataList = filteredList;
    this.getTotal();
  }

 // Reset all filters
 resetFilter() {
  this.selectedAssignedTo = [];
  this.selectedTask = [];
  this.selectedTitle = [];
  this.dataList = [...this.originalDataList]; // Restore full list
  this.getTotal();
}



clearSelection(type: string) {
  if (type === 'assignedTo') {
      this.selectedAssignedTo = [];
  }  else if (type === 'task') {
      this.selectedTask = [];
  } else if (type === 'title') {
      this.selectedTitle = [];
  }
  
  this.applyFilters(); // Apply filters after clearing the selection
}

sortData(column: keyof WFTaskAnalysis | '') {
  if (column === '') {
    // Reset to default (original data) but apply the filters again
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
        (this.selectedAssignedTo.length === 0 || this.selectedAssignedTo.includes(item.person)) &&
        (this.selectedTitle.length === 0 || this.selectedTitle.includes(item.entityTitle)) && 
        (this.selectedTask.length === 0 || this.selectedTask.includes(item.taskTitle)) 
      
      );
    });

    // Reset the sort state to default
    this.sortState = { 
      activeColumn: '',
      startDate: '',
      dueDate: '',
      completedDate: '',
      assessmentPoints: '',
      mHrAssigned: '',
      mHrConsumed: '',
      mHrAssessed: '',
      vHrConsumed: '',
      vHrAssessed: '',
      delay: ''
    };
    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if no sorting is selected
  }

  // If the clicked column is already the active column, cycle through the three states
  const currentSort = this.sortState[column];

  if (currentSort === 'newFirst') {
    // If it's 'newFirst', change to 'oldFirst'
    this.sortState[column] = 'oldFirst';
  } else if (currentSort === 'oldFirst') {
    // If it's 'oldFirst', reset to default (no sorting)
    this.sortState[column] = '';
    this.sortState.activeColumn = '';
    const dataToFilter = this.originalDataList;

    // Apply filters to the original data list
    this.dataList = dataToFilter.filter((item:any) => {
      return (
        (this.selectedAssignedTo.length === 0 || this.selectedAssignedTo.includes(item.person)) &&
        (this.selectedTitle.length === 0 || this.selectedTitle.includes(item.entityTitle)) && 
        (this.selectedTask.length === 0 || this.selectedTask.includes(item.taskTitle)) 
      );
    });

    this.isSorted = false; // Set isSorted to false when sorting is reset
    return; // Exit the function if reset is selected
  } else {
    // If no sorting is applied, set it to 'newFirst' (ascending order)
    this.sortState[column] = 'newFirst';
  }

  // Set the active column
  this.sortState['activeColumn'] = column;

  // Reset other columns' sort state to '' (no sort)
  for (let key in this.sortState) {
    if (key !== column && key !== 'activeColumn') {
      this.sortState[key] = ''; // Reset other columns' sort state to no sort
    }
  }

  // Sorting logic: Compare dates for the active column
  const sortedData = [...this.dataList].sort((a, b) => {
    const dateA = new Date(a[column] as string);
    const dateB = new Date(b[column] as string);

    if (this.sortState[column] === 'newFirst') {
      return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
    } else if (this.sortState[column] === 'oldFirst') {
      return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
    }
    return 0; // If no sorting, return unchanged order
  });

  // Update the dataList with the sorted data
  this.dataList = sortedData;
  this.isSorted = true; // Set isSorted to true when sorting is applied
}



}
