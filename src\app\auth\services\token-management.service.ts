import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Subscription, timer } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class TokenManagementService implements OnD<PERSON>roy {
  private refreshTimer?: Subscription;
  private readonly REFRESH_BUFFER_MINUTES = 5; // Refresh 5 minutes before expiry
  private readonly CHECK_INTERVAL_MINUTES = 1; // Check every minute

  private tokenRefreshSubject = new BehaviorSubject<boolean>(false);
  public tokenRefreshed$ = this.tokenRefreshSubject.asObservable();

  constructor(private authService: AuthService) {
    // Delay token monitoring to allow initial authentication to complete
    setTimeout(() => {
      this.startTokenMonitoring();
    }, 30000); // Start monitoring after 30 seconds to avoid interference with login
  }

  ngOnDestroy(): void {
    this.stopTokenMonitoring();
  }

  /**
   * Parse JWT token and extract expiration date
   */
  parseJwtToken(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT token:', error);
      return null;
    }
  }

  /**
   * Get token expiration date from JWT
   */
  getTokenExpirationDate(token: string): Date | null {
    const decoded = this.parseJwtToken(token);
    if (decoded && decoded.exp) {
      return new Date(decoded.exp * 1000);
    }
    return null;
  }

  /**
   * Check if token is expired or will expire soon
   */
  isTokenExpiringSoon(token: string, bufferMinutes: number = this.REFRESH_BUFFER_MINUTES): boolean {
    const expirationDate = this.getTokenExpirationDate(token);
    if (!expirationDate) {
      return true; // If we can't parse expiration, consider it expiring
    }

    const now = new Date();
    const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds
    const expirationWithBuffer = new Date(expirationDate.getTime() - bufferTime);

    return now >= expirationWithBuffer;
  }

  /**
   * Check if token is completely expired
   */
  isTokenExpired(token: string): boolean {
    const expirationDate = this.getTokenExpirationDate(token);
    if (!expirationDate) {
      return true;
    }
    return new Date() >= expirationDate;
  }

  /**
   * Update token expiration dates in current user store
   */
  updateTokenExpirationDates(): void {
    if (!this.authService.currentUserStore) {
      return;
    }

    const store = this.authService.currentUserStore;

    // Update access token expiration
    if (store.token) {
      const tokenExpiry = this.getTokenExpirationDate(store.token);
      store.tokenExpiresAt = tokenExpiry || undefined;
    }

    // Update refresh token expiration (7 days from now if not available in token)
    if (store.refreshToken) {
      const refreshTokenExpiry = this.getTokenExpirationDate(store.refreshToken);
      if (refreshTokenExpiry) {
        store.refreshTokenExpiresAt = refreshTokenExpiry;
      } else {
        // Fallback: assume 7 days from now
        store.refreshTokenExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      }
    }

    this.authService.setUserStore(store);
  }

  /**
   * Start monitoring token expiration
   */
  private startTokenMonitoring(): void {
    this.stopTokenMonitoring(); // Ensure no duplicate timers

    // Check every minute
    this.refreshTimer = timer(0, this.CHECK_INTERVAL_MINUTES * 60 * 1000).subscribe(() => {
      this.checkAndRefreshToken();
    });
  }

  /**
   * Stop monitoring token expiration
   */
  private stopTokenMonitoring(): void {
    if (this.refreshTimer) {
      this.refreshTimer.unsubscribe();
      this.refreshTimer = undefined;
    }
  }

  /**
   * Check if token needs refresh and refresh if necessary
   */
  private async checkAndRefreshToken(): Promise<void> {
    if (!this.authService.currentUserStore || !this.authService.currentUserStore.token) {
      return;
    }

    const token = this.authService.currentUserStore.token;
    const refreshToken = this.authService.currentUserStore.refreshToken;

    // Check if refresh token is expired
    if (refreshToken && this.isTokenExpired(refreshToken)) {
      console.log('Refresh token expired, logging out');
      this.authService.logout();
      return;
    }

    // Check if access token needs refresh
    if (this.isTokenExpiringSoon(token)) {
      console.log('Token expiring soon, attempting refresh');
      try {
        await this.refreshAccessToken();
      } catch (error) {
        console.error('Failed to refresh token:', error);
        // Don't logout here, let the interceptor handle it
      }
    }
  }

  /**
   * Refresh access token
   */
  private async refreshAccessToken(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getRefreshToken().subscribe({
        next: (result) => {
          if (result && result.token) {
            console.log('Token refreshed successfully');
            this.updateTokenExpirationDates();
            this.tokenRefreshSubject.next(true);
            resolve();
          } else {
            reject(new Error('Failed to refresh token'));
          }
        },
        error: (error) => {
          console.error('Error refreshing token:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Force refresh token (for manual refresh)
   */
  public async forceRefreshToken(): Promise<boolean> {
    try {
      await this.refreshAccessToken();
      return true;
    } catch (error) {
      console.error('Force refresh failed:', error);
      return false;
    }
  }

  /**
   * Get time until token expires (in minutes)
   */
  public getTimeUntilTokenExpires(): number | null {
    if (!this.authService.currentUserStore?.token) {
      return null;
    }

    const expirationDate = this.getTokenExpirationDate(this.authService.currentUserStore.token);
    if (!expirationDate) {
      return null;
    }

    const now = new Date();
    const timeDiff = expirationDate.getTime() - now.getTime();
    return Math.floor(timeDiff / (1000 * 60)); // Convert to minutes
  }

  /**
   * Get time until refresh token expires (in hours)
   */
  public getTimeUntilRefreshTokenExpires(): number | null {
    if (!this.authService.currentUserStore?.refreshToken) {
      return null;
    }

    const expirationDate = this.getTokenExpirationDate(this.authService.currentUserStore.refreshToken);
    if (!expirationDate) {
      return null;
    }

    const now = new Date();
    const timeDiff = expirationDate.getTime() - now.getTime();
    return Math.floor(timeDiff / (1000 * 60 * 60)); // Convert to hours
  }
}
