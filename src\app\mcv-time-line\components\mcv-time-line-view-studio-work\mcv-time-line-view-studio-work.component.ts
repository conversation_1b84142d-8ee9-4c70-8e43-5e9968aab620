import { Component, effect, EventEmitter, Inject, Input, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { Package, PackageDeliverable, PackageDeliverableTaskMap } from 'src/app/package/models/package.model';
import { debounceTime, distinctUntilChanged, forkJoin, map, merge, Observable, Subscription, tap } from 'rxjs';
import { PackageApiService } from 'src/app/package/services/package-api.service';
import { AppConfig } from 'src/app/app.config';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { NgIf, NgFor, AsyncPipe, JsonPipe, DatePipe, CommonModule } from '@angular/common';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { WFTask } from 'src/app/models/wf-task.model';
import { AuthService } from 'src/app/auth/services/auth.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { WFTaskApiService } from 'src/app/wf-task/services/wf-task-api.service';
import { McvPopoverContentComponent } from '../../../mcv-popover/components/mcv-popover-content/mcv-popover-content.component';
import { McvPopoverDirective } from '../../../mcv-popover/directives/mcv-popover.directive';
import { PackageDeliverableTaskMapService } from 'src/app/package/services/package-deliverables-task-map-api.service';
import { McvTimeLineService } from '../../services/mcv-time-line.service';
import { McvTimeLineViewDialogComponent } from '../mcv-time-line-view-dialog/mcv-time-line-view-dialog.component';
@Component({
  selector: 'app-mcv-time-line-view-studio-work',
  templateUrl: './mcv-time-line-view-studio-work.component.html',
  styleUrls: ['./mcv-time-line-view-studio-work.component.scss'],
  standalone: true,
  imports:[ ReactiveFormsModule,MatFormFieldModule,TextFieldModule,CommonModule,MatDialogModule, McvPopoverDirective,MatCheckboxModule,McvPopoverContentComponent,  MatTooltipModule,MatButtonModule,MatIconModule,MatInputModule,NgxMaterialTimepickerModule, MatAutocompleteModule,MatOptionModule,MatDatepickerModule]
})
export class McvTimeLineViewStudioWorkComponent {
 

  deliverables: PackageDeliverable[] = [];
  form!: FormGroup;
  selectedPackage!: Package;
  package!: Package;
  minDate!: Date;
  // minTime: string = '00:00';
  // maxTime: string = '23:59';
  minutesGap = 15;
  isCurrentUser: boolean = false;
  activePackages: Package[] = [];
  studioWorkStageIndex = 3;
  selectPackages!: Observable<Package[]>;
  checkedDeliverables: PackageDeliverable[] = [];
  packageFilter = [{ key: 'statusFlag', value: 0 }, { key: 'typeFlag', value: 0 }]
  data: any;
  hour: any;
  day: any;
  wfTask!: WFTask;
  presentEvent:any;

  packageData:any;
  currentTime = new Date();
  private readonly PACKAGE_ASSOCIATE_TYPEFLAG = this.config.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE;
  private readonly PACKAGE_PARTNER_TYPEFLAG = this.config.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER;

  @Input('data') set Data(value: any) {
    this.data = value
    this.day =  this.data.day
    this.presentEvent = this.data.containsEvent
   
    if (!this.form) {
      this.buildForm();
    }
  }
  @Input('packages') set Package(value: any) {
    this.activePackages = value
    let _activePackages = this.activePackages.filter(x => x.activeStage == 'Green Workshop')
    this.activePackages = _activePackages
  }

  constructor(  @Inject(MAT_DIALOG_DATA) dialogData: any, private datePipe: DatePipe,   private dialogRef: MatDialogRef<McvTimeLineViewDialogComponent>,  private packageDeliverableTaskMapService: PackageDeliverableTaskMapService,    private wftaskService: WFTaskApiService,private utilityService: UtilityService,  private authService: AuthService,public config:AppConfig,  private packageService: PackageApiService,  private formBuilder: FormBuilder, private timelineService : McvTimeLineService){
    // this.getCurrentUser();
  
    const hours = this.padWithZero(this.currentTime.getHours());
    const minutes = this.padWithZero(this.currentTime.getMinutes());
    
    // this.minTime = `${hours}:${minutes}`; // Set minimum time to the current time
      
    // }
    this.minDate = new Date();
  }
  get f(): any { return this.form.controls; }
  get isCurrentUserID() {
    return this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0
  }
  get isSpecialPermissionEdit(): boolean {
    return this.packageService.isPermissionSpecialEdit;
  }
  
  get isSpecialPermissionDelete(): boolean {
    return this.packageService.isPermissionSpecialDelete;
  }
  get currentUser() { return this.authService.currentUserStore?.contact }
  get isMobileView(): boolean {
    return this.utilityService.isMobileView;
  }
  getErrorMessage(control: AbstractControl) {
    return this.utilityService.getErrorMessage(control);
  }

  displaySelectedPackageValue(option: Package) {
    return option ? option.title : '';
  }
  padWithZero(value: number): string {
    return value < 10 ? '0' + value : value.toString();
  }
  ngOnInit(){
  
    
   
  }

  // getCurrentUser() {
  //   const currentUser = this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0;
  //   if (this.eventData.eventData.assigner.id == currentUser) {
  //     this.isCurrentUser = true;
  //   } else {
  //     this.isCurrentUser = false;
  //   }
  // }
  buildForm() {

    this.form = this.formBuilder.group({
      startDate: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      dueDate: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      startTime: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      endTime: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] }),
      comment: new FormControl<string>(''),
      package: new FormControl<any>(null, { nonNullable: true, validators: [Validators.required] })
    });

  
    merge(
      this.f['startTime'].valueChanges,
      this.f['endTime'].valueChanges
    )
    .pipe(debounceTime(400), distinctUntilChanged())
    .subscribe(() => {
      const startTime = this.f['startTime'].value;
      const endTime = this.f['endTime'].value;
      
      if (startTime && endTime) {
        const overlapResult = this.checkOverlapping(); // Use the new method
    
        if (overlapResult.hasOverlap) {
          this.f['startTime'].setValue(null);
          this.f['endTime'].setValue(null);
    
         
          this.utilityService.showSweetDialog(
            'Error!',
            `The selected time conflicts with an existing event. Please choose a different time slot!`,
            'warning'
          );
        }
      }
    });
    
    this.packageSuggestionOnValueChange();
    this.bindForm();
  }

  // checkOverlapping(): { hasOverlap: boolean; type: string | null } {
  //   if (!this.presentLeave && (!this.presentTask || this.presentTask.length === 0)) {
  //     return { hasOverlap: false, type: null };
  //   }
  
  //   const startDateTime = this.utilityService.setTimeValue(this.f['startDate'].value, this.f['startTime'].value);
  //   const dueDateTime = this.utilityService.setTimeValue(this.f['dueDate'].value, this.f['endTime'].value);
  
  //   if (!startDateTime || !dueDateTime) {
  //     return { hasOverlap: false, type: null };
  //   }
  
  //   // Check overlap with leave
  //   if (this.presentLeave) {
  //     const leaveStart = new Date(this.presentLeave.startDate);
  //     const leaveEnd = new Date(this.presentLeave.endDate);
  
  //     if (
  //       (startDateTime >= leaveStart && startDateTime < leaveEnd) ||
  //       (dueDateTime > leaveStart && dueDateTime <= leaveEnd) ||
  //       (startDateTime <= leaveStart && dueDateTime >= leaveEnd)
  //     ) {
  //       return { hasOverlap: true, type: "Leave" };
  //     }
  //   }
  
  //   // Check overlap with any task in presentTask array
  //   if (this.presentTask && this.presentTask.length > 0) {
  //     for (const task of this.presentTask) {
  //       const taskStart = new Date(task.startDate);
  //       const taskEnd = new Date(task.endDate);
  
  //       if (
  //         (startDateTime >= taskStart && startDateTime < taskEnd) ||
  //         (dueDateTime > taskStart && dueDateTime <= taskEnd) ||
  //         (startDateTime <= taskStart && dueDateTime >= taskEnd)
  //       ) {
  //         return { hasOverlap: true, type: "Task" };
  //       }
  //     }
  //   }
  
  //   return { hasOverlap: false, type: null };
  // }
  checkOverlapping(): { hasOverlap: boolean; type: string | null } {
    if (!this.presentEvent || this.presentEvent.length === 0) {
      return { hasOverlap: false, type: null };
    }
  
    const startDateTime = this.utilityService.setTimeValue(this.f['startDate'].value, this.f['startTime'].value);
    const dueDateTime = this.utilityService.setTimeValue(this.f['dueDate'].value, this.f['endTime'].value);
  
    if (!startDateTime || !dueDateTime) {
      return { hasOverlap: false, type: null };
    }
  
    for (const event of this.presentEvent) {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);
  
      if (
        (startDateTime >= eventStart && startDateTime < eventEnd) ||
        (dueDateTime > eventStart && dueDateTime <= eventEnd) ||
        (startDateTime <= eventStart && dueDateTime >= eventEnd)
      ) {
        return { hasOverlap: true, type: event.serviceType }; // Return the type of the overlapping event
      }
    }
  
    return { hasOverlap: false, type: null };
  }
  
  
  
  packageSuggestionOnValueChange() {
    this.selectPackages = this.f['package'].valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),
      map(value =>
        typeof value === "string"
          ? value
          : value != null
            ? (value as Package).title
            : null
      ),
      map(name =>
        name ? this.filterPackages(name as Package + "") : this.filterPackages("")
      )
    )
  }
 

  private filterPackages(query: String): Package[] {
    if (query && query.length > 0) {
      const filterValue = query.toLowerCase();
      return this.activePackages.filter(
        option =>
          option &&
          option.title &&
          option.title.toLowerCase().includes(filterValue) ||
          option.projectTitle.toLowerCase().includes(filterValue)
      );
    } else {
      return this.activePackages;
    }
  }

  onPackageSelected(event: MatAutocompleteSelectedEvent) {
    var selPackage = event.option.value;
    this.selectedPackage = selPackage;
    this.f['package'].setValue(selPackage);
    this.package = selPackage;
    console.log('package',this.package)
    this.deliverables = this.package.deliverables;

    console.log(' this.deliverables ', this.deliverables );
  }

  bindForm() {
    console.log('bindstudiowork')
    console.log('this.day',this.day)
    this.f['startDate'].setValue(this.day);
    this.f['dueDate'].setValue(this.day);
    this.form.valueChanges.subscribe(value => {
      const startDate = new Date(value.startDate);
      const dueDate = new Date(value.dueDate);
      const startTime = value.startTime;
      const endTime = value.endTime;
    
      if (startDate.getTime() === dueDate.getTime()) {
        const startTimeDate = new Date(`1970-01-01T${startTime}:00Z`);
        const endTimeDate = new Date(`1970-01-01T${endTime}:00Z`);
        if (startTimeDate > endTimeDate) {
          this.f['endTime'].setErrors({ wrongTime: true });
        } 
      } 
    });
  

    if(this.data.ispackageTimeline){
      this.f['package'].setValue(this.data.entityData)
      this.package = this.data.entityData
      this.selectedPackage = this.data.entityData
    }
  
  }

  convertISTToUTC(dateString: string): Date {
    const istDate = new Date(dateString);
  
    // Extract the IST offset in milliseconds (UTC+5:30)
    const istOffset = 5.5 * 60 * 60 * 1000;
  
    // Convert the IST date to UTC by subtracting the IST offset
    const utcDate = new Date(istDate.getTime() - istOffset);
  
    return utcDate;
  }

  touchForm() {
    Object.keys(this.form.controls).forEach(field => {
      const control = this.form.get(field);
      if (control)
        control.markAsTouched({ onlySelf: true });
    });
  }

  onSelectCheckbox(deliverable: PackageDeliverable) {
    const _exist = this.checkedDeliverables.find(x => x.id == deliverable.id);
    if (_exist) {
      this.checkedDeliverables = this.checkedDeliverables.filter(x => x.id !== deliverable.id);
    } else {
      this.checkedDeliverables.push(deliverable);
    }
  }
  
  onSubmit() {
    
    if (this.form.invalid) {
      this.touchForm();
      this.utilityService.showSweetDialog(
        'Incomplete data!',
        'Please fill all required fields with valid data and try again!',
        'warning'
      );
      return;
    }

    let packageID:number
    if(this.data.ispackageTimeline){
      packageID = this.data.entityData.id
    }
    else{
      packageID = this.package.id 
    }
    const getStartDate = new Date(this.f['startDate'].value);
    const getEndDate = new Date(this.f['dueDate'].value);
    console.log('getStartDate',getStartDate);
    console.log('getEndDate',getEndDate);
    const getStartTime = new Date(this.f['startTime'].value);
    const getEndTime = new Date(this.f['endTime'].value);
    const formattedStartDate = this.datePipe.transform(getStartDate, 'fullDate'); // Use your desired format
    const formattedEndDate = this.datePipe.transform(getEndDate, 'fullDate'); // Use your desired format
    this.utilityService.showConfirmationDialog(`Do you wish to assign task from ${formattedStartDate} to ${formattedEndDate}? `,
      () => {
     
        
        
    
        const dateChangeCount = Math.floor((Date.UTC(getEndDate.getFullYear(), getEndDate.getMonth(), getEndDate.getDate()) - Date.UTC(getStartDate.getFullYear(), getStartDate.getMonth(), getStartDate.getDate()) ) /(1000 * 60 * 60 * 24));;
       
       
        if (getEndDate.getTime() > getStartDate.getTime()) {
          console.log('dateChangeCount',dateChangeCount);
    
          for (let i = 0; i <= dateChangeCount; i++) {
            const taskDate = new Date(getStartDate);
            taskDate.setDate(getStartDate.getDate() + i);
        
            let startDateTime, dueDateTime;
        
            // if (i === 0) {
            //   // First task
            //   startDateTime = this.utilityService.setTimeValue(taskDate, this.f['startTime'].value);
            //   dueDateTime = this.utilityService.setTimeValue(taskDate, this.config.TIMELINE_END_TIME);
            // } else if (i === dateChangeCount) {
            //   // Last task
            //   startDateTime = this.utilityService.setTimeValue(taskDate, this.config.TIMELINE_START_TIME);
            //   dueDateTime = this.utilityService.setTimeValue(taskDate, this.f['endTime'].value);
            // } else {
            //   // Intermediate tasks
            //   startDateTime = this.utilityService.setTimeValue(taskDate, this.config.TIMELINE_START_TIME);
            //   dueDateTime = this.utilityService.setTimeValue(taskDate, this.config.TIMELINE_END_TIME);
            // }
            startDateTime = this.utilityService.setTimeValue(taskDate, this.f['startTime'].value);
            dueDateTime = this.utilityService.setTimeValue(taskDate,this.f['endTime'].value);
    
            let studioWork = new WFTask(this.config.NAMEOF_ENTITY_PACKAGE, packageID);
            studioWork.isPreAssignedTimeTask = true;
            studioWork.isAssessmentRequired = true;
            studioWork.stageIndex = this.studioWorkStageIndex;
            studioWork.subtitle = `${this.package.projectTitle} | ${this.package.title}`;
            studioWork.title = this.config.PRESET_TASK_TITLE_STUDIO_WORK;
            studioWork.description = this.f['comment'].value;
            // const _isUserAssociated = this.selectedPackage.associations.find(x => x.contactID == this.currentUser?.id);
            // const _associate = this.selectedPackage.associations.find(x => x.typeFlag == this.PACKAGE_ASSOCIATE_TYPEFLAG);
            // if (_isUserAssociated) {
            //   studioWork.assignerContactID = _isUserAssociated.contactID;
            // } else {
            //   if (_associate) {
            //     studioWork.assignerContactID = _associate.contactID;
            //   } else {
            //     this.utilityService.showSwalToast('No Associate Found!!', 'Please add associate to package', 'error');
            //     return;
            //   }
            // }
            studioWork.projectID = this.package.projectID;
            studioWork.contactID = parseInt(this.data.person.id ? this.data.person.id : '0');
            studioWork.startDate = startDateTime;
            studioWork.dueDate = dueDateTime;
            studioWork.mHrAssigned = this.utilityService.getHourDifference(studioWork.startDate, studioWork.dueDate);
        
            if (this.form.invalid) {
              this.touchForm();
              this.utilityService.showSweetDialog(
                'Incomplete data!',
                'Please fill all required fields with valid data and try again!',
                'warning'
              );
              return;
            }
            
            console.log(studioWork);
            this.wftaskService.create(studioWork).subscribe((value) => {
              if (value) {
                if (this.checkedDeliverables && this.checkedDeliverables.length > 0) {
                  let _deli: any[] = [];
                  this.checkedDeliverables.forEach(x => {
                    const _packDevTask = new PackageDeliverableTaskMap();
                    _packDevTask.packageID = this.package.id;
                    _packDevTask.wfTaskID = value.id;
                    _packDevTask.packageDeliverableID = x.id;
                    _deli.push(this.packageDeliverableTaskMapService.create(_packDevTask));
                  });
      
                  forkJoin(_deli).subscribe(res => {
                    console.log(res);
                  });
                }
                this.timelineService.notifyTaskCreated(value);
                this.dialogRef.close(value);
    
              }
            });
          }
        } else if( getEndDate.getTime() === getStartDate.getTime()) {
          // The else part remains unchanged
         
          let studioWork = new WFTask(this.config.NAMEOF_ENTITY_PACKAGE,packageID);
          studioWork.isPreAssignedTimeTask = true;
          studioWork.isAssessmentRequired = true;
          studioWork.stageIndex = this.studioWorkStageIndex;
          studioWork.subtitle = `${this.package.projectTitle} | ${this.package.title}`;
          studioWork.title = this.config.PRESET_TASK_TITLE_STUDIO_WORK;
          studioWork.description = this.f['comment'].value;
          // const _isUserAssociated = this.selectedPackage.associations.find(x => x.contactID == this.currentUser?.id);
          // const _associate = this.selectedPackage.associations.find(x => x.typeFlag == this.PACKAGE_ASSOCIATE_TYPEFLAG);
          // if (_isUserAssociated) {
          //   studioWork.assignerContactID = _isUserAssociated.contactID;
          // } else {
          //   if (_associate) {
          //     studioWork.assignerContactID = _associate.contactID;
          //   } else {
          //     this.utilityService.showSwalToast('No Associate Found!!', 'Please add associate to package', 'error');
          //     return;
          //   }
          // }
          studioWork.projectID = this.package.projectID;
          studioWork.contactID = parseInt(this.data.person.id ? this.data.person.id : '0');
          studioWork.startDate = this.utilityService.setTimeValue(this.f['startDate'].value, this.f['startTime'].value);
          studioWork.dueDate = this.utilityService.setTimeValue(this.f['dueDate'].value, this.f['endTime'].value);
          studioWork.mHrAssigned = this.utilityService.getHourDifference(studioWork.startDate, studioWork.dueDate);
         
          console.log(studioWork);
          this.wftaskService.create(studioWork).subscribe((value) => {
            if (value) {
              if (this.checkedDeliverables && this.checkedDeliverables.length > 0) {
                let _deli: any[] = [];
                this.checkedDeliverables.forEach(x => {
                  const _packDevTask = new PackageDeliverableTaskMap();
                  _packDevTask.packageID = this.package.id;
                  _packDevTask.wfTaskID = value.id;
                  _packDevTask.packageDeliverableID = x.id;
                  _deli.push(this.packageDeliverableTaskMapService.create(_packDevTask));
                });
      
                forkJoin(_deli).subscribe(res => {
                  console.log(res);
                });
              }
              this.timelineService.notifyTaskCreated(value);
              this.dialogRef.close(value);
             
            }
          });
        }
    
      })

   

  }
  
    onChangeSelection(deliverable: PackageDeliverable) {
     
      const _exist = this.checkedDeliverables.find(x => x.id == deliverable.id);
      if (_exist) {
        this.checkedDeliverables = this.checkedDeliverables.filter(x => x.id !== deliverable.id);
      } else {
        this.checkedDeliverables.push(deliverable);
      }
    }

    // onCheckedDeliverables(deliverable: PackageDeliverable) {
    //   if (deliverable) {
    //     const _exist = deliverable.packageDeliverableTaskMaps.find(x => x.wfTaskID == this.wfTask.id);
    //     if (_exist) {
    //       return true;
    //     } return false;
    //   } return false;
    // }
}
