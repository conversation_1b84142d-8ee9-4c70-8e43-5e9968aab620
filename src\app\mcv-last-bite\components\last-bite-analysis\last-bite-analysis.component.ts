import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';

import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { AuthService } from 'src/app/auth/services/auth.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';

import { AppPermissions } from 'src/app/app.permissions';
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { ProjectLastBite } from 'src/app/mcv-last-bite/models/project-last-bite.model';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { LastBiteAnalysisListItemComponent } from '../last-bite-analysis-list-item/last-bite-analysis-list-item.component';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';

@Component({
  selector: 'mcv-last-bite-analysis',
  templateUrl: './last-bite-analysis.component.html',
  styleUrls: ['./last-bite-analysis.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    LastBiteAnalysisListItemComponent,
    FormsModule,
    FooterComponent,
    MatFormFieldModule,
    MatDatepickerModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    FilterToggleDirective,
    MatMenuModule,
    MatOptionModule,
    MatCheckboxModule,
    McvFilterSidenavComponent
  ],
})
export class LastBiteAnalysisComponent implements OnInit {
  pagedListConfig: PagedListConfig = new PagedListConfig({
    pageSize: 50,
    filters: [
      { key: 'rangeStart', value: this.utility.getMonthStart().toISOString() },
      { key: 'rangeEnd', value: this.utility.getMonthEnd().toISOString() },
      {
        key: 'contactID',
        value: this.authService.currentUserStore
          ? this.authService.currentUserStore?.contact.id.toString()
          : '0',
      },
    ],
    searchKey: null,
    sort: '',
    route: '',
    showAll: false,
    showAssigned: false,
    groupBy: [],
    keyPropertyName: '',
  });
  @Input('config') set configValue(value: PagedListConfig) {
    if (value) {
      this.pagedListConfig = value;
    }
  }

  dataList: ProjectLastBite[] = [];
  total!: ProjectLastBite;
  isLoading: boolean = false;
  totalRecordsCount = 0;
  totalPages = 0;
  // pageSize = 50;
  currentPage = 0;
  isMobileView: boolean = false;
  dateFilters!: FormGroup;
  searchFilter!: FormControl;
  showAll: boolean = false;
  isPermissionSpecialShowAll: boolean = false;
  isSorted: boolean = false;
  projectKeySearch: string = '';
  filteredProject: string[] = [];
  selectedProject: string[] = [];
  distinctProject: string[] = [];
  statusSearch: string = '';
  filteredStatus: string[] = [];
  selectedStatus: string[] = [];
  distinctStatus: string[] = [];
  originalDataList: any[] = [];

  partnerSearch: string = '';
  filteredPartner: string[] = [];
  selectedPartner: string[] = [];
  distinctPartner: string[] = [];

  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '', // No active column initially
    lastReceivedPaymentDate: '',
    previousPaymentDate: '',
    lastBillNo: '',
    lastReceivedPaymentAmount: '',
    partnerAmount: '',
    vHrCost: '',
    lastBiteAmount: '',
  };

  @Output() listLoad = new EventEmitter<any>();

  constructor(
    private authService: AuthService,
    private service: ProjectApiService,
    private utility: UtilityService,
    private formBuilder: FormBuilder,
    private permissions: AppPermissions
  ) {}

  ngOnInit() {
    this.buildForm();
    this.isMobileView = this.utility.isMobileView;
    this.isPermissionSpecialShowAll = this.authService.isInAnyRole([
      this.permissions.COCKPIT_LAST_BITE_ANALYSIS_SPECIAL_SHOW_ALL,
    ]);

    this.refresh();
  }

  buildForm() {
    this.dateFilters = this.formBuilder.group({
      start: new FormControl<any>(null, { validators: [Validators.required] }),
      end: new FormControl<any>(null, { validators: [Validators.required] }),
    });

    this.dateFilters.controls['start'].setValue(this.utility.getMonthStart(), {
      emitEvent: false,
    });

    this.dateFilters.controls['end'].setValue(this.utility.getMonthEnd(), {
      emitEvent: false,
    });

    this.dateFilters.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value && value.start && value.end) {
          this.pagedListConfig.filters = this.pagedListConfig.filters.filter(
            (x) => x.key !== 'rangeStart' && x.key != 'rangeEnd'
          );
          this.pagedListConfig.filters.push({
            key: 'rangeStart',
            value: value.start.toISOString(),
          });
          this.pagedListConfig.filters.push({
            key: 'rangeEnd',
            value: value.end.toISOString(),
          });

          this.search();
        }
      });
    this.searchFilter = new FormControl();
    this.searchFilter.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.pagedListConfig.searchKey = value;
          this.search();
        }
      });
  }

  getDataList(currentPage: any, pageSize: any) {
    this.isLoading = true;
    this.service
      .getAnalysis(
        'LastBite',
        this.pagedListConfig.filters,
        this.pagedListConfig.searchKey,
        this.pagedListConfig.sort,
        this.pagedListConfig.showAll
      )
      .subscribe(
        (data: any) => {
          // this.totalRecordsCount = data.total;
          // this.totalPages = data.pages;
          // this.listLoad.emit({ totalRecordsCount: this.totalRecordsCount });
          this.dataList = data; // this.utility.updatePagedList<WFTaskVHrAnalysisDto>(data.list, this.dataList, 'contactID');
          this.isLoading = false;
          this.calculateTotal();
          this.extractDistinctValues();
          this.originalDataList = [...this.dataList];
        },
        (error: any) => {
          this.isLoading = false;
        }
      );
  }

  calculateTotal() {
    this.total = new ProjectLastBite();
    this.dataList.forEach((x) => {
      this.total.lastReceivedPaymentAmount += x.lastReceivedPaymentAmount;
   
      this.total.partnerAmount += x.partnerAmount;
      this.total.vHrCost += x.vHrCost;
      this.total.lastBiteAmount += x.lastBiteAmount;
    });
  }

  loadMoreRecords() {
    if (
      this.currentPage * this.pagedListConfig.pageSize <
      this.totalRecordsCount
    ) {
      this.currentPage++;
      this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
    }
  }

  search() {
    this.currentPage = 0;
    this.dataList = [];
    this.getDataList(this.currentPage, this.pagedListConfig.pageSize);
  }
  refresh() {
    this.pagedListConfig.searchKey = null;
    this.searchFilter.setValue(null);
    this.dataList = [];
    this.getDataList(0, (this.currentPage + 1) * this.pagedListConfig.pageSize);
  }

  onPeriodSelection(event: any) {}

  onExportExcel() {
    this.service.exportAnalysisExcel(
      'LastBite',
      this.pagedListConfig.filters,
      this.pagedListConfig.searchKey,
      this.pagedListConfig.sort
    );
  }

  onToggleShowAll() {
    this.showAll = !this.showAll;
    this.pagedListConfig.showAll = this.showAll;
    if (this.showAll) {
      this.pagedListConfig.filters = this.pagedListConfig.filters.filter(
        (x) => x.key !== 'contactID'
      );
    } else {
      this.pagedListConfig.filters.push({
        key: 'contactID',
        value: this.authService.currentUserStore
          ? this.authService.currentUserStore?.contact.id.toString()
          : '0',
      });
    }
    this.refresh();
  }

  clearSearch(event: any) {
    this.projectKeySearch = '';
    this.statusSearch = '';
    this.partnerSearch = '';
    this.filterDistinctProject();
    this.filterDistinctStatus();
    this.filterDistinctPartner();
  }

  toggleSelectAll(filterType: 'project' | 'status' | 'partner') {
    const distinctValues = this.getDistinctValues(filterType);
    switch (filterType) {
      case 'project':
        this.selectedProject = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'status':
        this.selectedStatus = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'partner':
        this.selectedPartner = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
    }
    this.applyFilters();
  }
  getDistinctValues(filterType: 'project' | 'status'  | 'partner'): string[] {
    switch (filterType) {
      case 'project':
        return this.distinctProject;
      case 'status':
        return this.distinctStatus;
      case 'partner':
        return this.distinctPartner;
      default:
        return [];
    }
  }

  isAllSelected(type: string): boolean {
    if (type === 'project') {
      return (
        this.selectedProject.length === this.distinctProject.length &&
        this.selectedProject.length > 0
      );
    } else if (type === 'status') {
      return (
        this.selectedStatus.length === this.distinctStatus.length &&
        this.selectedStatus.length > 0
      );
    }
    else if (type === 'partner') {
      return (
        this.selectedPartner.length === this.distinctPartner.length &&
        this.selectedPartner.length > 0
      );
    }
    return false;
  }

  toggleSelection(value: string, type: string) {
    if (type === 'project') {
      this.selectedProject.includes(value)
        ? this.selectedProject.splice(this.selectedProject.indexOf(value), 1)
        : this.selectedProject.push(value);
    } else if (type === 'status') {
      this.selectedStatus.includes(value)
        ? this.selectedStatus.splice(this.selectedStatus.indexOf(value), 1)
        : this.selectedStatus.push(value);
    }
    else if (type === 'partner') {
      this.selectedPartner.includes(value)
        ? this.selectedPartner.splice(this.selectedPartner.indexOf(value), 1)
        : this.selectedPartner.push(value);
    }
    this.applyFilters();
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if (this.pagedListConfig.searchKey) {
      const searchLower = this.pagedListConfig.searchKey.toLowerCase();
      filteredList = filteredList.filter(
        (item) =>
          item.project?.toLowerCase().includes(searchLower) ||
          '' ||
          item.status?.toLowerCase().includes(searchLower) ||
          ''
      );
    }
    if (this.selectedProject.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedProject.includes(item.project)
      );
    }

    if (this.selectedStatus.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedStatus.includes(item.status)
      );
    }
    if (this.selectedPartner.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedPartner.includes(item.partner)
      );
    }
    this.dataList = filteredList;
    this.calculateTotal();
  }

  extractDistinctValues() {
    this.distinctProject = [...new Set(this.dataList.map((x) => x.project))];
    this.distinctStatus = [...new Set(this.dataList.map((x) => x.status))];
    this.distinctPartner = [...new Set(this.dataList.map((x) => x.partner))];
    this.filteredProject = [...this.distinctProject];
    this.filteredStatus = [...this.distinctStatus];
    this.filteredPartner = [...this.distinctPartner];
  }

  filterDistinctProject() {
    const searchLower = this.projectKeySearch.toLowerCase();
    this.filteredProject = this.distinctProject.filter((person) =>
      person.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctStatus() {
    const searchLower = this.statusSearch.toLowerCase();
    this.filteredStatus = this.distinctStatus.filter((person) =>
      person.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctPartner() {
    const searchLower = this.partnerSearch.toLowerCase();
    this.filteredPartner = this.distinctPartner.filter((person) =>
      person.toLowerCase().includes(searchLower)
    );
  }

  clearSelection(type: string) {
    if (type === 'project') {
      this.selectedProject = [];
    } else if (type === 'status') {
      this.selectedStatus = [];
    } 
    else if (type === 'partner') {
      this.selectedPartner = [];
    }
    // else if (type === 'title') {
    //   this.selectedTitle = [];
    // }

    this.applyFilters(); // Apply filters after clearing the selection
  }

  sortData(column: keyof ProjectLastBite | '') {
    if (column === '') {
      // Reset to default (original data) but apply the filters again
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProject.length === 0 ||
            this.selectedProject.includes(item.person)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status)) &&
            (this.selectedPartner.length === 0 ||
            this.selectedPartner.includes(item.partner))
          //  (this.selectedTask.length === 0 || this.selectedTask.includes(item.taskTitle))
        );
      });

      // Reset the sort state to default
      this.sortState = {
        activeColumn: '',
        lastReceivedPaymentDate: '',
        previousPaymentDate: '',
        lastBillNo: '',
        lastReceivedPaymentAmount: '',
        partnerAmount: '',
        vHrCost: '',
        lastBiteAmount: '',
      };
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if no sorting is selected
    }

    // If the clicked column is already the active column, cycle through the three states
    const currentSort = this.sortState[column];

    if (currentSort === 'newFirst') {
      // If it's 'newFirst', change to 'oldFirst'
      this.sortState[column] = 'oldFirst';
    } else if (currentSort === 'oldFirst') {
      // If it's 'oldFirst', reset to default (no sorting)
      this.sortState[column] = '';
      this.sortState.activeColumn = '';
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProject.length === 0 ||
            this.selectedProject.includes(item.person)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status)) &&
          (this.selectedPartner.length === 0 ||
            this.selectedPartner.includes(item.partner))
          //  (this.selectedTask.length === 0 || this.selectedTask.includes(item.taskTitle))
        );
      });

      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if reset is selected
    } else {
      // If no sorting is applied, set it to 'newFirst' (ascending order)
      this.sortState[column] = 'newFirst';
    }

    // Set the active column
    this.sortState['activeColumn'] = column;

    // Reset other columns' sort state to '' (no sort)
    for (let key in this.sortState) {
      if (key !== column && key !== 'activeColumn') {
        this.sortState[key] = ''; // Reset other columns' sort state to no sort
      }
    }

    // Sorting logic: Compare dates for the active column
    const sortedData = [...this.dataList].sort((a, b) => {
      const dateA = new Date(a[column] as string);
      const dateB = new Date(b[column] as string);

      if (this.sortState[column] === 'newFirst') {
        return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
      } else if (this.sortState[column] === 'oldFirst') {
        return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
      }
      return 0; // If no sorting, return unchanged order
    });

    // Update the dataList with the sorted data
    this.dataList = sortedData;
    this.isSorted = true; // Set isSorted to true when sorting is applied
  }

  resetFilter() {
    this.selectedProject = [];
    this.selectedPartner = [];
    this.selectedStatus = [];
    this.dataList = [...this.originalDataList]; // Restore full list
    this.calculateTotal();
  }
}
