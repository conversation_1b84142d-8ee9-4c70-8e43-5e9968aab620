import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AppConfig } from '../app.config';
import { AuthService } from '../auth/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService,
    private config: AppConfig
  ) { }

  canActivate(next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkLogin(state.url, next.data['role']);
  }

  checkLogin(url: string, roles: string[]): boolean {
    // Check if user store exists and user is authenticated
    if (this.authService.currentUserStore && this.authService.currentUserStore.isAuth) {
      // Check if token exists and is not expired
      if (this.authService.currentUserStore.token) {
        // Check if token is expired
        if (this.authService.currentUserStore.tokenExpiresAt) {
          const now = new Date();
          if (now >= this.authService.currentUserStore.tokenExpiresAt) {
            console.log('Token expired, redirecting to login');
            this.authService.redirectUrl = url;
            this.router.navigate([this.config.ROUTE_LOGIN]);
            return false;
          }
        }

        // Check role permissions if required
        if (roles) {
          const _permission = this.authService.isInAnyRole(roles);
          console.log('Role permission check:', _permission);
          return _permission;
        }

        return true;
      }
    }

    console.log('User not authenticated, redirecting to login. URL:', url);
    // To redirect to the page user is after login
    this.authService.redirectUrl = url;
    // move to login page
    this.router.navigate([this.config.ROUTE_LOGIN]);
    return false;
  }
}
