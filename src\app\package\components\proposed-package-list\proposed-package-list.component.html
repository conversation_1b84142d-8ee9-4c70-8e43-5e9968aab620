<div class="package-analysis-wrapper">


    <div class="data-filter-row"
        *ngIf="selectedProject.length > 0 || selectedPartner.length > 0 || selectedPackage.length > 0 || selectedStatus.length > 0">
        <div class="filter-header"
            *ngIf="selectedProject.length > 0 || selectedPartner.length > 0 || selectedPackage.length > 0">
            <h6 class="font-focused ">Filters:</h6>
        </div>
        <h6 *ngIf="selectedProject.length > 0">
            <b>Project:</b> {{selectedProject.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('project')">✖</span>
        </h6>
        <h6 *ngIf="selectedPartner.length > 0">
            <b>Partner:</b> {{selectedPartner.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('partner')">✖</span>
        </h6>
        <h6 *ngIf="selectedPackage.length > 0">
            <b>Package:</b> {{selectedPackage.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('package')">✖</span>
        </h6>
        <h6 *ngIf="selectedStatus.length > 0">
            <b>Status:</b> {{selectedStatus.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('package')">✖</span>
        </h6>
        <h6 (click)="resetFilter()"
            *ngIf="selectedProject.length > 0 || selectedPartner.length > 0 || selectedPackage.length > 0 || selectedStatus.length > 0">
            <b> Clear All</b>

        </h6>
    </div>
    <div class="package-analysis-table-wrapper">
        <table>
            <thead>

                <tr class="packages-info-header">
                    <th>
                        <div>

                            <h6><span>Proposed: </span> {{totalProposedVhr | number:'2.0-2'}} <small>vHr</small> </h6>
                        </div>
                    </th>
                    <th colspan="2">
                        <div>

                            <h6><span>Active: </span>{{totalActiveVHr | number:'2.0-2'}} <small>vHr</small> </h6>
                        </div>
                    </th>
                    <th colspan="11">
                        <div>

                            <h6><span>Items: </span> {{dataList.length}} <small>items</small> </h6>
                        </div>
                    </th>
                   
                </tr>
                <tr>
                    <th mat-button [matMenuTriggerFor]="assignedToMenu"
                        [ngClass]="{'filter': selectedProject.length > 0}">

                        <div class="analysis-table-header">
                            <h6>Project</h6>
                            <mat-icon>filter_alt</mat-icon>
                            <mat-menu #assignedToMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input matInput placeholder="Search" [(ngModel)]="projectSearchKey"
                                            (input)="filterDistinctProject()" />
                                        <mat-icon class="clear-icon" matSuffix
                                            (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('project')">
                                    {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
                                </button>
                                <mat-option class="menu-mat-option" *ngFor="let project of filteredProject"
                                    (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                                    <mat-checkbox [checked]="selectedProject.includes(project)">{{ project
                                        }}</mat-checkbox>
                                </mat-option>

                            </mat-menu>
                        </div>

                    </th>
                    
                    <th mat-button [matMenuTriggerFor]="partnerMenu" [ngClass]="{'filter': selectedPartner.length > 0}">

                        <div class="analysis-table-header">
                            <h6>Partner</h6>
                            <mat-icon>filter_alt</mat-icon>
                            <mat-menu #partnerMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input matInput placeholder="Search" [(ngModel)]="partnerSearchKey"
                                            (input)="filterDistinctProject()" />
                                        <mat-icon class="clear-icon" matSuffix
                                            (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('partner')">
                                    {{ isAllSelected('partner') ? 'Deselect All' : 'Select All' }}
                                </button>
                                <mat-option class="menu-mat-option" *ngFor="let partner of filteredPartner"
                                    (click)="$event.stopPropagation(); toggleSelection(partner, 'partner')">
                                    <mat-checkbox [checked]="selectedPartner.includes(partner)">{{ partner
                                        }}</mat-checkbox>
                                </mat-option>

                            </mat-menu>
                        </div>
                    </th>
                    <th mat-button [matMenuTriggerFor]="packageMenu" [ngClass]="{'filter': selectedPackage.length > 0}">

                        <div class="analysis-table-header">
                            <h6>Package</h6>
                            <mat-icon>filter_alt</mat-icon>
                            <mat-menu #packageMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input matInput placeholder="Search" [(ngModel)]="partnerSearchKey"
                                            (input)="filterDistinctProject()" />
                                        <mat-icon class="clear-icon" matSuffix
                                            (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                </div>
                                <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                                <button mat-menu-item (click)="$event.stopPropagation(); toggleSelectAll('package')">
                                    {{ isAllSelected('package') ? 'Deselect All' : 'Select All' }}
                                </button>
                                <mat-option class="menu-mat-option" *ngFor="let package of filteredPackage"
                                    (click)="$event.stopPropagation(); toggleSelection(package, 'package')">
                                    <mat-checkbox [checked]="selectedPackage.includes(package)">{{ package
                                        }}</mat-checkbox>
                                </mat-option>

                            </mat-menu>
                        </div>
                    </th>
                    <th></th>
                    <th>

                        <div class="analysis-table-header" (click)="sortData('startDate')">
                            <h6>Start</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'startDate' && sortState['startDate'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['startDate'] === ''">import_export</mat-icon>
                        </div>
                    </th>
                    <th>

                        <div class="analysis-table-header" (click)="sortData('finalDate')">
                            <h6>Due</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'finalDate' && sortState['finalDate'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'finalDate' && sortState['finalDate'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['finalDate'] === ''">import_export</mat-icon>
                        </div>
                    </th>



                    <th>


                        Priority

                    </th>
                    <th>
                        <div class="analysis-table-header" (click)="sortData('proposedProbablity')">
                            <h6>proposedProbablity</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'proposedProbablity' && sortState['proposedProbablity'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'proposedProbablity' && sortState['proposedProbablity'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['proposedProbablity'] === ''">import_export</mat-icon>
                        </div>
                    </th>

                    <th>
                        <div class="analysis-table-header" (click)="sortData('stageServiceAmount')">
                            <h6>Stage Value</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'stageServiceAmount' && sortState['stageServiceAmount'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'stageServiceAmount' && sortState['stageServiceAmount'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['stageServiceAmount'] === ''">import_export</mat-icon>
                        </div>
                    </th>
                    <th>
                        <div class="analysis-table-header" (click)="sortData('expectedVhr')">
                            <h6>Stage vHr</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'expectedVhr' && sortState['expectedVhr'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'expectedVhr' && sortState['expectedVhr'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['expectedVhr'] === ''">import_export</mat-icon>
                        </div>
                    </th>
                    <th>
                        <div class="analysis-table-header" (click)="sortData('vHrAssigned')">
                            <h6>vHr</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'vHrAssigned' && sortState['vHrAssigned'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'vHrAssigned' && sortState['vHrAssigned'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['vHrAssigned'] === ''">import_export</mat-icon>
                        </div>

                    </th>
                    <th>
                        <div class="analysis-table-header" (click)="sortData('vHrAssignedCost')">
                            <h6>vHr Cost</h6>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'vHrAssignedCost' && sortState['vHrAssignedCost'] === 'newFirst'">south</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === 'vHrAssignedCost' && sortState['vHrAssignedCost'] === 'oldFirst'">north</mat-icon>
                            <mat-icon
                                *ngIf="sortState['activeColumn'] === '' && sortState['vHrAssignedCost'] === ''">import_export</mat-icon>
                        </div>
                    </th>

                </tr>
                <tr class="total">
                    <th [colSpan]="8">Total</th>


                    <th class="align-right">{{total.stageServiceAmount |
                        currency:'INR':'symbol':'1.0'}}</th>
                    <th class="align-right">{{total.expectedVhr | number:'2.0-2'}}</th>
                    <th class="align-right">{{total.vHrAssigned | number:'2.0-2'}}</th>
                    <th class="align-right">{{total.vHrAssignedCost |
                        currency:'INR':'symbol':'1.0'}}</th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let item of dataList; let i = index">
                    <tr>
                        <td>{{item.project}}</td>
                    
                        <td>{{item.partner}}</td>


                        <td class="text-pre-wrap">
                            {{item.package}}
                        </td>
                        <td class="align-center">
                            <div class="package-type" [style.background-color]="PACKAGE_PROPOSED_COLOR"
                                *ngIf="item.typeFlag==PACKAGE_TYPE_FLAG_PROPOSED"></div>
                            <div class="package-type" [style.background-color]="PACKAGE_ACTIVE_COLOR"
                                *ngIf="item.typeFlag==PACKAGE_TYPE_FLAG_ACTIVE"></div>
                        </td>

                        <td class="text-right">{{item.startDate | date:'dd MMM y'}}</td>
                        <td class="text-right">{{item.finalDate | date:'dd MMM y'}}</td>
                        <td class="text-right">
                            {{item.proposedPriority}}
                        </td>
                        <td class="text-right">
                            {{item.proposedProbablity}} %
                        </td>

                        <td class="text-right">{{item.stageServiceAmount | currency:'INR':'symbol':'1.0'}}
                        </td>
                        <td class="text-right">{{item?.expectedVhr | number:'1.0-0'}}
                        </td>
                        <td class="text-right">{{item.vHrAssigned | number:'2.0-2'}}</td>
                        <td class="text-right">{{item.vHrAssignedCost |
                            currency:'INR':'symbol':'1.0'}}
                        </td>
                    </tr>

                </ng-container>
            </tbody>
        </table>
    </div>
</div>
<app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
          </button>
        
      
        <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <span> Export Excel</span>
            <!-- <img src="assets/icons/file-xlsx.png" alt="xlsx" /> -->
        </button>

    </div>
    <div class="nav-footer-mobile-actions">

        <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>

    </div>
    <div class="nav-filters">

        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="typeFC" multiple placeholder="Type">
                    <mat-option *ngFor="let item of typeOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <span> Export Excel</span>
            <!-- <img src="assets/icons/file-xlsx.png" alt="xlsx" /> -->
        </button>

    </div>


</app-footer>

<app-mcv-filter-sidenav #sidenav>
    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="companyFC" placeholder="Company">
                <mat-option *ngFor="let item of companyOptions" [value]="item">
                    {{item.title }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="typeFC" multiple placeholder="Type">
                <mat-option *ngFor="let item of typeOptions" [value]="item.value">{{item.title}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="statusFC" multiple placeholder="Status">
                <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</app-mcv-filter-sidenav>