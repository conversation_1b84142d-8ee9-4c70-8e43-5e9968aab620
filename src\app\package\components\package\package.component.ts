import { AuthService } from "src/app/auth/services/auth.service";
import { TimeEntryApiService } from 'src/app/services/time-entry-api.service';
import { ContactApiService } from "src/app/contact/services/contact-api.service";
import { PackageApiService } from "src/app/package/services/package-api.service";
import { ProjectApiService } from './../../../project/services/project-api.service';
import { PackageAttachmentApiService } from '../../services/package-attachment-api.service';
import { AppSettingMasterApiService } from '../../../services/app-setting-master-api.service';
import { PackageAssociationApiService } from '../../services/package-association-api.service';
import { MeetingAgendaApiService } from 'src/app/meeting-agenda/services/meeting-agenda-api.service';
import { Validators, ReactiveFormsModule, FormsModule, FormGroup, FormBuilder, AbstractControl, FormControl, FormArray } from "@angular/forms";
import { Package, PackageDeliverable, SpaceDiveUrl } from "src/app/package/models/package.model";
import { WFTask } from "src/app/models/wf-task.model";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { OnInit, Component, Input, EventEmitter, Output, ViewChild } from "@angular/core";
import { PackageStudioDialogComponent } from "../package-studio-dialog/package-studio-dialog.component";
import { TimeEntryDto } from "src/app/models/time-entry-dto";
import { Contact } from "src/app/contact/models/contact";
import { debounceTime, distinctUntilChanged, map, startWith } from "rxjs/operators";
import { firstValueFrom, Observable } from "rxjs";
import { PagedListConfig } from 'src/app/mcv-core/models/paged-list-config.model';
import { McvComponentDialogConfig } from 'src/app/models/mcv-component-dialog-config';
import { PackageDetailDialogComponent } from '../package-detail-dialog/package-detail-dialog.component';
import { PackageAgendaEditComponent } from '../package-agenda-edit/package-agenda-edit.component';
import { PackagePlaylistDialogComponent } from '../package-playlist-dialog/package-playlist-dialog.component';
import { PackageAssociationDto } from '../../models/package-association-dto';
import { PackageAttachment } from '../../models/package-attachment.model';
import { MeetingAgenda } from 'src/app/meeting-agenda/models/meeting-agenda.model';
import { PackagePlaylist } from './../../models/package-playlist.model';
import { SafeUrl } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';
import { DateFilterFn, MatDatepickerModule } from "@angular/material/datepicker";
import { PackageDeliverableMasterApiService } from "../../services/package-deliverable-master-api.service";
import { McvQrCodeComponent } from "../../../mcv-qr-code/components/mcv-qr-code/mcv-qr-code.component";
import { McvActivityListComponent } from "../../../mcv-activity/components/mcv-activity-list/mcv-activity-list.component";
import { PackageSubmissionActionComponent } from "../package-submission-action/package-submission-action.component";
import { PackageTaskActionBoxComponent } from "../package-task-action-box/package-task-action-box.component";
import { PackageStudioActionComponent } from "../package-studio-action/package-studio-action.component";
import { McvTimeEntryListComponent } from "../../../mcv-time-entry/components/mcv-time-entry-list/mcv-time-entry-list.component";
import { PackagePagedListComponent } from "../package-paged-list/package-paged-list.component";
import { MatTooltipModule } from "@angular/material/tooltip";
import { PackageAttachmentComponent } from "../package-attachment/package-attachment.component";
import { PackageTransmittalComponent } from "../package-transmittal/package-transmittal.component";
import { McvPopoverContentComponent } from "../../../mcv-popover/components/mcv-popover-content/mcv-popover-content.component";
import { McvPopoverDirective } from "../../../mcv-popover/directives/mcv-popover.directive";
import { MeetingAgendaComponent } from "../../../meeting-agenda/components/meeting-agenda/meeting-agenda.component";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { ContactListItemComponent } from "../../../contact/components/contact-list-item/contact-list-item.component";
import { PackageAssociationContactComponent } from "../package-association-contact/package-association-contact.component";
import { MatOptionModule } from "@angular/material/core";
import { MatAutocompleteModule } from "@angular/material/autocomplete";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { PackageVhrProgressBarComponent } from "../package-vhr-progress-bar/package-vhr-progress-bar.component";
import { PackageProgressBarComponent } from "../package-progress-bar/package-progress-bar.component";
import { PackageStudioTaskComponent } from "../package-studio-task/package-studio-task.component";
import { MatExpansionModule } from "@angular/material/expansion";
import { WftaskTitleBarComponent } from "../../../wf-task/components/wftask-title-bar/wftask-title-bar.component";
import { NgClass, AsyncPipe, DecimalPipe, DatePipe, CommonModule } from "@angular/common";
import { AppConfig } from "src/app/app.config";
import { McvComponentConfig } from "src/app/models/mcv-component-config";
import { AppPermissions } from "src/app/app.permissions";
import { UtilityService } from "src/app/shared/services/utility.service";
import { WFTaskApiService } from "src/app/wf-task/services/wf-task-api.service";
import { McvTimeEntryTimeLineComponent } from "src/app/mcv-time-entry/components/mcv-time-entry-time-line/mcv-time-entry-time-line.component";
import { ProjectsPartnerFeePhaseEditorDialogComponent } from "src/app/project-charts/components/projects-partner-fee-phase-editor-dialog/projects-partner-fee-phase-editor-dialog.component";
import { ProjectScopeApiService } from "src/app/project-scope/services/project-scope-api.service";
import { ProjectSummary } from "src/app/project/models/project-summary.model";
@Component({
  selector: "app-package",
  templateUrl: "./package.component.html",
  styleUrls: ["./package.component.scss"],
  standalone: true,
  imports: [CommonModule, WftaskTitleBarComponent, MeetingAgendaComponent, MatExpansionModule, PackageStudioTaskComponent, PackageProgressBarComponent, PackageVhrProgressBarComponent, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatDatepickerModule, MatAutocompleteModule, MatOptionModule, PackageAssociationContactComponent, ContactListItemComponent, MatButtonModule, MatIconModule, McvPopoverDirective, McvPopoverContentComponent, PackageTransmittalComponent, PackageAttachmentComponent, FormsModule, MatTooltipModule, PackagePagedListComponent, NgClass, McvTimeEntryListComponent, PackageStudioActionComponent, PackageTaskActionBoxComponent, PackageSubmissionActionComponent, McvActivityListComponent, McvQrCodeComponent, AsyncPipe, DecimalPipe, DatePipe]
})
export class PackageComponent implements OnInit {

  @ViewChild(McvActivityListComponent) protected activity!: McvActivityListComponent;
  @ViewChild(McvTimeEntryTimeLineComponent) protected timeline!: McvTimeEntryTimeLineComponent;

  isTaskMode: boolean = false;
  task: WFTask | any;
  entityID!: number;
  isCreateMode: boolean = false;
  form!: FormGroup;
  spaceDiveForm!:FormGroup;
  projectFlowData!: ProjectSummary;

  readonly WFTASK_STATUSFLAG_PENDING = this.config.WFTASK_STATUSFLAG_PENDING;
  readonly WFTASK_STATUSFLAG_COMPLETED = this.config.WFTASK_STATUSFLAG_COMPLETED;
  readonly WFTASK_STATUSFLAG_STARTED = this.config.WFTASK_STATUSFLAG_STARTED;
  readonly WFTASK_STATUSFLAG_PAUSED = this.config.WFTASK_STATUSFLAG_PAUSED;
  readonly WFTASK_STATUSFLAG_UNATTENDED = this.config.WFTASK_STATUSFLAG_UNATTENDED;

  get PACKAGE_COMPLETED_COLOR(){ return this.config.PACKAGE_COMPLETED_COLOR; }
  get PACKAGE_PROPOSED_COLOR(){ return this.config.PACKAGE_PROPOSED_COLOR; }
  get PACKAGE_ACTIVE_COLOR(){ return this.config.PACKAGE_ACTIVE_COLOR; }

  get PAYMENT_RECEIVED_COLOR(){ return this.config.PAYMENT_RECEIVED_COLOR; }
  get PAYMENT_DUE_COLOR(){ return this.config.PAYMENT_DUE_COLOR; }

  get VHR_CONSUMED_COLOR(){ return this.config.VHR_CONSUMED_COLOR; }
  get VHR_X_COLOR(){ return this.config.VHR_X_COLOR; }

  @Input() isReadOnly: boolean = false;
  @Input('config') set configValue(value: McvComponentConfig) {
    if (value) {
      this.entityID = value.entityID;
      this.task = value.task;
      this.isCreateMode = value.isCreateMode;
      this.isTaskMode = value.isTaskMode;
      this.currentEntity = value.currentEntity;
      this.refresh();
    }
  }

  @Output() submit = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<any>();
  @Output() create = new EventEmitter<any>();
  @Output() update = new EventEmitter<any>();
  @Output() delete = new EventEmitter<any>();
  @Output() taskComplete = new EventEmitter<WFTask>();

  treeMap: PackagePlaylist[] = [];
  //Common component properties-------------------------
  currentEntity: Package = new Package();
  nameOfEntity = this.config.NAMEOF_ENTITY_PACKAGE;
  //-----------end
  readonly taskConstants = {
    STAGEINDEX: {
      BRAINSTORMING: 1,
      STUDIOWORK: 3,
      PACKAGE: 5,
      FINALREVIEW: 6,
      SUBMISSION: 7
    }
  };

  packageVhr: number = 0
  studioTaskAction: boolean = false;
  allowEdit: boolean = true;
  studioTasks: WFTask[] = [];
  isAssessmentPending: boolean = false;
  previousPackages: Package[] = [];
  searchKeyPreviousPackages!: string;
  previousPackagesPagedListConfig!: PagedListConfig;
  totalProjectVHr = 0;
  totalProjectConsumedVHr = 0;
  // meetingAgendas: MeetingAgendaDto[];

  get currentContact() { return this.authService.currentUserStore?.contact; }
  get isPermissionShowAll() { return this.entityService.isPermissionSpecialShowAll; }
  get isPermissionEdit() { return this.entityService.isPermissionEdit; }
  get isPermissionDelete() { return this.entityService.isPermissionDelete; }
  get isPermissionSpecialEdit() { return this.entityService.isPermissionSpecialEdit; }
  get isPermissionSpecialDelete() { return this.entityService.isPermissionSpecialDelete; }
  get urls() { return this.f['spaceDiveUrls'] as FormArray; }
  contactOptions: Contact[] = [];
  contactFilter = [{ key: 'usersOnly', value: 'true' },{ key: 'appointmentStatusFlag', value: '0' }];
  filteredContacts$!: Observable<any[]>;
  selectedCoPilot!: Contact;
  vhrRate = 1;
  totalVHrConsumed = 0
  totalVHrAssessed = 0;
  totalVHrAssigned = 0;
  totalBrainStromVhr = 0;
  pendingVhr = 0;
  editableAttachmentConfig: any;
  meetingAgendaList: MeetingAgenda[] = [];
  isUpdatingForm = false;
  assignee: FormControl = new FormControl();
  dateFilter: DateFilterFn<Date | null> = (d: Date | null): boolean => {
    if (!d) {
      return true;
    }
    // console.log('date',d);
    // Prevent Saturday and Sunday from being selected.
    // const day = d;//.toDate();
    if (d && d.getDay() === 0) {
      return false;
      //&& day !== 6;
    }
    // if (day < (new Date())) {
    //   return false;
    // }
    return true;
  }

  get f(): any { return this.form.controls; }
  get isMobileView(): boolean { return this.utilityService.isMobileView; }
  get isPermissionMaster() {
    return this.authService.isInRole(this.permissions.MASTER);
  }

  get isCurrentUserPartner(): boolean {
    return this.currentEntity?.associations.filter(x => x.typeFlag == 0 && x.contactID == this.authService.currentUserStore?.contact.id).length != 0;
  }

  get isCurrentUserAssociate(): boolean {
    return this.currentEntity?.associations.filter(x => x.typeFlag == 1 && x.contactID == this.authService.currentUserStore?.contact.id).length != 0;
  }

  get partnerTypeFlag() {
    return this.config.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER;
  }

  get associateTypeFlag() {
    return this.config.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE;
  }


  constructor(
    private config: AppConfig,
    private dialog: MatDialog,
    private authService: AuthService,
    private formBuilder: FormBuilder,
    private permissions: AppPermissions,
    private utilityService: UtilityService,
    private wfTaskService: WFTaskApiService,
    private entityService: PackageApiService,
    private contactService: ContactApiService,
    private projectService: ProjectApiService,
    private timeEntryService: TimeEntryApiService,
    private projectScopeService: ProjectScopeApiService,
    private appSettingService: AppSettingMasterApiService,
    private meetingAgendaService: MeetingAgendaApiService,
    private attachmentService: PackageAttachmentApiService,
    private packageAssociateService: PackageAssociationApiService,
    private packageDeliverablesMasterService: PackageDeliverableMasterApiService,
  ) {
  }
  async ngOnInit() {
    await this.getPackageDeliverables();
    if (!this.form) {
      this.buildForm();
    }
    this.getContactOptions();
    await this.appSettingService.loadPresets();

    this.vhrRate = Number(this.appSettingService.presets
      .find(x => x.presetKey == this.config.PRESET_COMPANY_VHR_COST)?.presetValue);

      
  }

  refresh() {
    this.currentEntity = new Package();
    if (!this.form) {
      this.buildForm();
     
    }
    this.pendingVhr = 0;
    this.totalVHrAssessed = 0;
    this.totalVHrAssigned = 0;
    this.totalVHrConsumed = 0;
    this.studioTasks = [];
    if (this.entityID && this.entityID != -1) {
      this.getCurrent(this.entityID);
    }
    if (this.task) {
      if (this.task.timeEntries.length == 0) {
        this.getTaskTimeEntries(this.task.id);
      }
    }
   
  }

  getErrorMessage(control: AbstractControl) {
    return this.utilityService.getErrorMessage(control);
  }

  private async getCurrent(id: number) {
    const _entity = await firstValueFrom(this.entityService.getById(id));
    if (_entity) {
      this.currentEntity = _entity;
      this.packageVhr = this.currentEntity.vHrAssigned;
      this.getProjectProgress(this.currentEntity.projectID);
      this.getPreviousPackages(this.currentEntity.projectID);
      this.getMeetingAgenda(this.currentEntity.id);
      if (this.currentEntity.statusFlag === 1) {
        this.form.disable();
      } else {
        this.entityService.getConsumed(id).subscribe(data => {
          if (data) {
            this.currentEntity.vHrConsumed = data.vHrConsumed;
            this.currentEntity.vHrConsumedCost = data.vHrConsumedCost;
            this.currentEntity.vHrConsumedInclusive = data.vHrConsumedInclusive;
            this.currentEntity.vHrConsumedInclusiveCost = data.vHrConsumedInclusiveCost;
          }
        });
      }

      if (this.isPermissionSpecialEdit
        || (this.isTaskMode && this.task && this.task.stageIndex == 1)
        || this.authService.isCurrentUser(this.currentEntity.associations.filter(x => x.typeFlag === 0).map(x => x.contactID))) {

        this.allowEdit = true;
        this.f.contact.reset();
      } else {
        this.form.disable();
        this.allowEdit = false;
      }

      this.bindForm(this.currentEntity);
      this.patchSpaceDiveUrls();
      this.totalVHrAssigned = 0;
      this.getStudioTasks(this.currentEntity.id);
      this.getBrainStromeTime(id);
      this.editableAttachmentConfig = {
        apiUrl: this.config.apiPackageAttachments,
        autoUpload: true,
        typeFlag: 1,
        allowEdit: this.isPermissionEdit || (!this.isReadOnly && this.isTaskMode && this.task), //this.allowEdit,
        entityTitle: this.currentEntity.title,
        entityID: this.currentEntity.id
      };
    }
  }

  private async getMeetingAgenda(packageID: number) {
    const _agendas = await firstValueFrom(this.meetingAgendaService.get([
      { key: 'packageID', value: packageID.toString() },
      { key: 'IsVersion', value: 'false' },
      { key: 'isforwarded', value: 'false' }
    ]));
    if (_agendas) {
      this.meetingAgendaList = _agendas;
      // this.updatePackage();
    }
  }

  async onAgendaUpdate(agenda: MeetingAgenda) {
    if (agenda) {
      // console.log('agenda updated', agenda);
      await this.refresh();
      //// not needed as done while sending submissions
      //   this.currentEntity.meetingAgendaList = this.meetingAgendaList;
      //   this.entityService.update(this.currentEntity).subscribe(
      //     (data) =>
      //     {
      //       this.utilityService.showSwalToast(
      //         'Success!',
      //         'Save successful.',
      //       );
      //       this.update.emit(data);
      //       this.activity.refresh();
      //     });
    }
  }

  async updatePackage() {
    const _pendingAgendas = this.meetingAgendaList
      .filter(x => x.statusFlag === this.config.MEETING_AGENDA_STATUSFLAG_PENDING)
      .sort((a: any, b: any) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

    // console.log(_pendingAgendas);

    if (_pendingAgendas && _pendingAgendas.length !== 0 && _pendingAgendas[0].dueDate) {
      const pendingDueDate = new Date(_pendingAgendas[0].dueDate).getTime();
      const currentFinalDate = new Date(this.currentEntity.finalDate).getTime();

      if (currentFinalDate !== pendingDueDate) {
        // Set finalDate to pendingDueDate if it's not null, otherwise use the current date
        this.currentEntity.finalDate = _pendingAgendas[0].dueDate ? _pendingAgendas[0].dueDate : new Date();
        let _updated = await firstValueFrom(this.entityService.update(this.currentEntity));
        if (_updated) {
          // Update currentEntity with the updated values from the server
          this.currentEntity = Object.assign(this.currentEntity, _updated);
        }
      }
    }

  }

  private getTaskTimeEntries(wfTaskID: number) {
    this.timeEntryService.get([{ key: 'wftaskID', value: wfTaskID.toString() }])
      .subscribe(data => this.task.timeEntries = data);
  }

  buildForm() {
    this.form = this.formBuilder.group({
      startDate: ['', Validators.required],
      finalDate: ['', Validators.required],
      contact: [''],
      valueHours: ["", [Validators.required, Validators.min(0)]],
    
    });
    this.spaceDiveForm = this.formBuilder.group({
      spaceDiveUrls: this.formBuilder.array([
        this.createSpaceDiveUrlGroup(), // Initialize with one FormGroup
      ]),
    });
    this.f.startDate.disable();
    this.f.finalDate.disable();

    
    this.filteredContacts$ = this.f.contact.valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),
      map(value => value ? (typeof value === 'string' ? value : (value as Contact).name) : null),
      map(name => name ? this.filterContacts(name as string) : this.contactOptions.slice()),
    );

    this.filteredContacts$ = this.assignee.valueChanges.pipe(
      debounceTime(400),
      distinctUntilChanged(),
      map(value => value ? (typeof value === 'string' ? value : (value as Contact).name) : null),
      map(name => name ? this.filterContacts(name as string) : this.contactOptions.slice()),
    );

    this.touchForm();
    // this.addSpaceDiveUrl()
  }

  get spaceDiveUrls(): FormArray {
    return this.spaceDiveForm.get('spaceDiveUrls') as FormArray;
  }
  

  patchSpaceDiveUrls(): void {
    if (this.currentEntity.spaceDiveUrls && this.currentEntity.spaceDiveUrls.length !== 0) {
      const urlsToPatch = this.currentEntity.spaceDiveUrls;
      console.log('Patching URLs:', urlsToPatch); // Debugging log
  
      // Ensure the FormArray has enough FormGroups for the data
      while (this.spaceDiveUrls.length < urlsToPatch.length) {
        this.spaceDiveUrls.push(this.createSpaceDiveUrlGroup());
      }
  
      // Patch each FormGroup in the FormArray with corresponding data
      urlsToPatch.forEach((urlData, index) => {
        this.spaceDiveUrls.at(index).patchValue(urlData);
      });
  
      console.log('FormArray after patching:', this.spaceDiveUrls.value); // Debugging log
    } else {
      console.warn('No existing spaceDiveUrls in currentEntity. Patch operation skipped.');
    }
  }
  
  
  updateUrls(index: number): void {
    console.log('Updating URL at index:', index, this.spaceDiveUrls.at(index).value);
    this.updateSpaceDiveUrls(this.spaceDiveUrls.value); // Sync updates to the parent entity
  }

  createSpaceDiveUrlGroup(): FormGroup {
    return this.formBuilder.group({
      title: [null], // Title is optional
      url: ['', [Validators.required]], // URL is required
    });
  }
  
  addSpaceDiveUrl(): void {
    this.isUpdatingForm = true; // Prevent unnecessary valueChanges triggering
    this.spaceDiveUrls.push(this.createSpaceDiveUrlGroup());
    this.isUpdatingForm = false;
  }

  // urlValidator(control: string): null | { invalidUrl: boolean } {
  //   const urlRegex = new RegExp(
  //     '^https?:\\/\\/([\\w\\-]+\\.)+[\\w\\-]+(\\/[-\\w@:%._+~#=]*)?$',
  //     'i'
  //   );
  //   return urlRegex.test(control) ? null : { invalidUrl: true };
  // }
  
  removeSpaceDiveUrl(index: number) {
    this.spaceDiveUrls.removeAt(index);
    console.log('this.spaceDiveUrls.value',this.spaceDiveUrls.value);
    this.updateSpaceDiveUrls(this.spaceDiveUrls.value);
  }

  openSpaceDiveUrls(url: string): void {
    if (url) {
      console.log('Opening URL:', url);
  
      // Open the URL in a new window or tab
      window.open(url, '_blank');
    } else {
      console.error('URL is empty or invalid:', url);
    }
  }

  updateSpaceDiveUrls(urls: SpaceDiveUrl[]): void {
    if (!Array.isArray(this.currentEntity.spaceDiveUrls)) {
      this.currentEntity.spaceDiveUrls = [];
    }
  
    this.currentEntity.spaceDiveUrls = [...urls];
  
    firstValueFrom(this.entityService.update(this.currentEntity))
      .then(updatedEntity => {
        console.log('Entity updated successfully:', updatedEntity);
      })
      .catch(error => {
        console.error('Error updating entity:', error);
      });
  }
  
  touchForm() {
    //touch form inputs to show validation messages
    if (this.form) {
      Object.keys(this.form.controls).forEach(field => {
        // {1}
        const control = this.form.get(field); // {2}
        if (control)
          control.markAsTouched({ onlySelf: true }); // {3}
      });
    }
  }

  private bindForm(data: Package) {
    if (data) {
      this.f.startDate.setValue(data.startDate);
      this.f.finalDate.setValue(data.finalDate);
      this.f.valueHours.setValue(data.vHrAssigned)
    }
    // this.addSpaceDiveUrl()
  }

  onDeleteMeetingAgenda(item: MeetingAgenda) {
    const _messageText = "Remove Agenda: " + item.title + " from Package";
    this.utilityService.showConfirmationDialog(_messageText,
      () => {


        this.meetingAgendaList = this.meetingAgendaList.filter(obj => obj.uid !== item.uid);
        var _deletedAgenda = Object.assign({}, item);
        _deletedAgenda.entity = null;
        _deletedAgenda.entityID = null;
        _deletedAgenda.entityTitle = null;
        _deletedAgenda.packageID = null;
        this.meetingAgendaService.update(_deletedAgenda).subscribe(() => {

          this.currentEntity.meetingAgendas = this.meetingAgendaList;
          this.entityService.update(this.currentEntity).subscribe(
            (data) => {
              this.utilityService.showSwalToast(
                'Success!',
                'Save successful.',
              );
              this.update.emit(data);
              this.activity.refresh();
            });
        });
      }
    );
  }

  private getStudioTasks(packageID: number) {
    var _totalPendingVHr = 0;
    var _totalVHrAssigned = 0;
    var _totalVHrAssessed = 0;
    var _totalVHrConsumed = 0;
    var _totalSystemAssessed = 0;
    this.wfTaskService.get([
      { key: 'entity', value: 'package' },
      { key: 'entityID', value: packageID.toString() },
      { key: 'isPreAssignedTimeTask', value: 'true' },
    ]).subscribe(
      data => {
        this.studioTasks = data.filter(x => x.stageIndex == this.taskConstants.STAGEINDEX.STUDIOWORK);
        this.studioTasks.sort((a, b) => (new Date(b.startDate).getTime() - new Date(a.created).getTime()));
        this.studioTasks.forEach(x => {
          if (x.statusFlag == this.WFTASK_STATUSFLAG_COMPLETED && x.outcomeFlag != -1 && x.assessments.length == 0) {
            // console.log(x);
            _totalPendingVHr += x.vHrAssigned;
            this.pendingVhr = _totalPendingVHr;
            // console.log('PendingVHR', this.pendingVhr);
          }

          if (x.statusFlag == this.WFTASK_STATUSFLAG_COMPLETED && x.outcomeFlag != -1 && x.assessmentPoints > 0) {
            _totalVHrAssessed += x.vHrAssessed;
            this.totalVHrAssessed = _totalVHrAssessed;
            // console.log('Total VHR Assessed', this.totalVHrAssessed);
          }

          // if(x.statusFlag == this.WFTASK_STATUSFLAG_COMPLETED && x.outcomeFlag != -1 && x.modifiedBy.toLowerCase() == 'system'){
          //   console.log(x);
          //   _totalSystemAssessed += x.vHrAssessed;
          //   this.totalVHrAssessed += _totalSystemAssessed;
          // }

          // if ((x.statusFlag == this.WFTASK_STATUSFLAG_PENDING || x.statusFlag == this.WFTASK_STATUSFLAG_COMPLETED || x.statusFlag == this.WFTASK_STATUSFLAG_STARTED || x.statusFlag == this.WFTASK_STATUSFLAG_PAUSED) && x.outcomeFlag != -1) {
          if (x.outcomeFlag != -1) {
            _totalVHrAssigned += x.vHrAssigned;
            this.totalVHrAssigned = _totalVHrAssigned;
            // console.log('TotalVHRAssigned', _totalVHrAssigned);

            _totalVHrConsumed += x.vHrConsumed;
            this.totalVHrConsumed = _totalVHrConsumed;
            // console.log('TotalVhrConsumed', _totalVHrConsumed);
          }
        });
        this.assignStudioTaskAction();
        if (this.studioTasks.length != 0 && this.isTaskMode && this.task) {
          // const _unApprovedTasks = this.studioTasks
          //   .filter(x => x.assignerContactID == this.task.contactID
          //     && (x.statusFlag == 0 || (
          //       x.statusFlag == 1
          //       && x.outcomeFlag == 1
          //     )));
          //     console.log(_unApprovedTasks);
          // // this.isAssessmentPending = _unApprovedTasks.length !== 0;
          // if (_unApprovedTasks.length !== 0) {
          //   this.isAssessmentPending = false;
          // } else {
          //   this.isAssessmentPending = true;
          // }
          // console.log(this.isAssessmentPending)
          const _unApprovedTask = this.studioTasks.filter(x =>
            x.assignerContactID == this.task.contactID
            && (x.statusFlag == 0 || (
              x.statusFlag !== 1 && x.outcomeFlag !== 1
            )));
          this.isAssessmentPending = _unApprovedTask.length !== 0;
        } else {
          this.isAssessmentPending = false;
        }
      });
  }

  onUpdate(task?: WFTask) {
    // stop here if form is invalid
    if (this.form.invalid) {
      this.touchForm();
      this.utilityService.showSweetDialog('Invalid Form',
        'Please fill all required fields with valid data and try again.', 'error'
      );
      return;
    }


    this.currentEntity.startDate = this.utilityService.getLocalDate(this.f.startDate.value);
    this.currentEntity.finalDate = this.utilityService.getLocalDate(this.f.finalDate.value);
    this.currentEntity.vHrAssigned = this.f.valueHours.value;

    const _messageText =
      (this.isCreateMode ? "Create New " + this.nameOfEntity + ": " : "Update " + this.nameOfEntity + ": ") +
      this.currentEntity.title;
    this.utilityService.showConfirmationDialog(_messageText,
      () => {

        this.entityService.update(this.currentEntity).subscribe(
          (data) => {
            this.utilityService.showSwalToast(
              'Success!',
              'Save successful.',
            );
            this.update.emit(data);
            this.activity.refresh();
            this.refresh();
            if (task) {
              // console.log('Trigger Task complete');
              this.wfTaskService.completeTask(task);
            }
          }
        );

      }
    );

  }


  onDelete() {
    const _messageText = `Delete ${this.nameOfEntity}: ${this.currentEntity.title}`;
    this.utilityService.showConfirmationDialog(_messageText,
      () => {
        this.entityService.delete(this.currentEntity.id).subscribe(
          () => {
            this.utilityService.showSwalToast(
              'Success!',
              'Delete successful.',
            );

            this.delete.emit(this.currentEntity);
            this.entityService.refreshList();
          }
        );
      }
    );
  }

  assignStudioTask() {
    const dialogConfig = new MatDialogConfig();

    dialogConfig.disableClose = true; dialogConfig.autoFocus = true;
    dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    dialogConfig.data = {
      dialogTitle: 'Assign Studio Work',
      saveButtonText: 'Assign',
      saveButtonTooltip: 'Click to Assign Task',
      // isStudioAssign: true,
      assignee: this.assignee.value,
      entity: this.config.NAMEOF_ENTITY_PACKAGE,
      entityID: this.currentEntity.id,
      package: this.currentEntity,
      isCreateMode: true,
      task: new WFTask(this.config.NAMEOF_ENTITY_PACKAGE, this.currentEntity.id),
      deliverables: this.currentEntity.deliverables
    };

    const dialogRef = this.dialog.open(
      PackageStudioDialogComponent,
      dialogConfig
    );

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.studioTasks = this.studioTasks.filter(x => {
          return x.id !== result.id;
        });
        this.studioTasks.push(result);
      }
         this.getStudioTasks(this.currentEntity.id);
      // this.getStudioTasks(this.package.id);
    });
  }

  updateStudioTask(result: WFTask) {
    this.studioTasks = this.studioTasks.filter(x => {
      return x.id !== result.id;
    });
    this.studioTasks.push(result);
  }

  deleteStudioTask(event: any) {
    this.getStudioTasks(this.currentEntity.id);
  }

  sendSubmission() {
    this.taskComplete.emit(this.task);
  }

  onRemoveTimeEntry(obj: TimeEntryDto) {
    this.task.timeEntries = this.task.timeEntries.filter((x: any) => x.id !== obj.id);
    if (this.timeline) {
      this.timeline.refresh();
    }
  }

  private getContactOptions() {
    this.contactService.get(this.contactFilter, null, 'fullName').subscribe(data => {
      this.contactOptions = data;
    });
    console.log('studioContacts',this.studioContacts);
  }

  private getBrainStromeTime(entityID: number) {
    let filters = [
      { key: 'entity', value: 'Package' },
      { key: 'statusFlag', value: this.config.WFTASK_STATUSFLAG_COMPLETED.toString() },
      { key: 'entityID', value: entityID.toString() }
    ];
    this.wfTaskService.get(filters).subscribe(data => {
      this.totalBrainStromVhr = 0;
      data.filter(x => x.stageIndex == 1).forEach(y => {
        // console.log(y);
        if (y.vHrAssigned == 0) {
          this.totalVHrAssigned += y.vHrAssessed;
          this.totalBrainStromVhr += y.vHrAssessed;
        } else {
          this.totalVHrAssigned += y.vHrAssigned;
          this.totalBrainStromVhr += y.vHrAssigned;
        }
        // console.log(this.totalVHrAssigned);
      });
    });

  }

  //Excluding Package parters and associates
  get studioContacts() {
    return this.contactOptions
      .filter(x => !this.currentEntity.associations.filter(a => a.typeFlag == 0 || a.typeFlag == 1).map(c => c.contactID).includes(x.id))
  }

  filterContacts(property: string): any[] {
    return this.studioContacts
      .filter(option => option ? option.name.toLowerCase().includes(property.toLowerCase()) : false);
  }

  displayFnContact(option?: Contact): string {
    return option ? option.name : '';
  }

  getFilteredAssociates(associates: PackageAssociationDto[], typeFlag: number = 0) {
    if (associates) {
      return associates.filter(x => x.typeFlag == typeFlag);
    } return [];
  }

  onSelectContact(event: any, typeFlag: number = 0) {
    if (this.f.contact.value) {
      this.createAssociate(typeFlag);
    }
  }

  private createAssociate(typeFlag: number = 0) {

    if (!this.f.contact.value || !this.f.contact.value.id) {
      this.utilityService.showSwalToast('Invalid Contact!', 'Please select a person and try again!', 'error');
      return;
    }
    let _associate = new PackageAssociationDto();
    _associate.contactID = this.f.contact.value.id;
    _associate.packageID = this.currentEntity.id;
    _associate.typeFlag = typeFlag;
    if (typeFlag == 0) {
      _associate.title = 'Package Partner'
    } else if (typeFlag == 1) {
      _associate.title = 'Package Associate'
    }


    this.packageAssociateService.create(_associate).subscribe(
      data => {
        if (data) {
          this.currentEntity.associations.push(data);
        }
        this.utilityService.showSwalToast(
          "Success!",
          "Save Successfull.",
        );
        this.f.contact.reset();
        this.activity.refresh();
      }
    );


  }

  onDeleteAssociate(associate: PackageAssociationDto) {
    if (associate) {
      this.packageAssociateService.delete(associate.id).subscribe(
        () => {

          this.currentEntity.associations = this.currentEntity.associations.filter(x => x.id !== associate.id);
          this.utilityService.showSwalToast(
            "Success!",
            "Delete Successfull.",
          );
          this.activity.refresh();
        }
      );
    }
  }

  onSecondaryAction(index: number) {
    if (index === 0) {
      this.assignStudioTask();
    }
  }

  onDeleteAttachment(item: PackageAttachment) {
    if (item) {
      this.attachmentService.delete(item.id).subscribe(
        () => {
          this.currentEntity.attachments = this.currentEntity.attachments.filter(obj => obj.id !== item.id);
        }
      );
    }
  }

  onUploadAttachments(items: PackageAttachment[]) {
    if (items) {
      this.currentEntity.attachments = items;
    }
  }

  onSearchPreviousPackages() {
    this.getPreviousPackages(this.currentEntity.projectID);
  }

  private getPreviousPackages(projectID: number) {
    // let _filters = [{ key: 'projectID', value: projectID.toString() }, { key: 'statusFlag', value: '1' }];
    // this.entityService.getPages(_filters, this.searchKeyPreviousPackages, 'submissionDate desc').subscribe(data => {
    //   this.previousPackages = data;
    // })
    this.previousPackagesPagedListConfig = new PagedListConfig({
      searchKey: this.searchKeyPreviousPackages,
      sort: 'submissionDate desc',
      showAll: false,
      showAssigned: false,
      filters: [
        { key: 'projectid', value: projectID.toString() }, { key: 'statusFlag', value: '1' }
      ],
      route: '',
      pageSize: 20,
      groupBy: [], keyPropertyName: 'id'
    });
  }

  onPreviousPackageClick(item: any) {
    let _dialogData = new McvComponentDialogConfig();
    _dialogData.dialogTitle = item.title;
    _dialogData.entityID = item.id;
    _dialogData.entityTypeFlag = item.typeFlag;
    _dialogData.isCreateMode = false;
    _dialogData.isTaskMode = false;
    _dialogData.isReadOnly = true;
    _dialogData.task = undefined;
    const dialogRef = this.entityService.openDialog(PackageDetailDialogComponent, _dialogData);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        console.log('onClose', res);
      }
    });
  }

  private async getProjectProgress(projectID: number) {
    const _data = await firstValueFrom(this.projectService.getAnalysis('cashflow', [{ key: 'projectID', value: projectID.toString() }]));
    if (_data) {
      this.totalProjectVHr = 100;
      this.totalProjectConsumedVHr = (_data[0].totalFee / 5.0) > 0 ? Math.ceil(_data[0].vHrCost / (_data[0].totalFee / 5.0) * 100) : 100;
      this.projectFlowData = _data[0];
    }

    this.projectService.getAnalysis('cashflow', [{ key: 'projectID', value: projectID.toString() }]).subscribe((data: ProjectSummary[]) => {
      // this.totalProjectVHr = Math.ceil((data[0].totalFee / 5.0 )/ this.vhrRate);
      // this.totalProjectConsumedVHr = Math.ceil(data[0].vHrCost / this.vhrRate);
      this.totalProjectVHr = 100;//Math.ceil((data[0].totalFee / 5.0 )*100);
      this.totalProjectConsumedVHr = (data[0].totalFee / 5.0) > 0 ? Math.ceil(data[0].vHrCost / (data[0].totalFee / 5.0) * 100) : 100;
    })
  }

  assignStudioTaskAction() {
    if (this.pendingVhr > this.currentEntity.vHrAssigned) {
      this.studioTaskAction = false;
    } else {
      this.studioTaskAction = true;
    }
  }

  openDeliverablesItemDialog() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.panelClass = 'mcv-fullscreen-dialog';
    dialogConfig.minWidth = '100%';
    dialogConfig.data = {
      dialogTitle: `Package Sheet | ${this.currentEntity.title}`,
      entity: this.currentEntity,
    }
    const dialogRef = this.dialog.open(
      PackagePlaylistDialogComponent,
      dialogConfig
    );

    dialogRef.afterClosed().subscribe((data) => {
      if (data) {
        this.currentEntity.deliverables = data;
        // if (!data[0].hasOwnProperty('update'))
        // {
        // } else
        // {
        //   data.forEach(x =>
        //   {
        //     delete x.update;
        //     this.currentEntity.deliverables.push(x);
        //   })
        //   this.entityService.update(this.currentEntity).subscribe(x =>
        //   {
        //     console.log(x);
        //   });
        // }
      }
    });
  }


  qrCodeDownloadLink: SafeUrl = '';
  get submissionUrl() { return `${this.config.apiPackages}/submission/${this.currentEntity.uid}`; }
  onChangeURL(url: SafeUrl) {
    this.qrCodeDownloadLink = url;
  }
  get companyLogo() {
    return environment.logoUrl;
  }

  onClickAddAgenda() {
    const dialogRef = this.entityService.openDialog(PackageAgendaEditComponent, {
      dialogTitle: `Pick Agenda | ${this.currentEntity.title}`,
      package: this.currentEntity,
      selectedMeetingAgendas: this.meetingAgendaList
    });

    dialogRef.afterClosed().subscribe((data) => {
      if (data) {
        this.meetingAgendaList = data;
      }
    });
  }

  async getPackageDeliverables() {
    if (!this.packageDeliverablesMasterService.masterPlaylist || this.packageDeliverablesMasterService.masterPlaylist.length == 0)
      this.packageDeliverablesMasterService.masterPlaylist = await firstValueFrom(this.packageDeliverablesMasterService.get());
    this.packageDeliverablesMasterService.masterPlaylist.filter(x => x.code).sort((a, b) => a.code.localeCompare(b.code));
  }

  deliverableTaskLinked(packageDeliverable: PackageDeliverable) {
    if(!packageDeliverable || packageDeliverable.packageDeliverableTaskMaps == undefined || packageDeliverable.packageDeliverableTaskMaps.length == 0) return false;
    
    const _exist = packageDeliverable.packageDeliverableTaskMaps.find(x => x.wfTaskID == this.task.id);
    if (_exist) {
      return true;
    } return false;
  }

  onCancel() {
    this.cancel.emit();
  }

  onTaskCompleted(e: any) {
    this.wfTaskService.activeTask = undefined;
    this.taskComplete.emit(e);
  }

  openProgressDialog() {
    const dialogRef = this.projectScopeService.openDialog(ProjectsPartnerFeePhaseEditorDialogComponent, this.projectFlowData, true);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.projectFlowData = res;
      }
    });
  }

  // addSpaceDiveUrl(){
  //   const urlForm = this.formBuilder.group(
  //     {
  //       url: new FormControl<string>(''),
    
  //     }
  //   );
  //   this.urls.push(urlForm);
  // }

  onUpdatePackageVHr(){
    this.onUpdate();
  }
}

