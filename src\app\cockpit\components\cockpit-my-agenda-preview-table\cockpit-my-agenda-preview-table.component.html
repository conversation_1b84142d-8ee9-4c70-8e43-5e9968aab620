<div class="mat-dialog-header mcv-dialog-header">
    <!-- <h5><span class="font-focused">{{data.dialogTitle}}</span> | {{data?.dialogSubTitle}}</h5> -->
    <div>
        <h5 class="font-focused">My Agendas ({{totalRecordsCount}})</h5>
        <!-- <h6>{{data.dialogSubTitle}}</h6> -->
    </div>
    <button mat-icon-button aria-label="close" (click)="onClose(null)">
        <mat-icon>close</mat-icon>
    </button>
</div>
<mat-dialog-content class="mcv-dialog-content">
    <div class="table-container">
      <div class="filter-header" *ngIf="selectedMeetingTitle.length  > 0 || selectedTitle.length > 0 " (click)="toggleFilters()">
        <mat-icon>{{ showFilters ? 'expand_less' : 'expand_more' }}</mat-icon> <h6 class="font-focused mt-1">Filters:</h6> 
    </div>
    
    <div class="data-filter-row" *ngIf=" showFilters && (selectedMeetingTitle.length  > 0 || selectedTitle.length > 0 )">
        <h6 *ngIf="selectedMeetingTitle.length > 0">
            <b>Project Name:</b> {{selectedMeetingTitle.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('meetingTitle')">✖</span>
        </h6>
        <h6 *ngIf="selectedTitle.length > 0">
            <b>Title:</b> {{selectedTitle.join(', ')}}
            <span class="clear-icon" (click)="clearSelection('title')">✖</span>
        </h6>
      
      
        <h6  (click)="resetFilter()">
          <b> Clear All</b>
         
        </h6>
    </div>
    
        <table class="table">
            <thead class="table-header">
              <tr>
                <th>Sr No.</th>
                <th  mat-button [matMenuTriggerFor]="MeetingTitleMenu" [ngClass]="{'filter': selectedMeetingTitle.length > 0}">
              
                  <div class="myagenda-table-header">
                    <h6>Project Name</h6>
                    <mat-icon >filter_alt</mat-icon>
                    <mat-menu #MeetingTitleMenu="matMenu">
                        <div class="search-container p-1">
                            <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                <input
                                matInput
                                placeholder="Search"
                                [(ngModel)]="meetingTitleSearch"
                                (input)="filterDistinctMeetingTitle()"
                              />
                              <mat-icon class="clear-icon"
                              matSuffix
                              (click)="clearSearch($event)">close</mat-icon>
                            </mat-form-field>
                          </div>
                        <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                        <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('meetingTitle')">
                            {{ isAllSelected('meetingTitle') ? 'Deselect All' : 'Select All' }}
                        </button>
                      <mat-option class="menu-mat-option" *ngFor="let title of filteredMeetingTitle" (click)="$event.stopPropagation(); toggleSelection(title, 'meetingTitle')">
                        <mat-checkbox [checked]="selectedMeetingTitle.includes(title)">{{ title }}</mat-checkbox>
                      </mat-option>
                     
                    </mat-menu>
                  </div>
                </th>
                <th class="title-column" mat-button [matMenuTriggerFor]="titleMenu" [ngClass]="{'filter': selectedTitle.length > 0}">
              
                  <div class="myagenda-table-header">
                    <h6>Title</h6>
                    <mat-icon >filter_alt</mat-icon>
                    <mat-menu #titleMenu="matMenu">
                        <div class="search-container p-1">
                            <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                <input
                                matInput
                                placeholder="Search"
                                [(ngModel)]="titleSearch"
                                (input)="filterDistinctTitle()"
                              />
                              <mat-icon class="clear-icon"
                              matSuffix
                              (click)="clearSearch($event)">close</mat-icon>
                            </mat-form-field>
                          </div>
                        <!-- <button mat-menu-item (click)="resetFilter()">Reset</button> -->
                        <button mat-menu-item  (click)="$event.stopPropagation(); toggleSelectAll('title')">
                            {{ isAllSelected('title') ? 'Deselect All' : 'Select All' }}
                        </button>
                      <mat-option class="menu-mat-option" *ngFor="let title of filteredTitle" (click)="$event.stopPropagation(); toggleSelection(title, 'title')">
                        <mat-checkbox [checked]="selectedTitle.includes(title)">{{ title }}</mat-checkbox>
                      </mat-option>
                     
                    </mat-menu>
                  </div>
                </th>
                <th class="subtitle-column">
                  <div class="myagenda-table-header">
                    <h6>Subtitle</h6>
                  </div>
                </th>
                <th>
                  <div class="myagenda-table-header">
                    <h6>Status</h6>
                  </div>
                </th>
                <th>Reminders</th>
                <th >
                  <div class="myagenda-table-header" (click)="sortData('dueDate')">
                    <h6>Due Date</h6>
                    <!-- <mat-icon *ngIf="sortState['activeColumn'] === 'dueDate' && sortState['dueDate'] === 'newFirst'">import_export</mat-icon> -->
                    <mat-icon *ngIf="sortState['activeColumn'] === 'dueDate' && sortState['dueDate'] === 'newFirst'">south</mat-icon>
                    <mat-icon *ngIf="sortState['activeColumn'] === 'dueDate' && sortState['dueDate'] === 'oldFirst'">north</mat-icon>
                    <mat-icon *ngIf="sortState['activeColumn'] !== 'dueDate' || (!sortState['dueDate'])">import_export</mat-icon>
                  </div>
                </th>
                <th>Action By</th>
                <th>Modified By</th>
                <th class="comment-column">Info</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of dataList; let i = index">
                <td>{{ currentPage * pageSize + i + 1 }}</td>
                <td>{{ item.meetingTitle }}</td>
                <td class="title-column">{{ item.title }}</td>
                <td class="subtitle-column">{{ item.subtitle }}</td>
                <td>{{ item.status }}</td>
                <td>{{ item.reminderCount }}</td>
                <td>{{ item.dueDate | date: 'dd MMM yyyy' }}</td>
                <td>{{ item.actionBy }}</td>
                <td>{{ item.modifiedBy }}</td>
                <td class="comment-column">
             
                    <mat-icon [matTooltip]="item.comment">info</mat-icon>
                  
                 
                </td>
              </tr>
              <tr *ngIf="!dataList?.length">
                <td colspan="9" class="text-center">No records found.</td>
              </tr>
            </tbody>
          </table>

    </div>
      
      
      
      <div class="pagination-footer">
        <button mat-icon-button (click)="goToPreviousPage()" [disabled]="currentPage === 0">
       <mat-icon>chevron_left</mat-icon>
        </button>
      
        <span> {{ currentPage + 1 }} / {{ totalPages }}</span>
      
        <button mat-button (click)="goToNextPage()" [disabled]="(currentPage + 1) >= totalPages">
            <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
      
</mat-dialog-content>