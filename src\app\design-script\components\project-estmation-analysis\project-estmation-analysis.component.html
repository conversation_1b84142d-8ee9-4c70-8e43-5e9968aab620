<div class="estimation-analysis-wrapper">
  
    <div class="data-filter-row">
    <div class="filter-header"
        *ngIf="selectedProjects.length > 0 || selectedStatus.length > 0 ">
        <h6 class="font-focused ">Filters:</h6>
    </div>
    <h6 *ngIf="selectedProjects.length > 0">
        <b>Project:</b> {{selectedProjects.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('project')">✖</span>
    </h6>
    <h6 *ngIf="selectedStatus.length > 0">
        <b>Status:</b> {{selectedStatus.join(', ')}}
        <span class="clear-icon" (click)="clearSelection('status')">✖</span>
    </h6>
    <h6 (click)="resetFilter()"
        *ngIf="selectedProjects.length > 0 || selectedStatus.length > 0 ">
        <b> Clear All</b>
    </h6>



</div>
  <div class="header">
        <h6>Total no of rows: <span class="font-focused">{{dataList.length}}</span></h6>
    </div>
    <div class="estimation-table-wrapper">
        <table>
            <thead>
                <tr class="header-col">
                    <th mat-button [matMenuTriggerFor]="projectMenu"
                        [ngClass]="{'filter': selectedProjects.length > 0}">
                        <div class="analysis-table-header">
                            <h6>Project</h6>
                            <mat-icon>filter_alt</mat-icon>
                            <mat-menu #projectMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input matInput placeholder="Search" [(ngModel)]="projectKeySearch"
                                            (input)="filterDistinctProjects()" />
                                        <mat-icon class="clear-icon" matSuffix
                                            (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                </div>
                                <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('project')">
                                    {{ isAllSelected('project') ? 'Deselect All' : 'Select All' }}
                                </button>
                                <mat-option *ngFor="let project of filteredProjects"
                                    (click)="$event.stopPropagation(); toggleSelection(project, 'project')">
                                    <mat-checkbox [checked]="selectedProjects.includes(project)">{{ project
                                        }}</mat-checkbox>
                                </mat-option>
                            </mat-menu>
                        </div>

                    </th>
                    <!-- <th>ProjectID</th> -->
                    <th mat-button [matMenuTriggerFor]="statusMenu" [ngClass]="{'filter': selectedStatus.length > 0}">
                        <div class="analysis-table-header">
                            <h6>Status</h6>
                            <mat-icon>filter_alt</mat-icon>

                            <mat-menu #statusMenu="matMenu">
                                <div class="search-container p-1">
                                    <mat-form-field appearance="outline" (click)="$event.stopPropagation();">
                                        <input matInput placeholder="Search" [(ngModel)]="statusSearch"
                                            (input)="filterDistinctStatus()" />
                                        <mat-icon class="clear-icon" matSuffix
                                            (click)="clearSearch($event)">close</mat-icon>
                                    </mat-form-field>
                                </div>
                                <button mat-menu-item (click)="$event.stopPropagation();  toggleSelectAll('status')">
                                    {{ isAllSelected('status') ? 'Deselect All' : 'Select All' }}
                                </button>
                                <mat-option *ngFor="let status of filteredStatus"
                                    (click)="$event.stopPropagation(); toggleSelection(status, 'status')">
                                    <mat-checkbox [checked]="selectedStatus.includes(status)">{{ status
                                        }}</mat-checkbox>
                                </mat-option>
                            </mat-menu>
                        </div>
                    </th>
                    <!-- <th>Fee</th> -->
                    <th  [ngClass]="{'sort': sortState['activeColumn'] === 'spaceAmount'}">
                     <div class="analysis-table-header" (click)="sortData('spaceAmount')">
                        <h6>Space Estimate</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'spaceAmount' && sortState['spaceAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'spaceAmount' && sortState['spaceAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'spaceAmount' || !sortState['spaceAmount'] ">import_export</mat-icon>
                    </div>
                    </th>
                    <th  [ngClass]="{'sort': sortState['activeColumn'] === 'elementAmount'}">
                     <div class="analysis-table-header" (click)="sortData('elementAmount')">
                        <h6>Element Estimate</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'elementAmount' && sortState['elementAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'elementAmount' && sortState['elementAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'elementAmount' || !sortState['elementAmount'] ">import_export</mat-icon>
                    </div>
                    </th>
                    <th  [ngClass]="{'sort': sortState['activeColumn'] === 'boqAmount'}">
                     <div class="analysis-table-header" (click)="sortData('boqAmount')">
                        <h6>BOQ</h6>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'boqAmount' && sortState['boqAmount'] === 'newFirst'">south</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] === 'boqAmount' && sortState['boqAmount'] === 'oldFirst'">north</mat-icon>
                        <mat-icon
                            *ngIf="sortState['activeColumn'] !== 'boqAmount' || !sortState['boqAmount'] ">import_export</mat-icon>
                    </div>
                    </th>
                   
                </tr>
                <tr class="total">
                    <td>Total</td>
                    <td></td>
                    <!-- <td></td> -->
                    <!-- <td>{{total.fee | currency:'INR':'symbol':'1.0-0'}}</td> -->
                    <td>
                        {{total.spaceAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>
                        {{total.elementAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>
                        {{total.boqAmount |currency:'INR':'symbol':'1.0-0'}}
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of dataList">
                    <td>{{item.code}} - {{item.title}}</td>
                    <!-- <td>{{item.id}}</td> -->
                    <td>{{item.status}}</td>
                    <!-- <td>{{item.fee | currency:'INR':'symbol':'1.0-0'}}</td> -->
                    <td class="text-right">
                        {{item.spaceAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td class="text-right">

                        {{item.elementAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td class="text-right">
                        {{item.boqAmount |currency:'INR':'symbol':'1.0-0'}}
                    </td>

                </tr>
            </tbody>
        </table>
    </div>
</div>
<app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>

        <button mat-icon-button (click)="sidenav.toggleSidenav()" matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>



        <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            Export Excel
        </button>
    </div>
    <div class="nav-footer-mobile-actions">
        <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>


        <button mat-raised-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            Export Excel
        </button>
    </div>

    <div class="nav-filters">
        <div class="inline-list">


            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
                <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                    <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
            <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
                <mat-icon matPrefix>search</mat-icon>
                <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
            </div>
        </div>
    </div>
</app-footer>

<app-mcv-filter-sidenav #sidenav>
    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="companyFC" placeholder="Company">
                <mat-option *ngFor="let item of companyOptions" [value]="item">
                    {{item.title }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <div>
        <mat-form-field appearance="outline">
            <mat-select [formControl]="statusFC" multiple placeholder="Status">
                <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
</app-mcv-filter-sidenav>