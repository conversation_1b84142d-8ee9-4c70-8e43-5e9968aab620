<!-- <app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>
        <button mat-icon-button  (click)="refresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                    <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <button mat-icon-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">

            <img src="assets/icons/file-xlsx.png" alt="xlsx" />
        </button>
    </div>
</app-footer> -->

<div class="estimation-analysis-wrapper">
    <div class="header">
        <h6>Total no of rows: <span class="font-focused">{{dataList.length}}</span></h6>
    </div>
    <div class="estimation-table-wrapper">
        <table>
            <thead>
                <tr class="header-col">
                    <th>Project</th>
                    <!-- <th>ProjectID</th> -->
                    <th>Status</th>
                    <!-- <th>Fee</th> -->
                    <th>Space Estimate</th>
                    <th>Element Estimate</th>
                    <th>BOQ </th>
                </tr>
                <tr class="total">
                    <td>Total</td>
                    <td></td>
                    <!-- <td></td> -->
                    <!-- <td>{{total.fee | currency:'INR':'symbol':'1.0-0'}}</td> -->
                    <td>
                        {{total.spaceAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>
                        {{total.elementAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>
                        {{total.boqAmount |currency:'INR':'symbol':'1.0-0'}}
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of dataList">
                    <td>{{item.code}} - {{item.title}}</td>
                    <!-- <td>{{item.id}}</td> -->
                    <td>{{item.status}}</td>
                    <!-- <td>{{item.fee | currency:'INR':'symbol':'1.0-0'}}</td> -->
                    <td>
                        {{item.spaceAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>

                        {{item.elementAmount | currency:'INR':'symbol':'1.0-0'}}
                    </td>
                    <td>
                        {{item.boqAmount |currency:'INR':'symbol':'1.0-0'}}
                    </td>

                </tr>
            </tbody>
        </table>
    </div>
</div>
<app-footer>
    <div class="nav-footer-actions">
        <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
            <mat-icon matPrefix>search</mat-icon>
            <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
        </div>

        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                    <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <div>
            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <button mat-icon-button (click)="onRefresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>

        <button mat-icon-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <img src="assets/icons/file-xlsx.png" alt="xlsx" />
        </button>
    </div>
    <div class="nav-footer-mobile-actions">
        <button mat-icon-button appFilterToggle matTooltip="Filters" aria-label="Filters">
            <mat-icon>filter_list</mat-icon>
        </button>
        <button mat-icon-button (click)="onRefresh()" matTooltip="Refresh" aria-label="Refresh">
            <mat-icon>refresh</mat-icon>
        </button>

        <button mat-icon-button (click)="onExportExcel()" matTooltip="Export Excel" aria-label="export excel">
            <img src="assets/icons/file-xlsx.png" alt="xlsx" />
        </button>
    </div>

    <div class="nav-filters">
        <div class="inline-list">
            <div class="search-filter" [ngStyle]="{'height':'2.1rem'}">
                <mat-icon matPrefix>search</mat-icon>
                <input aria-label="Enter project name and select" [formControl]="searchFC" matInput />
            </div>

            <mat-form-field appearance="outline">
                <mat-select [formControl]="companyFC" placeholder="Company">
                    <mat-option *ngFor="let item of companyOptions" [value]="item">
                        {{item.title }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
                <mat-select [formControl]="partnerFC" multiple placeholder="Partner">
                    <mat-option *ngFor="let item of partnerOptions" [value]="item">{{item.name}}</mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
                <mat-select [formControl]="statusFC" multiple placeholder="Status">
                    <mat-option *ngFor="let item of statusOptions" [value]="item.value">{{item.title}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>
</app-footer>