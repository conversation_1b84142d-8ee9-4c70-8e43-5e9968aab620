<div class="timeline-header-control">
  <div class="timeline-legend">
   
  </div>

  <div class="timeline-header-week-control-wrapper">
    <button mat-icon-button aria-label="Previous" matTooltip="Previous" (click)="toggleWeek(-1)" *ngIf="selectedWeekMode != 'Custom Week'">
      <mat-icon>chevron_left</mat-icon>
    </button>
    <h5>
      <b>{{ rangeStartDate | date : "dd MMM y" }} -
        {{ rangeEndDate | date : "dd MMM y" }}</b>
    </h5>

    <button mat-icon-button aria-label="Next" matTooltip="Next" (click)="toggleWeek(1)" *ngIf="selectedWeekMode != 'Custom Week'">
      <mat-icon>chevron_right</mat-icon>
    </button>

  </div>

  <div class="timeline-header-view-control">
    <!-- <button class="viewMode m-1" mat-raised-button (click)="setWeekMode()">
      <h6 *ngIf="isFullWeek">Work Week</h6>
      <h6 *ngIf="!isFullWeek">Full Week</h6>

    </button> -->
   <!-- <PERSON><PERSON> and <PERSON><PERSON> for Day Selection -->
<button mat-button [matMenuTriggerFor]="dayMenu" *ngIf="!isMobileView && selectedWeekMode == 'Custom Week'">
  <div>{{ selectedDayMode }}</div>
  <mat-icon>arrow_drop_down</mat-icon>
</button>
<mat-menu #dayMenu="matMenu">
  <button mat-menu-item *ngFor="let item of weekDaysOption" (click)="setDayPerWeek(item)">
    {{ item }}
  </button>
</mat-menu>

<!-- Button and Menu for Week Selection -->
<button mat-button [matMenuTriggerFor]="weekMenu" *ngIf="!isMobileView">
  <div>{{ selectedWeekMode }}</div>
  <mat-icon>arrow_drop_down</mat-icon>
</button>
<mat-menu #weekMenu="matMenu">
  <button mat-menu-item *ngFor="let item of viewModeOptions" (click)="setWeekMode(item)">
    {{ item }}
  </button>
</mat-menu>

    <button class="viewMode m-1" mat-raised-button (click)="infoModeChange()">
      <h6 *ngIf="isDetailedView">Show Brief</h6>
      <h6 *ngIf="!isDetailedView">Show Detailed</h6>
    </button>
  </div>
  <button mat-raised-button color="primary" (click)="syncEventData()">
    Sync
  </button>
</div>

<div class="timeline-wrapper">
  <div class="team-wrapper">
    <ul>

      <li class="team-member-header" (scroll)="updateScroll('scrollOne')" #scrollOne>
        <div class="team-member-logo">
          <mat-icon>groups</mat-icon>
        </div>
        <div class="mhr-header">
          <h6>MHr</h6 >
        </div>
        <div class="week-header">
          <ng-container *ngFor="let day of visibleDays">
            <div class="weekday" #timeblock>
              <h6>{{ day | date: 'dd EEEE' }}</h6>
            </div>

          </ng-container>
        </div>
      </li>

      <div  class="virtual-viewport" 
        (scroll)="updateScroll('scrollTwo')" #scrollTwo>
        <div *ngIf="!isWeekChanged && isTaskValid(currentDate,currentDate)" class="current-time-line"
          [ngStyle]="{'left': calculateLineLeftPosition() + 'px'}"></div>
        <ng-container *ngFor="let person of filteredContacts;trackBy: trackById">

          <li  [style.height]="getTaskListHeight(person.id)">

            <div class="team-member">
              <img class="team-member-photo" [matTooltip]="person.name"
                src="{{person.photoUrl || 'assets/images/user.png' }}" class="team-member-photo" />
            </div>
            <div class="total-mhr">
            <h6  class="font-focused">{{calculateTotalAssignedMhr(person) }}</h6>  
            </div>

            <div class="team-member-week">
              <ng-container *ngFor="let day of visibleDays">
                <div class="day" [ngClass]="{'current-day': isCurrentDay(day)}" [style.width.px]="dayWidth"
                  (click)="openCreateDialog(person,day)">

                  <!-- tasks here -->
                  <div class="task-wrapper">
                    <ng-container *ngFor="let event of getPersonEvent(person.id,day);trackBy: trackById">

                      <div class="studio-work" [ngClass]="getTaskStatus(event.statusFlag, event)"
                        (click)="openTaskDialog(event, person, $event)" [style.width.px]="event.taskWidth"
                        [style.left]="event.taskLeftPosition" [style.top]="event.taskTopPosition" 
                        *ngIf="event && isTaskValid(event.startDate,event.endDate) && event.serviceType === 'Studio Work'">

                     

                        <div class="task-content d-flex align-items-center justify-content-between"
                          [ngClass]="{'flex-shrink-1': isDetailedView, 'flex-shrink-0': !isDetailedView}">
                          <h4 >{{ event.mHr  | number:'1.0-2'}}</h4>
                          <div class="d-flex flex-column"
                            [ngClass]="isDetailedView ? 'align-items-start flex-shrink-1' : 'align-items-start flex-shrink-0'">

                        
                            <small *ngIf="!isDetailedView && event.package">
                             {{event.package.projectCode}} - {{  event.package.code }}
                            </small>
                            <small *ngIf="!isDetailedView">
                              {{ event.createdBy}}
                            </small>
                            <small *ngIf="!isDetailedView">
                              {{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}
                            </small>

                            <div *ngIf="isDetailedView" class="d-flex flex-column">
                              <h6 class="task-extended-header-tag" style="font-size: 0.5rem;">
                               {{ event.subtitle || ' ' }} |
                              </h6>
                              <h6 class="task-extended-header-tag" style="font-size: 0.5rem;">
                                 {{ event.createdBy || ' ' }}  
                              </h6>
                              <h6 class="task-extended-header-tag" style="font-size: 0.5rem;">
                               {{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}
                              </h6>
                            </div>

                          </div>
                        
                        </div>
                        <div class="isextended d-flex align-items-center" *ngIf="event.isExtended">
                          <mat-icon>chevron_right</mat-icon>
                        </div>
                      

                      </div>

                      <div class="leave-task" 
                        *ngIf="(person.id == event.contactID) && event.serviceType == 'Leaves'" [style.top]="event.taskTopPosition" 
                        (click)="openLeaveDialog(event,person,$event)" [ngClass]="{
                          'leave': event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY,
                          'leave-break': event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED_BREAK || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY_BREAK,
                          'leave-halfday': event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED_HALFDAY || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY,
                          'leave-wfh': event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED_WFH || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY_WFH
                        } "
                        [style.width.px]="(event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY) ? dayWidth : event.taskWidth"
                        [style.left]="(event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED || event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY) ? 0 : event.taskLeftPosition">

                        <h3 class="task-header-tag" *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED">
                          LEAVE</h3>
                        <h3 class="task-header-tag" *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY">
                          E*LEAVE</h3>
                        <h3 class="task-header-tag"
                          *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED_HALFDAY">
                          HD</h3>
                        <h3 class="task-header-tag"
                          *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY">
                          E*HD</h3>
                        <h3 class="task-header-tag"
                          *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_APPROVED_BREAK">
                          BR</h3>
                        <h3 class="task-header-tag"
                          *ngIf="event.typeFlag === this.config.LEAVE_TYPEFLAG_EMERGENCY_BREAK">
                          E*BR</h3>




                      </div>

                      <div class="inspection"  [style.width.px]="event.taskWidth" [style.top]="event.taskTopPosition" 
                        *ngIf="event.serviceType == 'Inspection'" [style.left]="event.taskLeftPosition"
                        (click)="openInspectionDialog(event,person,$event)">
                        <h3>IN</h3>
                        <div class="task-content d-flex justify-content-between">
                          <div class="d-flex flex-column justify-content-right align-items-end">
                            <small>{{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}</small>
                          </div>

                        </div>

                      </div>


                      <div class="meeting" [style.top]="event.taskTopPosition" 
                        *ngIf="isTaskValid(event.startDate,event.endDate) && event.serviceType == 'Meeting' && (event.statusFlag == 0 || event.statusFlag == 1)"
                        [style.width.px]="event.taskWidth" [style.left]="event.taskLeftPosition"
                        (click)="openMeetingDialog(event,person,$event)">
                        <h3>M </h3>
                        <div class="task-content d-flex justify-content-between">
                          <div class="d-flex flex-column justify-content-right align-items-end">
                            <small>{{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}</small>
                          </div>

                        </div>

                      </div>



                      <div class="travel-time"  [style.top]="event.taskTopPosition" 
                        (click)="openTravelTimeDialog(event,person,$event)"
                        *ngIf="isTaskValid(event.startDate,event.endDate) && event.serviceType=='Travel Time'"
                        [style.width.px]="event.taskWidth" [style.left]="event.taskLeftPosition">
                        <h3 *ngIf="event.wfStageCode == this.config.TASK_STAGE_MEETING_CLOSE">M*TT</h3>
                        <h3 *ngIf="event.wfStageCode == this.config.TASK_STAGE_INSPECTION_CLOSE">I*TT</h3>
                        <div class="task-content d-flex justify-content-between">
                          <div class="d-flex flex-column justify-content-right align-items-end">

                            <small>{{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}</small>
                          </div>

                        </div>

                      </div>


                      <div class="prepare-report" [style.top]="event.taskTopPosition" 
                        *ngIf="isTaskValid(event.startDate,event.endDate) && event.serviceType == 'Prepare Minutes'"
                        [style.width.px]="event.taskWidth" [style.left]="event.taskLeftPosition"
                        (click)="openPrepareReportMinutesDialog(event,person,$event)">
                        <h3 *ngIf="event.wfStageCode == this.config.TASK_STAGE_INSPECTION_PREPARE_REPORT">I*PR</h3>
                        <h3 *ngIf="event.wfStageCode == this.config.TASK_STAGE_MEETING_PREPARE_MINUTES">M*PM</h3>
                        <div class="task-content d-flex justify-content-between">
                          <div class="d-flex flex-column justify-content-right align-items-end">

                            <small>{{ event.startDate | date:'HH:mm' }} - {{ event.endDate | date:'HH:mm' }}</small>
                          </div>

                        </div>

                      </div>
                    </ng-container>


                                   </div>
                  <div class="hour-wrapper">
                    <ng-container *ngFor="let day of numOfSegmentsArray,let i = index">
                      <div class="hour" [ngClass]="{ 'last-hour': i === 8 }"></div>
                    </ng-container>
                  </div>

                  <!-- <ng-container *ngFor="let hour of [].constructor(9); let i = index">
                      <div class="hour" [ngClass]="{ 'last-hour': i === 8 }"></div>
                    </ng-container> -->
                </div>

              </ng-container>
            </div>
          </li>
        </ng-container>
      </div>


    </ul>

  </div>
  <!-- <div class="hour-segments-wrapper" 
  [style.gridTemplateColumns]="'repeat(auto-fill, minmax(' + hourWidth + 'px, 1fr))'">
<div *ngFor="let _ of numOfSegmentsArray" class="hour-segment"></div>
</div> -->
</div>