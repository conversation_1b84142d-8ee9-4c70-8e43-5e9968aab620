@import 'variables';

.sort-dropdown {
    display: flex;
    align-items: center;

    mat-icon {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
    }
}

.sort-select-icon {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline;
}

.mat-mdc-menu-item,
.mat-mdc-icon-button,
.mat-mdc-menu-item,
.mdc-button {

    img {
        width: 24px;
        height: 24px;
        background-size: cover;
    }
}

.estimation-analysis-wrapper {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow: hidden auto;

    .header {
        position: sticky;
        position: -webkit-sticky;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        top: 0;
        z-index: 100;
        padding: 3px 15px;
        box-shadow: $box-shadow;

        .search-filter {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80%;
            border: 1px solid $gray-300;
            border-radius: 5px;
            height: 2.1rem;
        }

        .mat-mdc-form-field {
            width: 100%;
            margin-bottom: 0;
        }
    }


    .estimation-table-wrapper {
        // background-color: $gray-300;
        position: relative;
        width: 100%;
        overflow: auto;

        table {
            min-width: 100%;
            font-size: 0.8rem;
            cursor: pointer;

            thead {
                background-color: $gray-400;
                position: sticky;
                position: -webkit-sticky;
                top: 0;

                tr {
                    &.total {
                        background-color: $gray-600;
                        color: $white;
                    }

                    td,
                    th {
                        border-bottom: 1px solid $gray-400;
                        // white-space: nowrap;
                        border-right: 1px solid $gray-300;
                        padding: 0 0.3rem;

                        text-align: center;

                        &.align-right {
                            text-align: right;
                        }

                        &.text-pre-wrap {
                            white-space: pre-wrap;
                        }

                        &.collapsed {
                            padding: 0 !important;
                        }

                    }
                }

            }

            tbody {
                tr {
                    td {
                        border-bottom: 1px solid $gray-400;
                        white-space: nowrap;
                        border-right: 1px solid $gray-300;
                        padding: 0.3rem;

                        &.align-right {
                            text-align: right;
                        }

                        &.text-pre-wrap {
                            white-space: pre-wrap;
                        }
                    }


                }
            }
        }
    }
}