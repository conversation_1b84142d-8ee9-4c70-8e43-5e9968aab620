@import 'variables';
@import 'theme';

::ng-deep .mat-pseudo-checkbox {
    display: none !important;
}

::ng-deep .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) {
    background-color: transparent !important;
}

::ng-deep .mat-mdc-menu-panel {
  max-height: 30rem !important;
  overflow-y: auto;
}  
.sort-dropdown {
    display: flex;
    align-items: center;

    mat-icon {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
    }
}

.sort-select-icon {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline;
}

.mat-mdc-menu-item,
.mat-mdc-icon-button,
.mat-mdc-menu-item,
.mdc-button {

    img {
        width: 24px;
        height: 24px;
        background-size: cover;
    }
}

.estimation-analysis-wrapper {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow: hidden auto;

    .header {
        position: sticky;
        position: -webkit-sticky;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        top: 0;
        z-index: 100;
        padding: 3px 15px;
        box-shadow: $box-shadow;

        .search-filter {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80%;
            border: 1px solid $gray-300;
            border-radius: 5px;
            height: 2.1rem;
        }

        .mat-mdc-form-field {
            width: 100%;
            margin-bottom: 0;
        }
    }

.data-filter-row {
    // Dark background for contrast
    .filter-header {
        display: flex;
        align-items: start;
        justify-content: left;
        padding: 0rem 0.1rem;
    
    }
    padding: 0rem 0.4rem 0.3rem 0.4rem;

    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem; // Spacing between items

    h6 {
        padding: 0.2rem;
        margin: 0;
        font-size: 0.8rem;
        background: rgba(124, 124, 124, 0.1);
        border-radius: 3px;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        cursor: pointer;

        b {
            font-weight: 700;
            color: mat-color($mcv-primary,500);// Highlighting filter type
        }

        .clear-icon {
            cursor: pointer;
            font-size: 0.75rem;
            margin-left: 0.3rem;
            transition: opacity 0.3s;
            opacity: 0.6;

            &:hover {
                opacity: 1;
            }
        }
    }
}
    .estimation-table-wrapper {
        // background-color: $gray-300;
        position: relative;
        width: 100%;
        overflow: auto;

      table {
        max-width: 100%;
        width:100%;
        font-size: 0.8rem;
        cursor: pointer;
    
        thead {
            background-color: $gray-400;
            position: sticky;
            position: -webkit-sticky;
            top: 0;
    
            tr {
                &.total {
                    background-color: $gray-600;
                    color: $white;
                }
    
                td,
                th {
                    border-bottom: 1px solid $gray-400;
                    // white-space: nowrap;
                    border-right: 1px solid $gray-300;
                    padding: 0 0.3rem;
    
                    text-align: center;
    
                    &.align-right {
                        text-align: right;
                    }
    
                    &.text-pre-wrap {
                        white-space: pre-wrap;
                    }
    
                    &.collapsed {
                        padding: 0 !important;
                    }
                    &.filter , &.sort{
                        h6 {
                            color: white;
                        }
    
                        background-color: mat-color($mcv-primary, 500);
    
                        mat-icon {
                            color: white;
                        }
                    }
    
                }
            }
            .analysis-table-header {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.8rem;
    
                h6 {
                    font-weight: 600;
                    font-size: 0.8rem !important;
                }
    
                mat-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1rem;
    
                }
    
                .menu-mat-option {
                    mat-pseudo-checkbox {
                        display: none !important;
                    }
                }
              
            }
        }
    
        tbody {
            tr {
                td {
                    border-bottom: 1px solid $gray-400;
                    // white-space: nowrap;
                    border-right: 1px solid $gray-300;
                    padding: 0.3rem;
    
                    &.text-right {
                        text-align: right;
                    }
    
                    &.text-wrap {
                        white-space: pre-wrap;
                    }
    
                    &.collapsed {
                        padding: 0 !important;
                    }
    
                }
    
    
            }
        }
    }
    }
}