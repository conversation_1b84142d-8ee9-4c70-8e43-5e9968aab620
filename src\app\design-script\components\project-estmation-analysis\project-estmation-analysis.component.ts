import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Component, OnInit } from '@angular/core';

import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AuthService } from 'src/app/auth/services/auth.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';

import { AppConfig } from 'src/app/app.config';
import { Contact } from 'src/app/contact/models/contact';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { ApiFilter } from 'src/app/models/api-filters';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { NgFor, NgStyle, CurrencyPipe } from '@angular/common';
import { ProjectEstimation } from '../../models/project-estimation.model';
import { firstValueFrom } from 'rxjs';

@Component({
    selector: 'app-project-estmation-analysis',
    templateUrl: './project-estmation-analysis.component.html',
    styleUrls: ['./project-estmation-analysis.component.scss'],
    standalone: true,
    imports: [NgFor, FooterComponent, NgStyle, MatIconModule, MatFormFieldModule, MatInputModule, ReactiveFormsModule, MatSelectModule, MatOptionModule, MatButtonModule, MatTooltipModule, FilterToggleDirective, CurrencyPipe]
})
export class ProjectEstmationAnalysisComponent implements OnInit {

  searchKey!: string;

  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();

  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];
  dataList: ProjectEstimation[] = [];


  partnerFilter = [
    { key: 'usersOnly', value: 'true' },
    { key: 'projectPartnersOnly', value: 'true' }
  ];

  filters: ApiFilter[] = [
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_ONHOLD.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString() },
    { key: 'statusFlag', value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString() },
    { key: 'companyID', value: '1' },
  ];

  dataType: string = 'estimation';
  total: ProjectEstimation = new ProjectEstimation();

  constructor(
    private config: AppConfig,
    private authService: AuthService,
    private contactService: ContactApiService,
    private projectService: ProjectApiService,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService,
  ) { }

  ngOnInit() {

    this.refresh();
    this.getPartnerOptions();
    this.getStatusOptions();
    this.getCompanyOptions();

    this.statusFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "statusFlag");
            value.forEach((element: any) => {
              this.addFilter('statusFlag', element);
            });
            this.refresh();
          }
        }
      );

    this.searchFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    this.partnerFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "projectPartnerContactID");
            value.forEach((contact: Contact) => {
              if (contact && contact.id) {
                this.addFilter('projectPartnerContactID', contact.id);
              }
            });
            this.refresh();
          }
        }
      );

    this.companyFC.valueChanges
      .pipe(
        debounceTime(400),
        distinctUntilChanged()
      )
      .subscribe(
        (value) => {
          // console.log(value);
          if (value) {
            this.filters = this.filters.filter(i => i.key !== "companyID");
            if (value && value.id) {
              this.addFilter('companyID', value.id)
            }
            this.refresh();
          }
        }
      );
  }

  refresh() {
    this.dataList = [];
    this.total = new ProjectEstimation();


    this.projectService.getAnalysis(this.dataType, this.filters, this.searchKey).subscribe(val => {
      if (val) {
        this.dataList = val;
        this.dataList.forEach(x => {
          this.total.fee += x.fee;
          this.total.spaceAmount += x.spaceAmount;
          this.total.elementAmount += x.elementAmount;
          this.total.boqAmount += x.boqAmount;
        });
      }
    });
  }

  private addFilter(key: string, value: any) {
    const _filter = this.filters.find(obj => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  private getCompanyOptions() {
    this.companyAccountService.get().subscribe((data) => {
      this.companyOptions = data;
      this.companyFC.setValue(this.companyOptions.find(x => x.id == 1), { emitEvent: false });
    }
    );
  }

  private getPartnerOptions() {
    this.contactService.get(this.partnerFilter).subscribe((data) => {
      this.partnerOptions = data;
      if (!this.authService.isRoleMaster && this.partnerOptions.find(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))) {
        this.partnerFC.setValue(this.partnerOptions.filter(x => x.id == (this.authService.currentUserStore ? this.authService.currentUserStore.contact.id : 0))
        );
      }
    }
    );
  }

  protected async getStatusOptions() {
     this.statusOptions = await firstValueFrom(this.statusMasterService.get([{ key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT}]));
        this.statusFC.setValue(this.filters.map(x => x.value), { emitEvent: false })
      
  }
  onRefresh() {
    this.refresh();
  }
  onExportExcel() {
    this.projectService.exportAnalysisExcel(this.dataType, this.filters, this.searchKey, this.sortFC.value);
  }
}

