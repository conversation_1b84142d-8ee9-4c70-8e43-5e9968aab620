import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Component, OnInit } from '@angular/core';

import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { AuthService } from 'src/app/auth/services/auth.service';
import { StatusMasterService } from 'src/app/services/status-master.service';
import { ContactApiService } from 'src/app/contact/services/contact-api.service';
import { ProjectApiService } from 'src/app/project/services/project-api.service';
import { CompanyAccountApiService } from 'src/app/services/company-account-api.service';

import { AppConfig } from 'src/app/app.config';
import { Contact } from 'src/app/contact/models/contact';
import { CompanyAccount } from 'src/app/models/company-account';
import { StatusMaster } from 'src/app/models/status-master-dto';
import { ApiFilter } from 'src/app/models/api-filters';
import { FilterToggleDirective } from '../../../shared/directives/filter-toggle.directive';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '../../../shared/components/footer/footer.component';
import { CommonModule } from '@angular/common';
import { ProjectEstimation } from '../../models/project-estimation.model';
import { firstValueFrom } from 'rxjs';
import { McvFilterSidenavComponent } from 'src/app/mcv-core/components/mcv-filter-sidenav/mcv-filter-sidenav.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-project-estmation-analysis',
  templateUrl: './project-estmation-analysis.component.html',
  styleUrls: ['./project-estmation-analysis.component.scss'],
  standalone: true,
  imports: [
    FooterComponent,

    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatTooltipModule,
    FilterToggleDirective,
    McvFilterSidenavComponent,
    MatMenuModule,
    MatOptionModule,
    CommonModule,
    FormsModule,
    MatCheckboxModule,
  ],
})
export class ProjectEstmationAnalysisComponent implements OnInit {
  searchKey!: string;

  companyFC = new FormControl();
  partnerFC = new FormControl();
  statusFC = new FormControl();
  sortFC = new FormControl();
  searchFC = new FormControl();

  companyOptions: CompanyAccount[] = [];
  partnerOptions: Contact[] = [];
  statusOptions: StatusMaster[] = [];
  dataList: ProjectEstimation[] = [];
  originalDataList: any[] = [];
  selectedProjects: string[] = [];
  filteredProjects: string[] = [];
  distinctProjects: string[] = [];
  projectKeySearch: string = '';
  selectedStatus: string[] = [];
  filteredStatus: string[] = [];
  distinctStatus: string[] = [];
  statusSearch: string = '';
  isSorted: boolean = false;
  sortState: {
    activeColumn: string; // Active column should be a string
    [key: string]: '' | 'newFirst' | 'oldFirst' | string; // Allow any other property to be of type '' | 'newFirst' | 'oldFirst'
  } = {
    activeColumn: '',
    fee: '',
    spaceAmount: '',
    elementAmount: '',
    boqAmount: '',
  };
  partnerFilter = [
    { key: 'usersOnly', value: 'true' },
    { key: 'projectPartnersOnly', value: 'true' },
  ];

  filters: ApiFilter[] = [
    {
      key: 'statusFlag',
      value: this.config.PROJECT_STATUS_FLAG_INPROGRESS.toString(),
    },
    {
      key: 'statusFlag',
      value: this.config.PROJECT_STATUS_FLAG_ONHOLD.toString(),
    },
    {
      key: 'statusFlag',
      value: this.config.PROJECT_STATUS_FLAG_PREPROPOSAL.toString(),
    },
    {
      key: 'statusFlag',
      value: this.config.PROJECT_STATUS_FLAG_LOCKED.toString(),
    },
    { key: 'companyID', value: '1' },
  ];

  dataType: string = 'estimation';
  total: ProjectEstimation = new ProjectEstimation();

  constructor(
    private config: AppConfig,
    private authService: AuthService,
    private contactService: ContactApiService,
    private projectService: ProjectApiService,
    private statusMasterService: StatusMasterService,
    private companyAccountService: CompanyAccountApiService
  ) {}

  ngOnInit() {
    this.refresh();
    this.getPartnerOptions();
    this.getStatusOptions();
    this.getCompanyOptions();

    this.statusFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter((i) => i.key !== 'statusFlag');
          value.forEach((element: any) => {
            this.addFilter('statusFlag', element);
          });
          this.refresh();
        }
      });

    this.searchFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        this.searchKey = value;
        this.refresh();
      });

    this.partnerFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.filters = this.filters.filter(
            (i) => i.key !== 'projectPartnerContactID'
          );
          value.forEach((contact: Contact) => {
            if (contact && contact.id) {
              this.addFilter('projectPartnerContactID', contact.id);
            }
          });
          this.refresh();
        }
      });

    this.companyFC.valueChanges
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((value) => {
        // console.log(value);
        if (value) {
          this.filters = this.filters.filter((i) => i.key !== 'companyID');
          if (value && value.id) {
            this.addFilter('companyID', value.id);
          }
          this.refresh();
        }
      });
  }

  async refresh() {
    this.dataList = [];
    this.total = new ProjectEstimation();

    this.dataList = await firstValueFrom(
      this.projectService.getAnalysis(
        this.dataType,
        this.filters,
        this.searchKey
      )
    );

    this.dataList = this.dataList.sort((a, b) => {
      const codeA = a.code?.toLowerCase() || '';
      const codeB = b.code?.toLowerCase() || '';
      return codeA.localeCompare(codeB);
    });

    this.originalDataList = [...this.dataList];
    this.extractDistinctValues();
    this.getTotal();
  }
  private getTotal() {
    this.total = new ProjectEstimation();
    this.dataList.forEach((x) => {
      this.total.fee += x.fee;
      this.total.spaceAmount += x.spaceAmount;
      this.total.elementAmount += x.elementAmount;
      this.total.boqAmount += x.boqAmount;
    });
  }

  private extractDistinctValues() {
    this.distinctProjects = [...new Set(this.dataList.map((x) => x.title))];
    this.filteredProjects = [...this.distinctProjects];
    this.distinctStatus = [...new Set(this.dataList.map((x) => x.status))];
    this.filteredStatus = [...this.distinctStatus];
  }

  private addFilter(key: string, value: any) {
    const _filter = this.filters.find((obj) => {
      return obj.key === key && obj.value === value;
    });
    if (!_filter) {
      this.filters.push({ key: key, value: value.toString() });
    }
  }

  private getCompanyOptions() {
    this.companyAccountService.get().subscribe((data) => {
      this.companyOptions = data;
      this.companyFC.setValue(
        this.companyOptions.find((x) => x.id == 1),
        { emitEvent: false }
      );
    });
  }

  private getPartnerOptions() {
    this.contactService.get(this.partnerFilter).subscribe((data) => {
      this.partnerOptions = data;
      if (
        !this.authService.isRoleMaster &&
        this.partnerOptions.find(
          (x) =>
            x.id ==
            (this.authService.currentUserStore
              ? this.authService.currentUserStore.contact.id
              : 0)
        )
      ) {
        this.partnerFC.setValue(
          this.partnerOptions.filter(
            (x) =>
              x.id ==
              (this.authService.currentUserStore
                ? this.authService.currentUserStore.contact.id
                : 0)
          )
        );
      }
    });
  }

  protected async getStatusOptions() {
    this.statusOptions = await firstValueFrom(
      this.statusMasterService.get([
        { key: 'entity', value: this.config.NAMEOF_ENTITY_PROJECT },
      ])
    );
    this.statusFC.setValue(
      this.filters.map((x) => x.value),
      { emitEvent: false }
    );
  }
  onRefresh() {
    this.refresh();
  }
  onExportExcel() {
    this.projectService.exportAnalysisExcel(
      this.dataType,
      this.filters,
      this.searchKey,
      this.sortFC.value
    );
  }

  filterDistinctProjects() {
    const searchLower = this.projectKeySearch.toLowerCase();
    this.filteredProjects = this.distinctProjects.filter((project) =>
      project.toLowerCase().includes(searchLower)
    );
  }

  filterDistinctStatus() {
    const searchLower = this.statusSearch.toLowerCase();
    this.filteredStatus = this.distinctStatus.filter((status) =>
      status.toLowerCase().includes(searchLower)
    );
  }

  clearSearch(event: any) {
    this.projectKeySearch = '';
    this.statusSearch = '';
    this.filterDistinctProjects();
    this.filterDistinctStatus();
  }

  toggleSelectAll(filterType: 'project' | 'status') {
    const distinctValues = this.getDistinctValues(filterType);
    switch (filterType) {
      case 'project':
        this.selectedProjects = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
      case 'status':
        this.selectedStatus = this.isAllSelected(filterType)
          ? []
          : [...distinctValues];
        break;
    }
    this.applyFilters();
  }

  toggleSelection(value: string, type: string) {
    if (type === 'project') {
      this.selectedProjects.includes(value)
        ? this.selectedProjects.splice(this.selectedProjects.indexOf(value), 1)
        : this.selectedProjects.push(value);
    } else if (type === 'status') {
      this.selectedStatus.includes(value)
        ? this.selectedStatus.splice(this.selectedStatus.indexOf(value), 1)
        : this.selectedStatus.push(value);
    }
    this.applyFilters();
  }

  isAllSelected(type: string): boolean {
    if (type === 'project') {
      return (
        this.selectedProjects.length === this.distinctProjects.length &&
        this.selectedProjects.length > 0
      );
    } else if (type === 'status') {
      return (
        this.selectedStatus.length === this.distinctStatus.length &&
        this.selectedStatus.length > 0
      );
    }
    return false;
  }

  getDistinctValues(filterType: 'project' | 'status'): string[] {
    switch (filterType) {
      case 'project':
        return this.distinctProjects;
      case 'status':
        return this.distinctStatus;
      default:
        return [];
    }
  }

  applyFilters() {
    let filteredList = [...this.originalDataList]; // Start with a copy of the original data
    if (this.searchKey) {
      const searchLower = this.searchKey.toLowerCase();

      filteredList = filteredList.filter(
        (item) =>
          item.title?.toLowerCase().includes(searchLower) ||
          '' ||
          item.status?.toLowerCase().includes(searchLower) ||
          ''
      );
    }

    if (this.selectedProjects.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedProjects.includes(item.title)
      );
    }
    if (this.selectedStatus.length > 0) {
      filteredList = filteredList.filter((item) =>
        this.selectedStatus.includes(item.status)
      );
    }

    this.dataList = filteredList;
    this.getTotal();
  }
  clearSelection(type: string) {
    if (type === 'project') {
      this.selectedProjects = [];
    } else if (type === 'status') {
      this.selectedStatus = [];
    }
    this.applyFilters(); // Apply filters after clearing the selection
  }

  resetFilter() {
    this.selectedProjects = [];
    this.selectedStatus = [];
    this.dataList = [...this.originalDataList]; // Restore full list
    this.getTotal();
  }

  sortData(column: keyof ProjectEstimation | '') {
    if (column === '') {
      // Reset to default (original data) but apply the filters again
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProjects.length === 0 ||
            this.selectedProjects.includes(item.title)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status))
        );
      });

      // Reset the sort state to default
      this.sortState = {
        activeColumn: '',
        totalAmount: '',
        totalReceived: '',
        currentAmount: '',
        currentRecieved: '',
        score: '',
        igstAmount: '',
        cgstAmount: '',
        sgstAmount: '',
        billDate: '',
        paymentDate: '',
        workCompletion: '',
      };
      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if no sorting is selected
    }

    // If the clicked column is already the active column, cycle through the three states
    const currentSort = this.sortState[column];

    if (currentSort === 'newFirst') {
      // If it's 'newFirst', change to 'oldFirst'
      this.sortState[column] = 'oldFirst';
    } else if (currentSort === 'oldFirst') {
      // If it's 'oldFirst', reset to default (no sorting)
      this.sortState[column] = '';
      this.sortState.activeColumn = '';
      const dataToFilter = this.originalDataList;

      // Apply filters to the original data list
      this.dataList = dataToFilter.filter((item: any) => {
        return (
          (this.selectedProjects.length === 0 ||
            this.selectedProjects.includes(item.title)) &&
          (this.selectedStatus.length === 0 ||
            this.selectedStatus.includes(item.status))
        );
      });

      this.isSorted = false; // Set isSorted to false when sorting is reset
      return; // Exit the function if reset is selected
    } else {
      // If no sorting is applied, set it to 'newFirst' (ascending order)
      this.sortState[column] = 'newFirst';
    }

    // Set the active column
    this.sortState['activeColumn'] = column;

    // Reset other columns' sort state to '' (no sort)
    for (let key in this.sortState) {
      if (key !== column && key !== 'activeColumn') {
        this.sortState[key] = ''; // Reset other columns' sort state to no sort
      }
    }

    // Sorting logic: Compare dates for the active column
    const sortedData = [...this.dataList].sort((a, b) => {
      const dateA = new Date(a[column] as string);
      const dateB = new Date(b[column] as string);

      if (this.sortState[column] === 'newFirst') {
        return dateA < dateB ? -1 : dateA > dateB ? 1 : 0; // Ascending order
      } else if (this.sortState[column] === 'oldFirst') {
        return dateA > dateB ? -1 : dateA < dateB ? 1 : 0; // Descending order
      }
      return 0; // If no sorting, return unchanged order
    });

    // Update the dataList with the sorted data
    this.dataList = sortedData;
    this.isSorted = true; // Set isSorted to true when sorting is applied
  }
}
