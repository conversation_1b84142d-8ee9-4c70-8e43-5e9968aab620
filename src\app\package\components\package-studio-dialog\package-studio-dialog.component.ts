import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { PackageStudioEditComponent } from '../package-studio-edit/package-studio-edit.component';
import { PackageStudioAssignComponent } from '../package-studio-assign/package-studio-assign.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { NgIf } from '@angular/common';

@Component({
    selector: 'app-package-studio-dialog',
    templateUrl: './package-studio-dialog.component.html',
    styleUrls: ['./package-studio-dialog.component.scss'],
    standalone: true,
    imports: [NgIf, MatButtonModule, MatTooltipModule, MatIconModule, MatDialogModule, PackageStudioAssignComponent, PackageStudioEditComponent]
})
export class PackageStudioDialogComponent implements OnInit {

  data: any;
  isMobileView: boolean = false;
  @ViewChild('editActionButton') childEditComponent!: PackageStudioEditComponent;
  @ViewChild('createActionButton') childCreateComponent!: PackageStudioAssignComponent;

  constructor(@Inject(MAT_DIALOG_DATA) dialogData: any,
    private dialogRef: MatDialogRef<PackageStudioDialogComponent>,
    private utility: UtilityService,
  ) {
    this.data = dialogData;
  }

  ngOnInit() {
    this.isMobileView = this.utility.isMobileView;
  }

  onClose(result: any) {
    this.dialogRef.close(result);
  }

  onSave() {
    if (this.data.isCreateMode) {
      this.childCreateComponent.onSubmit();
    } else {
      this.childEditComponent.onSubmit();
    }
  }

  onDelete() {
    this.childEditComponent.onDelete();
  }
}

